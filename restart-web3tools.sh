#!/bin/bash

# Web3Tools应用重启脚本 (Ubuntu服务器版本)
# 功能：杀掉现有进程并重新启动应用
# 适用于：Ubuntu/Debian Linux服务器环境

# 设置脚本选项
set -eu  # 遇到错误立即退出，未定义变量报错

# 配置变量
JAR_NAME="Web3Tools-0.0.1-SNAPSHOT.jar"
JAR_PATH="./${JAR_NAME}"
LOG_FILE="./web3tools.log"
PID_FILE="./web3tools.pid"

# 检查是否为root用户运行
#if [ "$(id -u)" -eq 0 ]; then
#    echo "警告: 不建议以root用户运行此应用"
#    printf "是否继续? (y/N): "
#    read REPLY
#    case "$REPLY" in
#        [Yy]|[Yy][Ee][Ss]) ;;
#        *) exit 1 ;;
#    esac
#fi

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查Java环境
check_java_environment() {
    if ! command -v java >/dev/null 2>&1; then
        log_error "Java未安装或不在PATH中"
        log_info "请安装Java: sudo apt update && sudo apt install openjdk-21-jdk"
        exit 1
    fi

    java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    log_info "检测到Java版本: $java_version"

    # 检查Java版本是否符合要求（Spring Boot 3.x需要Java 17+）
    major_version=$(echo "$java_version" | cut -d'.' -f1)
    if [ "$major_version" -lt 17 ] 2>/dev/null; then
        log_warning "Java版本可能过低，建议使用Java 17或更高版本"
    fi
}

# 检查jar文件是否存在
check_jar_exists() {
    if [ ! -f "$JAR_PATH" ]; then
        log_error "JAR文件不存在: $JAR_PATH"
        log_info "请先运行 'mvn clean package' 来构建应用"
        exit 1
    fi

    # 检查jar文件权限
    if [ ! -r "$JAR_PATH" ]; then
        log_error "JAR文件不可读: $JAR_PATH"
        log_info "请检查文件权限: ls -la $JAR_PATH"
        exit 1
    fi

    jar_size=$(du -h "$JAR_PATH" | cut -f1)
    log_info "JAR文件大小: $jar_size"
}

# 查找并杀掉现有进程
kill_existing_process() {
    log_info "正在查找 $JAR_NAME 进程..."

    # 使用ps -ef查找进程ID
    PIDS=$(ps -ef | grep "$JAR_NAME" | grep -v grep | awk '{print $2}')

    if [ -z "$PIDS" ]; then
        log_info "没有找到运行中的 $JAR_NAME 进程"
        return 0
    fi

    log_warning "找到以下进程ID: $PIDS"

    # 显示找到的进程详细信息
    log_info "进程详细信息:"
    ps -ef | grep "$JAR_NAME" | grep -v grep

    # 逐个杀掉进程
    for PID in $PIDS; do
        log_info "正在停止进程 $PID..."

        # 首先尝试优雅停止 (SIGTERM)
        kill -TERM $PID 2>/dev/null

        # 等待进程优雅停止
        count=0
        while kill -0 $PID 2>/dev/null && [ $count -lt 30 ]; do
            sleep 1
            count=$((count + 1))
            printf "."
        done
        echo

        # 如果进程仍在运行，强制杀掉
        if kill -0 $PID 2>/dev/null; then
            log_warning "进程 $PID 未能优雅停止，强制杀掉..."
            kill -KILL $PID 2>/dev/null
            sleep 2
        fi

        # 验证进程是否已停止
        if ! kill -0 $PID 2>/dev/null; then
            log_success "进程 $PID 已成功停止"
        else
            log_error "无法停止进程 $PID"
            exit 1
        fi
    done

    # 清理PID文件
    if [ -f "$PID_FILE" ]; then
        rm -f "$PID_FILE"
        log_info "已清理PID文件"
    fi
}

# 创建日志目录
create_log_directory() {
    log_dir=$(dirname "$LOG_FILE")
    if [ ! -d "$log_dir" ]; then
        mkdir -p "$log_dir"
        log_info "已创建日志目录: $log_dir"
    fi
}

# 启动应用
start_application() {
    log_info "正在启动 $JAR_NAME..."

    # 创建日志目录
    create_log_directory

    # 后台启动应用，使用nohup确保进程在终端关闭后继续运行
    nohup java -jar "$JAR_PATH" > "$LOG_FILE" 2>&1 &
    new_pid=$!

    # 保存PID
    echo $new_pid > "$PID_FILE"

    log_success "应用已启动，PID: $new_pid"
    log_info "日志文件: $LOG_FILE"
    log_info "PID文件: $PID_FILE"

    # 等待几秒钟检查应用是否成功启动
    sleep 5

    # 验证进程是否仍在运行
    if kill -0 $new_pid 2>/dev/null; then
        log_success "应用启动成功！"
        log_info "可以使用以下命令查看日志:"
        log_info "  tail -f $LOG_FILE"
        log_info "可以使用以下命令查看实时日志:"
        log_info "  tail -f $LOG_FILE | grep -E '(ERROR|WARN|INFO)'"
        log_info "可以使用以下命令检查应用状态:"
        log_info "  ps -ef | grep $JAR_NAME"

        # 显示应用启动后的初始日志
        if [ -f "$LOG_FILE" ] && [ -s "$LOG_FILE" ]; then
            log_info "应用启动日志预览:"
            echo "----------------------------------------"
            tail -n 10 "$LOG_FILE"
            echo "----------------------------------------"
        fi
    else
        log_error "应用启动失败，请检查日志文件: $LOG_FILE"
        if [ -f "$LOG_FILE" ]; then
            log_error "错误日志:"
            echo "----------------------------------------"
            tail -n 20 "$LOG_FILE"
            echo "----------------------------------------"
        fi
        exit 1
    fi
}

# 显示应用状态
show_status() {
    log_info "应用状态检查:"
    echo "========================================"

    # 使用ps -ef查找进程
    running_processes=$(ps -ef | grep "$JAR_NAME" | grep -v grep)

    if [ -n "$running_processes" ]; then
        log_success "找到运行中的 $JAR_NAME 进程:"
        echo "$running_processes"

        # 显示进程资源使用情况
        pids=$(echo "$running_processes" | awk '{print $2}')
        for pid in $pids; do
            if [ -f "/proc/$pid/status" ]; then
                mem_usage=$(ps -o pid,ppid,user,%cpu,%mem,vsz,rss,comm -p $pid 2>/dev/null)
                if [ -n "$mem_usage" ]; then
                    echo "进程 $pid 资源使用情况:"
                    echo "$mem_usage"
                fi
            fi
        done
    else
        log_warning "没有找到运行中的 $JAR_NAME 进程"
    fi

    echo "========================================"

    # 检查PID文件
    if [ -f "$PID_FILE" ]; then
        local saved_pid=$(cat "$PID_FILE" 2>/dev/null)
        if [ -n "$saved_pid" ]; then
            if kill -0 $saved_pid 2>/dev/null; then
                log_success "PID文件中的进程正在运行，PID: $saved_pid"

                # 显示进程启动时间
                local start_time=$(ps -o lstart= -p $saved_pid 2>/dev/null)
                if [ -n "$start_time" ]; then
                    log_info "进程启动时间: $start_time"
                fi
            else
                log_warning "PID文件存在但进程未运行，清理PID文件"
                rm -f "$PID_FILE"
            fi
        else
            log_warning "PID文件为空，清理PID文件"
            rm -f "$PID_FILE"
        fi
    else
        log_info "未找到PID文件"
    fi

    # 检查日志文件
    if [ -f "$LOG_FILE" ]; then
        local log_size=$(du -h "$LOG_FILE" 2>/dev/null | cut -f1)
        log_info "日志文件大小: $log_size"
        log_info "最近的日志条目:"
        echo "----------------------------------------"
        tail -n 5 "$LOG_FILE" 2>/dev/null || echo "无法读取日志文件"
        echo "----------------------------------------"
    else
        log_info "日志文件不存在: $LOG_FILE"
    fi
}

# 显示系统信息
show_system_info() {
    log_info "系统信息:"
    echo "========================================"

    # 操作系统信息
    if [ -f /etc/os-release ]; then
        local os_info=$(grep PRETTY_NAME /etc/os-release | cut -d'"' -f2)
        log_info "操作系统: $os_info"
    fi

    # 系统负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}')
    log_info "系统负载:$load_avg"

    # 内存使用情况
    local mem_info=$(free -h | grep Mem)
    log_info "内存使用: $mem_info"

    # 磁盘使用情况
    local disk_usage=$(df -h . | tail -1)
    log_info "当前目录磁盘使用: $disk_usage"

    # Java进程数量
    local java_count=$(ps -ef | grep java | grep -v grep | wc -l)
    log_info "当前Java进程数量: $java_count"

    # 网络端口占用情况（常见的Spring Boot端口）
    if command -v netstat &> /dev/null; then
        local port_8080=$(netstat -tlnp 2>/dev/null | grep :8080 || echo "端口8080未被占用")
        log_info "端口8080状态: $port_8080"
    elif command -v ss &> /dev/null; then
        local port_8080=$(ss -tlnp | grep :8080 || echo "端口8080未被占用")
        log_info "端口8080状态: $port_8080"
    fi

    echo "========================================"
}

# 主函数
main() {
    log_info "开始重启 Web3Tools 应用..."
    echo "========================================"

    # 检查Java环境
    check_java_environment

    # 检查jar文件
    check_jar_exists

    # 杀掉现有进程
    kill_existing_process

    # 启动应用
    start_application

    # 显示状态
    show_status

    echo "========================================"
    log_success "重启完成！"
}

# 处理命令行参数
ACTION="${1:-restart}"
case "$ACTION" in
    "start")
        log_info "仅启动应用..."
        check_java_environment
        check_jar_exists
        start_application
        show_status
        ;;
    "stop")
        log_info "仅停止应用..."
        kill_existing_process
        show_status
        ;;
    "status")
        show_status
        ;;
    "info")
        show_system_info
        show_status
        ;;
    "restart"|"")
        main
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status|info}"
        echo "  start   - 仅启动应用"
        echo "  stop    - 仅停止应用"
        echo "  restart - 重启应用（默认）"
        echo "  status  - 显示应用状态"
        echo "  info    - 显示系统信息和应用状态"
        echo ""
        echo "Ubuntu服务器版本 - 使用ps -ef查找进程"
        exit 1
        ;;
esac
