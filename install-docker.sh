#!/bin/bash
set -e

echo "=== 卸载旧版本 Docker（如果存在） ==="
sudo apt-get remove -y docker docker-engine docker.io containerd runc || true

echo "=== 更新软件包索引 ==="
sudo apt-get update -y

echo "=== 安装依赖工具 ==="
sudo apt-get install -y ca-certificates curl gnupg lsb-release

echo "=== 添加 Docker 官方 GPG key ==="
sudo mkdir -p /etc/apt/keyrings
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | \
sudo gpg --dearmor -o /etc/apt/keyrings/docker.gpg

echo "=== 添加 Docker 软件源 ==="
echo \
  "deb [arch=$(dpkg --print-architecture) \
  signed-by=/etc/apt/keyrings/docker.gpg] \
  https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | \
  sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

echo "=== 更新软件包索引并安装 Docker ==="
sudo apt-get update -y
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

echo "=== 启动 Docker 服务 ==="
sudo systemctl enable docker
sudo systemctl start docker

echo "=== 添加当前用户到 docker 组（免 sudo 使用 docker） ==="
sudo groupadd docker || true
sudo usermod -aG docker $USER

echo "=== Docker 版本信息 ==="
docker --version

echo "=== 测试运行 hello-world 容器 ==="
docker run --rm hello-world

echo "=== 安装完成！请重新登录终端以生效 ==="

