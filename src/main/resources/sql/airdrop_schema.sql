-- 空投项目管理系统数据库表结构

-- 项目表
CREATE TABLE IF NOT EXISTS projects (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    chain VARCHAR(50) NOT NULL COMMENT '区块链类型',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    status ENUM('active', 'inactive', 'paused') DEFAULT 'active' COMMENT '项目状态',
    wallet_count INT DEFAULT 0 COMMENT '钱包数量',
    total_value DECIMAL(20,8) DEFAULT 0 COMMENT '总价值',
    created_by VARCHAR(42) NOT NULL COMMENT '创建者钱包地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_created_by (created_by),
    INDEX idx_chain (chain),
    INDEX idx_status (status),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='空投项目表';

-- 钱包表
CREATE TABLE IF NOT EXISTS wallets (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_id BIGINT NOT NULL COMMENT '项目ID',
    address VARCHAR(42) NOT NULL COMMENT '钱包地址',
    name VARCHAR(100) COMMENT '钱包名称',
    notes TEXT COMMENT '备注信息',
    balance DECIMAL(20,8) DEFAULT 0 COMMENT '余额',
    last_activity DATE COMMENT '最后活动日期',
    added_by VARCHAR(42) NOT NULL COMMENT '添加者钱包地址',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_wallet (project_id, address),
    INDEX idx_address (address),
    INDEX idx_added_by (added_by),
    INDEX idx_project_id (project_id),
    INDEX idx_balance (balance),
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钱包表';

-- 表格列配置表
CREATE TABLE IF NOT EXISTS table_columns (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    project_id BIGINT COMMENT '项目ID，NULL表示默认配置',
    column_key VARCHAR(50) NOT NULL COMMENT '列键名',
    label VARCHAR(100) NOT NULL COMMENT '列标签',
    width VARCHAR(20) COMMENT '列宽度',
    editable BOOLEAN DEFAULT FALSE COMMENT '是否可编辑',
    visible BOOLEAN DEFAULT TRUE COMMENT '是否可见',
    sort_order INT DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_column (COALESCE(project_id, 0), column_key),
    INDEX idx_project_id (project_id),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表格列配置表';

-- 插入默认的表格列配置
INSERT IGNORE INTO table_columns (project_id, column_key, label, width, editable, visible, sort_order) VALUES
(NULL, 'name', '钱包名称', '150px', TRUE, TRUE, 1),
(NULL, 'address', '地址', '200px', FALSE, TRUE, 2),
(NULL, 'balance', '余额', '100px', FALSE, TRUE, 3),
(NULL, 'lastActivity', '最后活动', '120px', FALSE, TRUE, 4),
(NULL, 'notes', '备注', '200px', TRUE, TRUE, 5),
(NULL, 'addedBy', '添加者', '150px', FALSE, TRUE, 6),
(NULL, 'createdAt', '创建时间', '150px', FALSE, TRUE, 7);
