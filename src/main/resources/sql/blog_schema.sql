-- 博客管理系统数据库表结构

-- 创建文章表
CREATE TABLE IF NOT EXISTS posts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '文章ID',
    title VARCHAR(500) NOT NULL COMMENT '文章标题',
    content TEXT NOT NULL COMMENT '文章内容(Markdown格式)',
    category VARCHAR(100) NOT NULL COMMENT '文章分类',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_category (category),
    INDEX idx_created_at (created_at),
    INDEX idx_title (title(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='博客文章表';

-- 创建分类表
CREATE TABLE IF NOT EXISTS categories (
    id VARCHAR(50) PRIMARY KEY COMMENT '分类ID',
    name VARCHAR(100) NOT NULL UNIQUE COMMENT '分类名称',
    icon VARCHAR(10) NOT NULL COMMENT '分类图标',
    color VARCHAR(20) NOT NULL COMMENT '分类颜色',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_name (name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='博客分类表';

-- 插入默认分类数据
INSERT IGNORE INTO categories (id, name, icon, color) VALUES
('default', '默认分类', '📝', '#6b7280'),
('tech', '技术分享', '💻', '#3b82f6'),
('life', '生活随笔', '🌟', '#10b981'),
('thinking', '思考感悟', '💭', '#8b5cf6'),
('reading', '读书笔记', '📚', '#f59e0b');

-- 插入示例文章数据
INSERT IGNORE INTO posts (title, content, category) VALUES
('欢迎使用博客管理系统', 
'# 欢迎使用博客管理系统

这是一个基于Spring Boot开发的博客管理系统，支持以下功能：

## 主要功能

- ✅ 文章的增删改查
- ✅ 分类管理
- ✅ Markdown文件上传
- ✅ 文章搜索和筛选
- ✅ 统计信息展示

## 技术栈

- **后端**: Spring Boot 3.x + JPA + MySQL
- **前端**: 支持任何前端框架
- **数据库**: MySQL 8.0+
- **缓存**: Redis

## 使用说明

1. 通过API接口管理文章和分类
2. 支持Markdown格式的文章内容
3. 提供完整的RESTful API
4. 支持文件上传功能

开始您的博客之旅吧！', 
'default'),

('Spring Boot 开发指南', 
'# Spring Boot 开发指南

Spring Boot是一个基于Spring框架的快速开发框架。

## 核心特性

- **自动配置**: 根据项目依赖自动配置Spring应用
- **起步依赖**: 简化Maven/Gradle配置
- **内嵌服务器**: 无需部署WAR文件
- **生产就绪**: 提供监控、健康检查等功能

## 快速开始

```java
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

这就是一个最简单的Spring Boot应用！', 
'tech'),

('我的编程学习心得', 
'# 我的编程学习心得

学习编程已经有几年时间了，想分享一些个人的学习心得。

## 学习方法

1. **理论与实践结合**: 不要只看书，要动手写代码
2. **项目驱动学习**: 通过实际项目来学习新技术
3. **持续学习**: 技术更新很快，要保持学习的习惯

## 推荐资源

- 官方文档是最好的学习资料
- GitHub上的开源项目
- 技术博客和社区

## 总结

编程是一个需要持续学习和实践的过程，保持好奇心和耐心很重要。', 
'thinking');
