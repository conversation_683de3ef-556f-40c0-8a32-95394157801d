-- 钱包管理系统数据库表结构

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    address VARCHAR(64) NOT NULL UNIQUE COMMENT '钱包地址',
    wallet_type VARCHAR(20) NOT NULL COMMENT '钱包类型(metamask/unisat/phantom)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_address (address),
    INDEX idx_wallet_type (wallet_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户钱包表
CREATE TABLE IF NOT EXISTS user_wallets (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    chain VARCHAR(20) NOT NULL COMMENT '区块链类型(EVM/BTC/SOLANA)',
    address VARCHAR(64) NOT NULL COMMENT '钱包地址',
    name VARCHAR(100) COMMENT '钱包名称',
    notes TEXT COMMENT '备注信息',
    balance DECIMAL(20,8) DEFAULT 0 COMMENT '主币余额',
    usd_value DECIMAL(20,2) DEFAULT 0 COMMENT 'USD价值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unique_user_wallet (user_id, address),
    INDEX idx_user_id (user_id),
    INDEX idx_address (address),
    INDEX idx_chain (chain),
    INDEX idx_balance (balance),
    CONSTRAINT fk_user_wallet_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户钱包表';

-- 钱包代币表
CREATE TABLE IF NOT EXISTS wallet_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    wallet_id BIGINT NOT NULL COMMENT '钱包ID',
    symbol VARCHAR(20) NOT NULL COMMENT '代币符号',
    name VARCHAR(100) NOT NULL COMMENT '代币名称',
    balance DECIMAL(20,8) DEFAULT 0 COMMENT '代币余额',
    usd_value DECIMAL(20,2) DEFAULT 0 COMMENT 'USD价值',
    price DECIMAL(20,2) DEFAULT 0 COMMENT '代币价格',
    change_24h DECIMAL(10,2) DEFAULT 0 COMMENT '24小时涨跌幅',
    contract_address VARCHAR(64) COMMENT '合约地址',
    decimals INT COMMENT '代币精度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_wallet_id (wallet_id),
    INDEX idx_symbol (symbol),
    INDEX idx_balance (balance),
    CONSTRAINT fk_wallet_token_wallet FOREIGN KEY (wallet_id) REFERENCES user_wallets(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钱包代币表';

-- 钱包历史数据表
CREATE TABLE IF NOT EXISTS wallet_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    date DATE NOT NULL COMMENT '日期',
    type VARCHAR(10) NOT NULL COMMENT '类型(DAILY/MONTHLY)',
    total_value DECIMAL(20,2) DEFAULT 0 COMMENT '总价值',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_date (date),
    INDEX idx_type (type),
    CONSTRAINT fk_wallet_history_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='钱包历史数据表';

-- 插入一些示例数据（可选）
-- INSERT INTO users (address, wallet_type) VALUES 
-- ('******************************************', 'metamask'),
-- ('******************************************', 'unisat'),
-- ('DYw8jCTfwHNRJhhmFcbXvVDTqWMEVFBX6ZKUmG5CNSKK', 'phantom');

-- 创建视图：用户资产总览
CREATE OR REPLACE VIEW user_asset_overview AS
SELECT 
    u.id as user_id,
    u.address as user_address,
    u.wallet_type,
    COUNT(uw.id) as total_wallets,
    COALESCE(SUM(uw.usd_value), 0) as total_value,
    COUNT(CASE WHEN uw.chain = 'EVM' THEN 1 END) as evm_wallets,
    COUNT(CASE WHEN uw.chain = 'BTC' THEN 1 END) as btc_wallets,
    COUNT(CASE WHEN uw.chain = 'SOLANA' THEN 1 END) as solana_wallets,
    COALESCE(SUM(CASE WHEN uw.chain = 'EVM' THEN uw.usd_value ELSE 0 END), 0) as evm_value,
    COALESCE(SUM(CASE WHEN uw.chain = 'BTC' THEN uw.usd_value ELSE 0 END), 0) as btc_value,
    COALESCE(SUM(CASE WHEN uw.chain = 'SOLANA' THEN uw.usd_value ELSE 0 END), 0) as solana_value
FROM users u
LEFT JOIN user_wallets uw ON u.id = uw.user_id
GROUP BY u.id, u.address, u.wallet_type;

-- 创建存储过程：清理过期历史数据
DELIMITER //
CREATE PROCEDURE CleanupWalletHistory(IN days_to_keep INT)
BEGIN
    DECLARE cutoff_date DATE;
    SET cutoff_date = DATE_SUB(CURDATE(), INTERVAL days_to_keep DAY);
    
    DELETE FROM wallet_history 
    WHERE date < cutoff_date AND type = 'DAILY';
    
    SELECT ROW_COUNT() as deleted_rows;
END //
DELIMITER ;

-- 创建触发器：自动更新钱包的updated_at字段
DELIMITER //
CREATE TRIGGER update_wallet_timestamp 
BEFORE UPDATE ON user_wallets
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END //
DELIMITER ;

-- 创建触发器：自动更新代币的updated_at字段
DELIMITER //
CREATE TRIGGER update_token_timestamp 
BEFORE UPDATE ON wallet_tokens
FOR EACH ROW
BEGIN
    SET NEW.updated_at = CURRENT_TIMESTAMP;
END //
DELIMITER ;
