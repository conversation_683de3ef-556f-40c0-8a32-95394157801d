INSERT INTO web3tools.keywords (id, word, type) VALUES (1, '新协议', 'BASE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (2, '新东西', 'BASE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (5, '新项目', 'BASE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (6, '新的币', 'BASE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (8, '打新', 'BASE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (9, '币', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (10, 'btc', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (11, 'eth', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (12, 'sol', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (13, '链', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (14, '成本', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (15, '交易', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (16, '$', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (17, 'http://', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (18, 'https://', 'REQUIRED');
INSERT INTO web3tools.keywords (id, word, type) VALUES (19, '广告', 'DELETE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (20, '营销', 'DELETE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (21, '垃圾', 'DELETE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (22, '灰产', 'DELETE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (23, '灰*产', 'DELETE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (24, '赚*钱', 'DELETE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (25, '网*赚', 'DELETE');
INSERT INTO web3tools.keywords (id, word, type) VALUES (26, '量子', 'DELETE');

-- 添加智能合约测试数据
INSERT INTO web3tools.smart_contracts (
    contract_address, deploy_transaction_hash, deploy_block_number, deploy_timestamp, 
    deployer_address, contract_name, contract_symbol, is_token, is_filtered,
    interaction_count_24h, unique_users_24h, total_value_24h, score, 
    created_at, updated_at
) VALUES 
(
    '0x1234567890abcdef1234567890abcdef12345678', 
    '0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890ab',
    18500000, 1640995200, '0x9876543210fedcba9876543210fedcba98765432',
    'TestToken', 'TT', true, false, 150, 45, 125000.50, 85.75,
    NOW(), NOW()
),
(
    '0x2345678901bcdef12345678901bcdef123456789', 
    '0xbcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abc',
    18500100, 1640995300, '0x8765432109fedcba8765432109fedcba87654321',
    'DeFiProtocol', 'DFP', true, false, 300, 120, 450000.75, 92.25,
    NOW(), NOW()
),
(
    '0x3456789012cdef123456789012cdef1234567890', 
    '0xcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcd',
    18500200, 1640995400, '0x7654321098fedcba7654321098fedcba76543210',
    'NFTCollection', 'NFT', false, false, 80, 25, 75000.25, 45.50,
    NOW(), NOW()
),
(
    '0x4567890123def1234567890123def12345678901', 
    '0xdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcde',
    18500300, 1640995500, '******************************************',
    'HighScoreContract', 'HSC', true, false, 500, 200, 850000.00, 95.00,
    NOW(), NOW()
),
(
    '******************************************', 
    '0xef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef',
    18500400, 1640995600, '******************************************',
    'FilteredContract', 'FC', false, true, 0, 0, 0.00, 0.00,
    NOW(), NOW()
);

-- SmartWalletTracker 数据库表结构

-- 聪明钱包表
CREATE TABLE IF NOT EXISTS smart_wallet (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    address VARCHAR(100) NOT NULL UNIQUE COMMENT '钱包地址',
    nickname VARCHAR(100) COMMENT '昵称',
    win_rate DOUBLE COMMENT '胜率百分比',
    total_profit BIGINT COMMENT '总盈利 (lamports)',
    profit_percent DOUBLE COMMENT '收益率百分比',
    avg_hold_time VARCHAR(50) COMMENT '平均持仓时间',
    recent_trades INT COMMENT '近期交易次数',
    followers INT DEFAULT 0 COMMENT '跟单人数',
    tags TEXT COMMENT '标签，逗号分隔',
    status ENUM('ACTIVE', 'OFFLINE') DEFAULT 'ACTIVE' COMMENT '状态',
    last_active DATETIME COMMENT '最后活跃时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 统计信息
    total_trades INT DEFAULT 0 COMMENT '总交易次数',
    profitable_trades INT DEFAULT 0 COMMENT '盈利交易次数',
    total_volume BIGINT DEFAULT 0 COMMENT '总交易量',
    avg_profit BIGINT DEFAULT 0 COMMENT '平均盈利',
    max_profit BIGINT DEFAULT 0 COMMENT '最大单笔盈利',
    max_loss BIGINT DEFAULT 0 COMMENT '最大单笔亏损',
    best_win_streak INT DEFAULT 0 COMMENT '最佳连胜次数',
    current_streak INT DEFAULT 0 COMMENT '当前连胜/连败次数',
    
    INDEX idx_address (address),
    INDEX idx_status (status),
    INDEX idx_profit_percent (profit_percent),
    INDEX idx_win_rate (win_rate)
) COMMENT='聪明钱包表';

-- 聪明钱包交易记录表
CREATE TABLE IF NOT EXISTS smart_wallet_transaction (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    smart_wallet_id BIGINT NOT NULL COMMENT '聪明钱包ID',
    token VARCHAR(20) NOT NULL COMMENT '代币符号',
    token_address VARCHAR(100) NOT NULL COMMENT '代币地址',
    action ENUM('BUY', 'SELL') NOT NULL COMMENT '交易动作',
    amount BIGINT COMMENT '代币数量',
    price DOUBLE COMMENT '代币价格 (SOL)',
    cost BIGINT COMMENT '成本 (lamports)',
    current_value BIGINT COMMENT '当前价值 (lamports)',
    profit BIGINT COMMENT '盈亏 (lamports)',
    profit_percent DOUBLE COMMENT '盈亏百分比',
    timestamp DATETIME NOT NULL COMMENT '交易时间',
    status ENUM('HOLDING', 'COMPLETED') DEFAULT 'HOLDING' COMMENT '状态',
    signature VARCHAR(200) NOT NULL COMMENT '交易签名',
    block_time BIGINT COMMENT '区块时间戳',
    slot BIGINT COMMENT 'Solana slot',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (smart_wallet_id) REFERENCES smart_wallet(id),
    INDEX idx_smart_wallet_id (smart_wallet_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_token_address (token_address),
    INDEX idx_status (status),
    INDEX idx_action (action)
) COMMENT='聪明钱包交易记录表';

-- 跟单任务表
CREATE TABLE IF NOT EXISTS follow_task (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id VARCHAR(50) NOT NULL UNIQUE COMMENT '任务ID',
    user_wallet VARCHAR(100) NOT NULL COMMENT '用户钱包地址',
    smart_wallet_id BIGINT NOT NULL COMMENT '要跟单的聪明钱包ID',
    wallet_private_key TEXT NOT NULL COMMENT '加密的钱包私钥',
    status ENUM('ACTIVE', 'PAUSED', 'STOPPED') DEFAULT 'ACTIVE' COMMENT '任务状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 配置信息
    copy_mode ENUM('RATIO', 'FIXED') DEFAULT 'RATIO' COMMENT '跟单模式',
    copy_ratio INT COMMENT '跟单比例 (%)',
    fixed_amount BIGINT COMMENT '固定金额 (lamports)',
    daily_limit BIGINT COMMENT '每日跟单上限 (lamports)',
    total_limit BIGINT COMMENT '总跟单上限 (lamports)',
    min_amount BIGINT COMMENT '最小跟单金额 (lamports)',
    profit_exit_ratio INT COMMENT '盈利出清比率 (%)',
    loss_exit_ratio INT COMMENT '亏损出清比例 (%)',
    follow_exit BOOLEAN DEFAULT TRUE COMMENT '是否跟随聪明钱出清',
    enable_smart_copy BOOLEAN DEFAULT FALSE COMMENT '是否启用智能跟单',
    smart_wallets TEXT COMMENT '智能跟单钱包列表，逗号分隔',
    smart_money_wallets TEXT COMMENT '聪明钱配置，逗号分隔',
    enable_auto_follow BOOLEAN DEFAULT TRUE COMMENT '启用自动跟单',
    
    -- 统计信息
    total_profit BIGINT DEFAULT 0 COMMENT '总盈利',
    total_loss BIGINT DEFAULT 0 COMMENT '总亏损',
    net_profit BIGINT DEFAULT 0 COMMENT '净盈亏',
    profit_percent DOUBLE DEFAULT 0.0 COMMENT '盈亏百分比',
    follow_count INT DEFAULT 0 COMMENT '跟单次数',
    success_rate DOUBLE DEFAULT 0.0 COMMENT '成功率',
    total_invested BIGINT DEFAULT 0 COMMENT '总投入',
    
    FOREIGN KEY (smart_wallet_id) REFERENCES smart_wallet(id),
    INDEX idx_task_id (task_id),
    INDEX idx_user_wallet (user_wallet),
    INDEX idx_smart_wallet_id (smart_wallet_id),
    INDEX idx_status (status)
) COMMENT='跟单任务表';

-- 跟单交易记录表
CREATE TABLE IF NOT EXISTS follow_transaction (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) NOT NULL UNIQUE COMMENT '交易ID',
    follow_task_id BIGINT NOT NULL COMMENT '跟单任务ID',
    original_tx_id VARCHAR(50) COMMENT '聪明钱原始交易ID',
    token VARCHAR(20) NOT NULL COMMENT '代币符号',
    token_address VARCHAR(100) NOT NULL COMMENT '代币地址',
    action ENUM('BUY', 'SELL') NOT NULL COMMENT '交易动作',
    amount BIGINT COMMENT '跟单数量',
    price DOUBLE COMMENT '代币价格',
    cost BIGINT COMMENT '跟单成本',
    current_value BIGINT COMMENT '当前价值',
    profit BIGINT COMMENT '盈亏',
    profit_percent DOUBLE COMMENT '盈亏百分比',
    timestamp DATETIME NOT NULL COMMENT '跟单执行时间',
    status ENUM('HOLDING', 'COMPLETED') DEFAULT 'HOLDING' COMMENT '状态',
    signature VARCHAR(200) NOT NULL COMMENT '交易签名',
    delay_time INT COMMENT '跟单延迟时间 (秒)',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (follow_task_id) REFERENCES follow_task(id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_follow_task_id (follow_task_id),
    INDEX idx_original_tx_id (original_tx_id),
    INDEX idx_timestamp (timestamp),
    INDEX idx_token_address (token_address),
    INDEX idx_status (status)
) COMMENT='跟单交易记录表';

-- 插入测试数据

-- 插入聪明钱包测试数据
INSERT INTO smart_wallet (
    address, nickname, win_rate, total_profit, profit_percent, avg_hold_time, 
    recent_trades, followers, tags, status, last_active,
    total_trades, profitable_trades, total_volume, avg_profit, max_profit, max_loss, 
    best_win_streak, current_streak
) VALUES 
(
    'DRiP2Pn2K6fuMLKQmt5rZWyHiUZ6WK3GChEySUpHSS4x', '鲸鱼交易员A', 85.6, 2580000, 340.5, '2.5天',
    45, 1250, 'DeFi,Memecoin,高频', 'ACTIVE', NOW(),
    156, 134, 15680000, 16538, 245000, -89000, 12, 3
),
(
    'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', 'Alpha猎手', 92.1, 4560000, 520.3, '1.8天',
    67, 2340, 'DeFi,Alpha,短线', 'ACTIVE', NOW(),
    289, 266, 28450000, 15786, 380000, -45000, 18, 5
),
(
    '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM', 'MEV专家', 78.9, 1890000, 245.7, '0.5天',
    123, 890, 'MEV,套利,高频', 'ACTIVE', NOW(),
    445, 351, 12340000, 4247, 156000, -78000, 8, -2
);

-- 插入聪明钱包交易记录测试数据
INSERT INTO smart_wallet_transaction (
    smart_wallet_id, token, token_address, action, amount, price, cost, current_value, 
    profit, profit_percent, timestamp, status, signature, block_time, slot
) VALUES 
(
    1, 'BONK', 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263', 'BUY', 1000000, 0.000012, 12000000, 18500000,
    6500000, 54.2, NOW() - INTERVAL 2 HOUR, 'HOLDING', '5J7abcdef1234567890abcdef1234567890abcdef1234567890abcdef12345678', 1705751425, 245678901
),
(
    1, 'SOL', 'So11111111111111111111111111111111111111112', 'SELL', 5000000, 0.00008, 40000000, 45000000,
    5000000, 12.5, NOW() - INTERVAL 1 DAY, 'COMPLETED', '3K8bcdef1234567890abcdef1234567890abcdef1234567890abcdef12345679', 1705665025, 245567890
),
(
    2, 'RAY', 'RAYdefghijklmnopqrstuvwxyz1234567890abcdef', 'BUY', 2500000, 0.00015, 37500000, 42000000,
    4500000, 12.0, NOW() - INTERVAL 3 HOUR, 'HOLDING', '7L9cdef1234567890abcdef1234567890abcdef1234567890abcdef123456780', 1705745825, 245668902
);
