spring.application.name=Web3Tools

spring.datasource.url=*******************************************
spring.datasource.username=root
spring.datasource.password=540623
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=update
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=60000
spring.datasource.hikari.max-lifetime=180000
#
spring.data.redis.host=***************
spring.data.redis.password=540623
spring.data.redis.port=6379

#spring.data.redis.host=redis-14863.crce197.us-east-2-1.ec2.redns.redis-cloud.com
#spring.data.redis.username=default
#spring.data.redis.password=JarH4xWeEVxaBNMOkT9hi4EcG99LYiT2
#spring.data.redis.port=14863


# Twitter API
twitter.api.url=https://twitter-api45.p.rapidapi.com/listtimeline.php
twitter.api.search-url=https://twitter-api45.p.rapidapi.com/search.php
twitter.api.list_id=1931591138139595029
twitter.api.host=twitter-api45.p.rapidapi.com

# Twitter API Key???
twitter.api.pool.enabled=true
twitter.api.pool.max-requests-per-key=1000
twitter.api.pool.reset-interval-hours=24
twitter.api.pool.keys[0]=**************************************************
# ????API keys?????????keys?
 twitter.api.pool.keys[1]=ea8d4c3e17msh08b73b07c6c315ap12cabfjsn1b436f326af5
 twitter.api.pool.keys[2]=78f86e6279msh5d9e057e023e8efp13e608jsn12288980e102
 twitter.api.pool.keys[3]=64a6a0f0ecmsh1fc025a9017b209p1cb595jsn4f4a40dbe707
 twitter.api.pool.keys[4]=d7f67447a7mshf6288ea3917dd46p165bb0jsncd11c0a7602c
 twitter.api.pool.keys[5]=7de6d2ca0fmsh7131158183f3410p172c6ajsn08109df6ec81
 twitter.api.pool.keys[6]=654a828ea2msh7b4acf869203a7cp102605jsnbc1ffe1a618f
 twitter.api.pool.keys[7]=bf62bd227bmsh3c60f2a4db374e9p1f5c79jsn6f20933bbd7e
 twitter.api.pool.keys[8]=c33915c2dfmsh409c98a498e018ap1116dfjsn6bb8f927cb62
 twitter.api.pool.keys[9]=0fee86737dmsh1e22c218d2538d8p1036cdjsnb1745715f529
 twitter.api.pool.keys[10]=2550936f19msha924d64cbe7d24fp159d56jsn37fed08ee456
 twitter.api.pool.keys[11]=3f74ed3406msh37a6b545713573dp147f6fjsn8f05058ced66
 twitter.api.pool.keys[12]=20deadb83fmsh2a3d8c667c1db7ap1f1ea9jsnb5731cedf1f8
 twitter.api.pool.keys[13]=8447cbb6a5msh3cbe1e9d6cc803ap1a5a44jsn4ebb524f5cd5
 twitter.api.pool.keys[14]=a8ab521815mshae4977aca5ad642p15791cjsn17a6340d6214
 twitter.api.pool.keys[15]=3ee59b7b42mshb3de3aeeed2e7b1p1377a3jsncc51264b1124
 twitter.api.pool.keys[16]=791f0b2ea8msh8a7f5aa9f29fa25p1295f0jsn8110c3bf816e
 twitter.api.pool.keys[17]=4f11594c67msh801c93a03914b85p1dd2e6jsn2bb592b83bfa
 twitter.api.pool.keys[18]=4a7d32128bmsh8aa260286b05dc7p1aadf6jsn6f12eabb741e
 twitter.api.pool.keys[19]=d299f43279msh455b92d92ef9cdfp1d3a53jsnf7ac4ae3a512
 twitter.api.pool.keys[20]=2f07076bddmsh3f6cb5d11fba753p111191jsn96ac5caa471f

# ?????????????????????pool???
search.twitter.api.url=https://twitter-api45.p.rapidapi.com/search.php
twitter.api.key=**************************************************


# ????
spring.mail.host=smtp.qq.com
#spring.mail.port=587
#spring.mail.username=<EMAIL>
#spring.mail.password=ddezniuodpvuicih
spring.mail.username=<EMAIL>
spring.mail.password=stqztvpgansrbaei
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.ssl.protocols=TLSv1.2
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000

# ????
notification.email-from=<EMAIL>
notification.email-to=<EMAIL>

# Gemini AI ????
langchain4j.google-ai-gemini.api-key=AIzaSyBkurdqp9RBPvBFFFKsc24hupuAeevPBsM
langchain4j.google-ai-gemini.model-name=gemini-2.5-flash
langchain4j.google-ai-gemini.temperature=0.7
langchain4j.google-ai-gemini.max-tokens=10000

# ????????
tweet.analysis.enabled=true
tweet.analysis.confidence-threshold=70
tweet.analysis.batch-size=10

# block chain
blockchain.ethereum.rpc-url=https://mainnet.infura.io/v3/********************************
blockchain.bitcoin.api-url=https://blockstream.info/api
blockchain.solana.rpc-url=https://api.mainnet-beta.solana.com

#bark ??
bark.push.url=https://api.day.app/qJzR5zZP7SzAyqAA7xQ3DD

# ??????
blog.admin.password=admin123

# ??????
spring.servlet.multipart.max-file-size=5MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.enabled=true

# ??????????
airdrop.admin.address=******************************************
airdrop.admin.addresses=******************************************

# ??????
airdrop.limits.max-projects-per-user=100
airdrop.limits.max-wallets-per-project=10000
airdrop.limits.max-batch-import-size=1000
airdrop.limits.max-export-size=50000
airdrop.limits.max-query-page-size=100
airdrop.limits.default-page-size=20

# ??????
airdrop.sync.enabled=true
airdrop.sync.batch-size=100
airdrop.sync.delay-between-requests=100
airdrop.sync.max-retries=3
airdrop.sync.timeout-seconds=30
airdrop.sync.schedule=0 0 */6 * * *

# ??????
airdrop.export.enabled=true
airdrop.export.allowed-formats=json,csv,xlsx
airdrop.export.temp-directory=/tmp/airdrop-exports
airdrop.export.max-file-size=100
airdrop.export.retention-days=7

# ?????????
ethereum.transaction.monitor.enabled=true
# ????????
ethereum.contract.discovery.enabled=true
ethereum.contract.discovery.poll-interval=5000
ethereum.contract.discovery.max-retries=3
ethereum.contract.discovery.stats-update-interval=300000
ethereum.contract.discovery.cleanup-interval=3600000
ethereum.contract.discovery.batch-size=100

# ??????
wallet.cache.enabled=true
wallet.cache.balance-expire-minutes=5
wallet.cache.price-expire-minutes=5
wallet.cache.key-prefix=wallet:
wallet.cache.refresh-interval-minutes=1
wallet.cache.batch-size=50
wallet.cache.request-interval-ms=100

# ????API?? - ????API
market.coinbase.api-url=https://api.coinbase.com/v2
market.binance.api-url=https://api.binance.com/api/v3
market.cryptocompare.api-url=https://min-api.cryptocompare.com/data




