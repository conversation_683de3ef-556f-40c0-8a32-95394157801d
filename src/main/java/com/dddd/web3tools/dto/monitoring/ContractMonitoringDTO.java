package com.dddd.web3tools.dto.monitoring;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合约监控数据DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractMonitoringDTO {
    
    private Long id;
    private String address;
    private String name;
    private String symbol;
    private String category;
    private Integer interactions24h;
    private Integer interactions7d;
    private Integer interactions30d;
    private BigDecimal changePercent24h;
    private Long gasUsed;
    private Integer uniqueUsers;
    private BigDecimal avgTxValue;
    private Integer anomalyScore;
    private LocalDateTime lastActivity;
    private String trend; // up, down, stable
    private String riskLevel; // low, medium, high
}