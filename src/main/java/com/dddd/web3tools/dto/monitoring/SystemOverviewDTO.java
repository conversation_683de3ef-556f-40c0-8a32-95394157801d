package com.dddd.web3tools.dto.monitoring;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 系统统计概览DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SystemOverviewDTO {
    
    private Long totalContracts;
    private Long totalInteractions;
    private Long anomalyCount;
    private TopGainerDTO topGainer;
    private NetworkStatsDTO networkStats;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TopGainerDTO {
        private Long id;
        private String name;
        private String symbol;
        private BigDecimal changePercent24h;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NetworkStatsDTO {
        private BigDecimal avgGasPrice;
        private BigDecimal networkUtilization;
        private BigDecimal blockTime;
    }
}