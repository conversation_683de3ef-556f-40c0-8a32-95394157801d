package com.dddd.web3tools.dto.monitoring;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 合约详细信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractDetailDTO {
    
    private Long id;
    private String address;
    private String name;
    private String symbol;
    private String category;
    private String description;
    private String website;
    private Map<String, String> socialLinks;
    private ContractInfoDTO contractInfo;
    private RealTimeMetricsDTO realTimeMetrics;
    private List<FeeDistributionDTO> feeDistribution;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContractInfoDTO {
        private LocalDateTime createdAt;
        private String deployer;
        private Boolean verified;
        private String sourceCode;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RealTimeMetricsDTO {
        private Integer interactions24h;
        private Integer interactions7d;
        private Integer interactions30d;
        private BigDecimal changePercent24h;
        private Long gasUsed;
        private Integer uniqueUsers;
        private BigDecimal avgTxValue;
        private BigDecimal totalVolume24h;
        private Integer anomalyScore;
        private LocalDateTime lastActivity;
        private String trend;
        private String riskLevel;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FeeDistributionDTO {
        private BigDecimal minFee;
        private BigDecimal maxFee;
        private Long count;
        private BigDecimal percentage;
    }
}