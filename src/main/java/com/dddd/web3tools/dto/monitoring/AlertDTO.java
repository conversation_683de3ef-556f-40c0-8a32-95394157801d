package com.dddd.web3tools.dto.monitoring;

import com.dddd.web3tools.dto.common.PaginationDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 警报相关DTO
 */
public class AlertDTO {
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateAlertRequestDTO {
        private Long contractId;
        private String alertType;
        private String condition;
        private BigDecimal threshold;
        private String notificationMethod;
        private String webhookUrl;
        private Boolean enabled;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateAlertResponseDTO {
        private String alertId;
        private Long contractId;
        private String alertType;
        private String condition;
        private BigDecimal threshold;
        private String status;
        private LocalDateTime createdAt;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlertHistoryDTO {
        private String id;
        private Long contractId;
        private String contractName;
        private String alertType;
        private BigDecimal threshold;
        private BigDecimal actualValue;
        private LocalDateTime triggeredAt;
        private String message;
        private String severity;
        private Boolean acknowledged;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlertHistoryListDTO {
        private List<AlertHistoryDTO> alerts;
        private PaginationDTO pagination;
    }
}