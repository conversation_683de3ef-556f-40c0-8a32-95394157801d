package com.dddd.web3tools.dto.monitoring;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 异常检测规则DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnomalyRulesDTO {
    
    private List<AnomalyRuleDTO> rules;
    private String scoringMethod;
    private Integer updateFrequency;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnomalyRuleDTO {
        private Long id;
        private String name;
        private String description;
        private BigDecimal threshold;
        private BigDecimal weight;
        private Boolean enabled;
    }
}