package com.dddd.web3tools.dto.monitoring;

import com.dddd.web3tools.dto.common.PaginationDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 合约监控列表响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractMonitoringListDTO {
    
    private List<ContractMonitoringDTO> contracts;
    private PaginationDTO pagination;
}