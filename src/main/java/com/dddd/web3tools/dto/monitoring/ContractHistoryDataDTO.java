package com.dddd.web3tools.dto.monitoring;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合约历史数据DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractHistoryDataDTO {
    
    private LocalDateTime timestamp;
    private String time; // 格式化时间
    private Integer interactions;
    private Long gasUsed;
    private Integer uniqueUsers;
    private BigDecimal avgFeeRate;
}