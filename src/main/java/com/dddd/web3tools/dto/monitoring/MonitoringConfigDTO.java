package com.dddd.web3tools.dto.monitoring;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 监控配置DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MonitoringConfigDTO {
    
    private MonitoringStatusDTO monitoring;
    private AlertsConfigDTO alerts;
    private RateLimitDTO rateLimit;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonitoringStatusDTO {
        private String status;
        private Integer updateInterval;
        private Integer dataRetention;
        private List<String> supportedNetworks;
        private String currentNetwork;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlertsConfigDTO {
        private Boolean enabled;
        private Map<String, Integer> thresholds;
        private List<String> notificationChannels;
    }
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RateLimitDTO {
        private Integer requestsPerMinute;
        private Integer requestsPerHour;
    }
}