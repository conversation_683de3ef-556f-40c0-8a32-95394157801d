package com.dddd.web3tools.dto.monitoring;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合约搜索响应DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractSearchDTO {
    
    private List<ContractSearchResultDTO> results;
    private Long total;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContractSearchResultDTO {
        private Long id;
        private String address;
        private String name;
        private String symbol;
        private String category;
        private Integer anomalyScore;
        private Integer interactions24h;
        private BigDecimal changePercent24h;
    }
}