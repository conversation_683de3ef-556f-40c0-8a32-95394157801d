package com.dddd.web3tools.dto;

import com.dddd.web3tools.entity.Wallet;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 钱包数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletDTO {

    private Long id;

    private Long projectId;

    @NotBlank(message = "钱包地址不能为空")
    @Pattern(regexp = "^0x[a-fA-F0-9]{40}$", message = "无效的钱包地址格式")
    private String address;

    @Size(max = 100, message = "钱包名称不能超过100个字符")
    private String name;

    @Size(max = 500, message = "备注信息不能超过500个字符")
    private String notes;

    private BigDecimal balance;

    private LocalDate lastActivity;

    @NotBlank(message = "添加者地址不能为空")
    @Pattern(regexp = "^0x[a-fA-F0-9]{40}$", message = "无效的钱包地址格式")
    private String addedBy;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    /**
     * 添加钱包请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AddRequest {
        @NotBlank(message = "钱包地址不能为空")
        @Pattern(regexp = "^0x[a-fA-F0-9]{40}$", message = "无效的钱包地址格式")
        private String address;

        @Size(max = 100, message = "钱包名称不能超过100个字符")
        private String name;

        @Size(max = 500, message = "备注信息不能超过500个字符")
        private String notes;

        @NotBlank(message = "添加者地址不能为空")
        @Pattern(regexp = "^0x[a-fA-F0-9]{40}$", message = "无效的钱包地址格式")
        private String addedBy;
    }

    /**
     * 批量导入钱包请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BatchImportRequest {
        @NotEmpty(message = "钱包列表不能为空")
        @Size(max = 1000, message = "单次最多导入1000个钱包")
        private List<AddRequest> wallets;
    }

    /**
     * 批量导入响应DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BatchImportResponse {
        private List<WalletDTO> wallets;
        private Integer imported;
        private Integer failed;
        private List<String> errors;
    }

    /**
     * 更新钱包请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UpdateRequest {
        @Size(max = 100, message = "钱包名称不能超过100个字符")
        private String name;

        @Size(max = 500, message = "备注信息不能超过500个字符")
        private String notes;
    }

    /**
     * 钱包详情DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DetailResponse {
        private String address;
        private BalanceInfo balance;
        private Integer transactionCount;
        private LocalDateTime firstActivity;
        private LocalDateTime lastActivity;
        private List<TokenInfo> tokens;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class BalanceInfo {
            private String nativeToken;
            private String usd;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class TokenInfo {
            private String symbol;
            private String balance;
            private String usdValue;
        }
    }

    /**
     * 钱包余额DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BalanceResponse {
        private String address;
        private String chain;
        private BalanceDetail balance;
        private LocalDateTime updatedAt;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class BalanceDetail {
            private NativeBalance nativeToken;
            private List<TokenBalance> tokens;
            private String totalUsdValue;

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            @Builder
            public static class NativeBalance {
                private String symbol;
                private String balance;
                private String usdValue;
            }

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            @Builder
            public static class TokenBalance {
                private String address;
                private String symbol;
                private String name;
                private String balance;
                private Integer decimals;
                private String usdValue;
            }
        }
    }

    /**
     * 钱包验证DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ValidationRequest {
        @NotBlank(message = "钱包地址不能为空")
        private String address;

        @NotBlank(message = "区块链类型不能为空")
        @Pattern(regexp = "^(ethereum|arbitrum|polygon|bsc|avalanche|fantom|optimism|base)$", 
                 message = "不支持的区块链类型")
        private String chain;
    }

    /**
     * 钱包验证响应DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ValidationResponse {
        private String address;
        private Boolean isValid;
        private String format;
        private String checksumAddress;
    }

    /**
     * 将实体转换为DTO
     */
    public static WalletDTO fromEntity(Wallet wallet) {
        if (wallet == null) {
            return null;
        }
        
        return WalletDTO.builder()
                .id(wallet.getId())
                .projectId(wallet.getProjectId())
                .address(wallet.getAddress())
                .name(wallet.getName())
                .notes(wallet.getNotes())
                .balance(wallet.getBalance())
                .lastActivity(wallet.getLastActivity())
                .addedBy(wallet.getAddedBy())
                .createdAt(wallet.getCreatedAt())
                .updatedAt(wallet.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     */
    public Wallet toEntity() {
        return Wallet.builder()
                .id(this.id)
                .address(this.address)
                .name(this.name)
                .notes(this.notes)
                .balance(this.balance)
                .lastActivity(this.lastActivity)
                .addedBy(this.addedBy)
                .createdAt(this.createdAt)
                .updatedAt(this.updatedAt)
                .build();
    }
}
