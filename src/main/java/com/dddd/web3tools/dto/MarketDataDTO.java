package com.dddd.web3tools.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 市场数据DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarketDataDTO {

    private Map<String, TokenPrice> prices;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TokenPrice {
        private String symbol;
        private String name;
        private BigDecimal price;
        private BigDecimal change24h;
        private BigDecimal volume24h;
        private BigDecimal marketCap;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime updatedAt;
    }
}
