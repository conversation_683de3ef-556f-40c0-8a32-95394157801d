package com.dddd.web3tools.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易记录DTO（仅用于API响应，不存储到数据库）
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransactionDTO {

    private String id;
    private String token;
    private String tokenAddress;
    private String action; // buy, sell
    private Long amount; // 代币数量
    private BigDecimal price; // 代币价格 (SOL)
    private Long cost; // 成本 (lamports)
    private Long currentValue; // 当前价值 (lamports)
    private Long profit; // 盈亏 (lamports)
    private BigDecimal profitPercent; // 盈亏百分比
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime timestamp; // 交易时间
    
    private String status; // holding, completed
    private String signature; // 交易签名
    private Long blockTime; // 区块时间戳
    private Long slot; // Solana slot

    /**
     * 跟单交易记录（继承自TransactionDTO）
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FollowTransaction {
        private String id;
        private String originalTxId; // 聪明钱原始交易ID
        private String token;
        private String tokenAddress;
        private String action;
        private Long amount; // 跟单数量
        private BigDecimal price;
        private Long cost; // 跟单成本
        private Long currentValue; // 当前价值
        private Long profit; // 盈亏
        private BigDecimal profitPercent;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime timestamp; // 跟单执行时间
        
        private String status;
        private String signature;
        private Integer delayTime; // 跟单延迟时间 (秒)
    }

    /**
     * 查询请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class QueryRequest {
        private Integer page = 1;
        private Integer limit = 50;
        private String status = "all"; // all, holding, completed
        private String action = "all"; // all, buy, sell
        private String tokenAddress; // 代币地址筛选，可选
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime startTime; // 开始时间，可选
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime endTime; // 结束时间，可选
    }

    /**
     * 交易汇总信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Summary {
        private Long totalProfit; // 总盈利
        private Long totalLoss; // 总亏损
        private Long netProfit; // 净盈亏
        private BigDecimal profitPercent; // 整体收益率
    }

    /**
     * 列表响应DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ListResponse {
        private List<TransactionDTO> transactions;
        private Long total;
        private Integer page;
        private Integer totalPages;
        private Summary summary;
    }

    /**
     * 跟单任务详情响应DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TaskDetailResponse {
        private FollowTaskDTO task;
        private List<FollowTransaction> transactions;
    }
}
