package com.dddd.web3tools.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目统计数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProjectStatsDTO {

    private Long projectId;
    private Integer walletCount;
    private TotalValue totalValue;
    private Integer activeWallets;
    private Integer inactiveWallets;
    private String averageBalance;
    private List<TopWallet> topWallets;
    private ActivityStats activityStats;
    private LocalDateTime updatedAt;

    /**
     * 总价值信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TotalValue {
        private String usd;
        private String nativeToken;
    }

    /**
     * 顶级钱包信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TopWallet {
        private String address;
        private String balance;
        private String percentage;
    }

    /**
     * 活动统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ActivityStats {
        private Integer last7Days;
        private Integer last30Days;
        private Integer last90Days;
    }

    /**
     * 表格列配置DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TableColumnDTO {
        private String key;
        private String label;
        private String width;
        private Boolean editable;
        private Boolean visible;
        private Integer sortOrder;
    }

    /**
     * 导出格式枚举
     */
    public enum ExportFormat {
        JSON("json", "application/json"),
        CSV("csv", "text/csv"),
        XLSX("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

        private final String format;
        private final String contentType;

        ExportFormat(String format, String contentType) {
            this.format = format;
            this.contentType = contentType;
        }

        public String getFormat() {
            return format;
        }

        public String getContentType() {
            return contentType;
        }

        public static ExportFormat fromString(String format) {
            if (format == null) {
                return JSON;
            }
            
            for (ExportFormat exportFormat : values()) {
                if (exportFormat.format.equalsIgnoreCase(format)) {
                    return exportFormat;
                }
            }
            return JSON;
        }
    }

    /**
     * 交易记录DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TransactionDTO {
        private String hash;
        private Long blockNumber;
        private LocalDateTime timestamp;
        private String from;
        private String to;
        private String value;
        private String gasUsed;
        private String gasPrice;
        private String status;
        private String type;
    }

    /**
     * 交易记录查询请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TransactionQueryRequest {
        private String walletAddress;
        private String chain;
        private String timeRange; // 7d, 30d, 90d, 1y
        @Builder.Default
        private Integer page = 1;
        @Builder.Default
        private Integer limit = 20;
    }

    /**
     * 分页响应DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PaginationDTO {
        private Integer page;
        private Integer limit;
        private Long total;
        private Integer totalPages;
    }

    /**
     * 交易记录响应DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TransactionResponse {
        private List<TransactionDTO> transactions;
        private PaginationDTO pagination;
    }
}
