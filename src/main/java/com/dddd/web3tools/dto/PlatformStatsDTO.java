package com.dddd.web3tools.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.List;

/**
 * 平台统计数据DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlatformStatsDTO {

    /**
     * 总钱包数
     */
    private Integer totalWallets;

    /**
     * 活跃钱包数
     */
    private Integer activeWallets;

    /**
     * 总跟单用户数
     */
    private Integer totalFollowers;

    /**
     * 总跟单任务数
     */
    private Integer totalFollowTasks;

    /**
     * 活跃跟单任务数
     */
    private Integer activeFollowTasks;

    /**
     * 平均胜率
     */
    private BigDecimal avgWinRate;

    /**
     * 总盈利 (lamports)
     */
    private Long totalProfit;

    /**
     * 平均收益率
     */
    private BigDecimal avgProfitPercent;

    /**
     * 表现最佳的钱包
     */
    private List<TopPerformer> topPerformers;

    /**
     * 表现最佳钱包信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TopPerformer {
        private String walletId;
        private String nickname;
        private BigDecimal winRate;
        private BigDecimal profitPercent;
    }
}
