package com.dddd.web3tools.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 推文分析结果DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TweetAnalysisResult {
    
    /**
     * 是否包含铸造/mint相关内容
     */
    private boolean containsMintContent;
    
    /**
     * 是否包含网址链接
     */
    private boolean containsUrl;
    
    /**
     * 是否为水贴/垃圾内容
     */
    private boolean isSpam;
    
    /**
     * 是否符合推送条件
     */
    private boolean shouldNotify;
    
    /**
     * 分析置信度 (0-100)
     */
    private int confidence;
    
    /**
     * 提取的网址列表
     */
    private List<String> extractedUrls;
    
    /**
     * 检测到的mint相关关键词
     */
    private List<String> mintKeywords;
    
    /**
     * AI分析详细说明
     */
    private String analysisDetails;
    
    /**
     * 分析原因
     */
    private String reason;
    
    /**
     * 原始推文内容
     */
    private String originalContent;
}
