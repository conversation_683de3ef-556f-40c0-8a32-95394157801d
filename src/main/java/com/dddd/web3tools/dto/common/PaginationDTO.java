package com.dddd.web3tools.dto.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分页信息DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaginationDTO {
    
    private int page;
    private int limit;
    private long total;
    private int totalPages;
    
    public PaginationDTO(int page, int limit, long total) {
        this.page = page;
        this.limit = limit;
        this.total = total;
        this.totalPages = (int) Math.ceil((double) total / limit);
    }
}