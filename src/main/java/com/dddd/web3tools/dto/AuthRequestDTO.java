package com.dddd.web3tools.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 认证请求DTO
 */
public class AuthRequestDTO {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WalletConnectRequest {
        @NotBlank(message = "钱包类型不能为空")
        @Pattern(regexp = "^(metamask|unisat|phantom)$", message = "钱包类型必须是 metamask、unisat 或 phantom")
        private String walletType;

        @NotBlank(message = "钱包地址不能为空")
        private String address;

        @NotBlank(message = "签名不能为空")
        private String signature;

        @NotBlank(message = "消息不能为空")
        private String message;

        @NotNull(message = "时间戳不能为空")
        private Long timestamp;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {
        private String id;
        private String address;
        private String walletType;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime createdAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WalletConnectResponse {
        private String token;
        private UserInfo user;
        private Long expiresIn;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerifyResponse {
        private Boolean valid;
        private UserInfo user;
    }
}
