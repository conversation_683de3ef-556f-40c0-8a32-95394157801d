package com.dddd.web3tools.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 聪明钱包DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SmartWalletDTO {

    private String id;
    private String address;
    private String nickname;
    private BigDecimal winRate;
    private Long totalProfit;
    private BigDecimal profitPercent;
    private String avgHoldTime;
    private Integer recentTrades;
    private Integer followers;
    private List<String> tags;
    private String status;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime lastActive;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime updatedAt;

    /**
     * 统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Stats {
        private Integer totalTrades;
        private Integer profitableTrades;
        private Long totalVolume;
        private Long avgProfit;
        private Long maxProfit;
        private Long maxLoss;
        private Integer bestWinStreak;
        private Integer currentStreak;
    }

    private Stats stats;

    /**
     * 查询请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class QueryRequest {
        private Integer page = 1;
        private Integer limit = 20;
        private String sortBy = "profitPercent";
        private String order = "desc";
        private String status = "active";
        private List<String> tags;
    }

    /**
     * 列表响应DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ListResponse {
        private List<SmartWalletDTO> wallets;
        private Long total;
        private Integer page;
        private Integer totalPages;
    }
}
