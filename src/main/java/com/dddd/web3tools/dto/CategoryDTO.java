package com.dddd.web3tools.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

/**
 * 分类数据传输对象
 */
public class CategoryDTO {

    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    private String name;

    @NotBlank(message = "分类图标不能为空")
    @Size(max = 10, message = "图标长度不能超过10个字符")
    private String icon;

    @NotBlank(message = "分类颜色不能为空")
    @Pattern(regexp = "^#[0-9a-fA-F]{6}$", message = "颜色格式必须为十六进制格式，如#3b82f6")
    private String color;

    // Getter和Setter方法
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }
}
