package com.dddd.web3tools.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 用户钱包DTO
 */
public class UserWalletDTO {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WalletInfo {
        private String id;
        private String address;
        private String name;
        private String notes;
        private BigDecimal balance;
        private BigDecimal usdValue;
        private List<TokenInfo> tokens;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime createdAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TokenInfo {
        private String symbol;
        private String name;
        private BigDecimal balance;
        private BigDecimal usdValue;
        private BigDecimal price;
        private BigDecimal change24h;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WalletsResponse {
        private Map<String, List<WalletInfo>> wallets; // 按链分组的钱包列表
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddWalletRequest {
        @NotBlank(message = "链类型不能为空")
        @Pattern(regexp = "^(EVM|BTC|SOLANA)$", message = "链类型必须是 EVM、BTC 或 SOLANA")
        private String chain;

        @NotBlank(message = "钱包地址不能为空")
        @Size(max = 64, message = "钱包地址长度不能超过64个字符")
        private String address;

        @NotBlank(message = "用户地址不能为空")
        @Size(max = 64, message = "用户地址长度不能超过64个字符")
        private String userAddress;

        @Size(max = 100, message = "钱包名称长度不能超过100个字符")
        private String name;

        @Size(max = 1000, message = "备注长度不能超过1000个字符")
        private String notes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateWalletRequest {
        @Size(max = 100, message = "钱包名称长度不能超过100个字符")
        private String name;

        @Size(max = 1000, message = "备注长度不能超过1000个字符")
        private String notes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchDeleteRequest {
        @NotNull(message = "钱包ID列表不能为空")
        private List<String> walletIds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BatchDeleteResponse {
        private Integer deletedCount;
        private List<String> failedIds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefreshResponse {
        private String id;
        private BigDecimal balance;
        private BigDecimal usdValue;
        private List<TokenInfo> tokens;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefreshAllResponse {
        private Integer refreshedCount;
        private BigDecimal totalValue;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
        private LocalDateTime updatedAt;
    }
}
