package com.dddd.web3tools.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 跟单任务DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FollowTaskDTO {

    private String taskId;
    private String smartWalletId;
    private String smartWalletAddress;
    private String smartWalletNickname;
    private String status;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'")
    private LocalDateTime updatedAt;

    private Config config;
    private Stats stats;

    /**
     * 跟单配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Config {
        // 基础配置
        private String copyMode; // ratio, fixed
        private Integer copyRatio; // 跟单比例 (%)
        private Long fixedAmount; // 固定金额 (lamports)
        
        // 限额配置
        private Long dailyLimit; // 每日跟单上限 (lamports)
        private Long totalLimit; // 总跟单上限 (lamports)
        private Long minAmount; // 最小跟单金额 (lamports)
        
        // 风控配置
        private Integer profitExitRatio; // 盈利出清比率 (%)
        private Integer lossExitRatio; // 亏损出清比例 (%)
        private Boolean followExit; // 是否跟随聪明钱出清
        
        // 智能跟单
        private Boolean enableSmartCopy; // 是否启用智能跟单
        private List<String> smartWallets; // 智能跟单钱包列表
        
        // 聪明钱配置
        private List<String> smartMoneyWallets;
        
        // 其他
        private Boolean enableAutoFollow; // 启用自动跟单
    }

    /**
     * 统计信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class Stats {
        private Long totalProfit; // 总盈利 (lamports)
        private Long totalLoss; // 总亏损 (lamports)
        private Long netProfit; // 净盈亏 (lamports)
        private BigDecimal profitPercent; // 盈亏百分比
        private Integer followCount; // 跟单次数
        private BigDecimal successRate; // 成功率
        private Long totalInvested; // 总投入 (lamports)
    }

    /**
     * 创建请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CreateRequest {
        private String wallet;
        private String smartWalletId;
        private String walletPrivateKey;
        private Config config;
    }

    /**
     * 查询请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class QueryRequest {
        private String wallet;
        private String status = "all";
        private Integer page = 1;
        private Integer limit = 20;
    }

    /**
     * 状态更新请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class StatusUpdateRequest {
        private String wallet;
        private String taskId;
        private String status;
    }

    /**
     * 删除请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class DeleteRequest {
        private String wallet;
        private String taskId;
    }

    /**
     * 列表响应DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ListResponse {
        private List<FollowTaskDTO> tasks;
        private Long total;
        private Integer page;
        private Integer totalPages;
    }
}
