package com.dddd.web3tools.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 钱包资产总览DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WalletOverviewDTO {

    private BigDecimal totalValue;
    private Integer totalWallets;
    private List<ChainDistribution> chainDistribution;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChainDistribution {
        private String chain;
        private String name;
        private BigDecimal value;
        private BigDecimal percentage;
        private Integer walletCount;
        private BigDecimal change24h;
    }
}
