package com.dddd.web3tools.dto;

import com.dddd.web3tools.entity.Project;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProjectDTO {

    private Long id;

    @NotBlank(message = "项目名称不能为空")
    @Size(max = 100, message = "项目名称不能超过100个字符")
    private String name;

    @NotBlank(message = "项目描述不能为空")
    @Size(max = 500, message = "项目描述不能超过500个字符")
    private String description;

    @NotBlank(message = "区块链类型不能为空")
    @Pattern(regexp = "^(ethereum|arbitrum|polygon|bsc|avalanche|fantom|optimism|base)$", 
             message = "不支持的区块链类型")
    private String chain;

    @NotNull(message = "开始日期不能为空")
    @FutureOrPresent(message = "开始日期不能早于今天")
    private LocalDate startDate;

    @NotNull(message = "结束日期不能为空")
    @Future(message = "结束日期必须是未来日期")
    private LocalDate endDate;

    @NotNull(message = "项目状态不能为空")
    @Pattern(regexp = "^(active|inactive|paused)$", message = "无效的项目状态")
    private String status;

    private Integer walletCount;

    private BigDecimal totalValue;

    @NotBlank(message = "创建者地址不能为空")
    @Pattern(regexp = "^0x[a-fA-F0-9]{40}$", message = "无效的钱包地址格式")
    private String createdBy;

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    private List<WalletDTO> wallets;

    /**
     * 创建项目请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CreateRequest {
        @NotBlank(message = "项目名称不能为空")
        @Size(max = 100, message = "项目名称不能超过100个字符")
        private String name;

        @NotBlank(message = "项目描述不能为空")
        @Size(max = 500, message = "项目描述不能超过500个字符")
        private String description;

        @NotBlank(message = "区块链类型不能为空")
        @Pattern(regexp = "^(ethereum|arbitrum|polygon|bsc|avalanche|fantom|optimism|base)$", 
                 message = "不支持的区块链类型")
        private String chain;

        @NotNull(message = "开始日期不能为空")
        private LocalDate startDate;

        @NotNull(message = "结束日期不能为空")
        private LocalDate endDate;

        @NotNull(message = "项目状态不能为空")
        @Pattern(regexp = "^(active|inactive|paused)$", message = "无效的项目状态")
        private String status;

        @NotBlank(message = "创建者地址不能为空")
        @Pattern(regexp = "^0x[a-fA-F0-9]{40}$", message = "无效的钱包地址格式")
        private String createdBy;
    }

    /**
     * 更新项目请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class UpdateRequest {
        @Size(max = 100, message = "项目名称不能超过100个字符")
        private String name;

        @Size(max = 500, message = "项目描述不能超过500个字符")
        private String description;

        @Pattern(regexp = "^(ethereum|arbitrum|polygon|bsc|avalanche|fantom|optimism|base)$", 
                 message = "不支持的区块链类型")
        private String chain;

        private LocalDate startDate;

        private LocalDate endDate;

        @Pattern(regexp = "^(active|inactive|paused)$", message = "无效的项目状态")
        private String status;
    }

    /**
     * 项目查询请求DTO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class QueryRequest {
        @NotBlank(message = "用户地址不能为空")
        @Pattern(regexp = "^0x[a-fA-F0-9]{40}$", message = "无效的钱包地址格式")
        private String userAddress;

        @Min(value = 1, message = "页码必须大于0")
        @Builder.Default
        private Integer page = 1;

        @Min(value = 1, message = "每页数量必须大于0")
        @Max(value = 100, message = "每页数量不能超过100")
        @Builder.Default
        private Integer limit = 10;

        private String chain;

        @Pattern(regexp = "^(active|inactive|paused)$", message = "无效的项目状态")
        private String status;
    }

    /**
     * 将实体转换为DTO
     */
    public static ProjectDTO fromEntity(Project project) {
        if (project == null) {
            return null;
        }
        
        return ProjectDTO.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .chain(project.getChain())
                .startDate(project.getStartDate())
                .endDate(project.getEndDate())
                .status(project.getStatus().name())
                .walletCount(project.getWalletCount())
                .totalValue(project.getTotalValue())
                .createdBy(project.getCreatedBy())
                .createdAt(project.getCreatedAt())
                .updatedAt(project.getUpdatedAt())
                .build();
    }

    /**
     * 将DTO转换为实体
     */
    public Project toEntity() {
        return Project.builder()
                .id(this.id)
                .name(this.name)
                .description(this.description)
                .chain(this.chain)
                .startDate(this.startDate)
                .endDate(this.endDate)
                .status(Project.ProjectStatus.valueOf(this.status))
                .walletCount(this.walletCount)
                .totalValue(this.totalValue)
                .createdBy(this.createdBy)
                .createdAt(this.createdAt)
                .updatedAt(this.updatedAt)
                .build();
    }
}
