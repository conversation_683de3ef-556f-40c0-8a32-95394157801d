package com.dddd.web3tools.constant;

public class RedisKeyConstant {

    /**
     * 关键词缓存键
     */
    public static final String KEYWORDS_CACHE_KEY = "notification:keywords";

    /**
     * 删除关键词缓存键
     */
    public static final String DELETE_KEYWORDS_CACHE_KEY = "notification:delete_keywords";

    /**
     * 必须包含的关键词缓存键
     */
    public static final String REQUIRED_KEYWORDS_CACHE_KEY = "notification:required_keywords";

    /**
     * 黑名单缓存键
     */
    public static final String BLACKLIST_CACHE_KEY = "blacklist:users";

    /**
     * gas搜索关键词缓存键
     */
    public static final String GAS_KEYWORD_CACHE_KEY = "gas:keywords";

    /**
     * 推特ID缓存键 - 最近7天的推特ID列表
     */
    public static final String TWEET_IDS_CACHE_KEY = "tweets:recent_ids";

    /**
     * 推特ID缓存过期时间（秒） - 7天
     */
    public static final long TWEET_IDS_CACHE_EXPIRE_SECONDS = 7 * 24 * 60 * 60;
}
