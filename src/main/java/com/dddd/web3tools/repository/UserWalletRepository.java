package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.User;
import com.dddd.web3tools.entity.UserWallet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 用户钱包数据访问接口
 */
@Repository
public interface UserWalletRepository extends JpaRepository<UserWallet, Long> {

    /**
     * 根据用户ID查找所有钱包
     */
    @Query("SELECT w FROM UserWallet w WHERE w.user.id = :userId ORDER BY w.createdAt DESC")
    List<UserWallet> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);

    /**
     * 根据用户ID和链类型查找钱包
     */
    @Query("SELECT w FROM UserWallet w WHERE w.user.id = :userId AND w.chain = :chain ORDER BY w.createdAt DESC")
    List<UserWallet> findByUserIdAndChainOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("chain") String chain);

    /**
     * 根据用户ID和地址查找钱包
     */
    @Query("SELECT w FROM UserWallet w WHERE w.user.id = :userId AND w.address = :address")
    Optional<UserWallet> findByUserIdAndAddress(@Param("userId") Long userId, @Param("address") String address);

    /**
     * 检查用户是否已添加该地址的钱包
     */
    @Query("SELECT COUNT(w) > 0 FROM UserWallet w WHERE w.user.id = :userId AND w.address = :address")
    boolean existsByUserIdAndAddress(@Param("userId") Long userId, @Param("address") String address);

    /**
     * 根据用户ID统计钱包总数
     */
    @Query("SELECT COUNT(w) FROM UserWallet w WHERE w.user.id = :userId")
    long countByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID计算总资产价值
     */
    @Query("SELECT COALESCE(SUM(w.usdValue), 0) FROM UserWallet w WHERE w.user.id = :userId")
    BigDecimal sumUsdValueByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和链类型统计钱包数量
     */
    @Query("SELECT COUNT(w) FROM UserWallet w WHERE w.user.id = :userId AND w.chain = :chain")
    long countByUserIdAndChain(@Param("userId") Long userId, @Param("chain") String chain);

    /**
     * 根据用户ID和链类型计算资产价值
     */
    @Query("SELECT COALESCE(SUM(w.usdValue), 0) FROM UserWallet w WHERE w.user.id = :userId AND w.chain = :chain")
    BigDecimal sumUsdValueByUserIdAndChain(@Param("userId") Long userId, @Param("chain") String chain);

    /**
     * 根据用户ID删除所有钱包
     */
    @Modifying
    @Query("DELETE FROM UserWallet w WHERE w.user.id = :userId")
    void deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID和钱包ID列表批量删除
     */
    @Modifying
    @Query("DELETE FROM UserWallet w WHERE w.user.id = :userId AND w.id IN :walletIds")
    void deleteByUserIdAndIdIn(@Param("userId") Long userId, @Param("walletIds") List<Long> walletIds);

    /**
     * 根据用户地址查找该用户的所有钱包
     */
    @Query("SELECT w FROM UserWallet w WHERE w.user.address = :userAddress ORDER BY w.createdAt DESC")
    List<UserWallet> findAllWalletsByAddress(@Param("userAddress") String userAddress);

    /**
     * 根据钱包地址查找用户
     */
    @Query("SELECT w.user FROM UserWallet w WHERE w.address = :address")
    Optional<User> findUserByWalletAddress(@Param("address") String address);

    // ==================== 基于地址的查询方法 ====================

    /**
     * 根据用户地址和链类型查找钱包
     */
    @Query("SELECT w FROM UserWallet w WHERE w.user.address = :userAddress AND w.chain = :chain ORDER BY w.createdAt DESC")
    List<UserWallet> findByUserAddressAndChainOrderByCreatedAtDesc(@Param("userAddress") String userAddress, @Param("chain") String chain);

    /**
     * 根据用户地址和钱包地址查找钱包
     */
    @Query("SELECT w FROM UserWallet w WHERE w.user.address = :userAddress AND w.address = :walletAddress")
    Optional<UserWallet> findByUserAddressAndWalletAddress(@Param("userAddress") String userAddress, @Param("walletAddress") String walletAddress);

    /**
     * 检查用户地址是否已添加该钱包地址
     */
    @Query("SELECT COUNT(w) > 0 FROM UserWallet w WHERE w.user.address = :userAddress AND w.address = :walletAddress")
    boolean existsByUserAddressAndWalletAddress(@Param("userAddress") String userAddress, @Param("walletAddress") String walletAddress);

    /**
     * 根据用户地址统计钱包总数
     */
    @Query("SELECT COUNT(w) FROM UserWallet w WHERE w.user.address = :userAddress")
    long countByUserAddress(@Param("userAddress") String userAddress);

    /**
     * 根据用户地址计算总资产价值
     */
    @Query("SELECT COALESCE(SUM(w.usdValue), 0) FROM UserWallet w WHERE w.user.address = :userAddress")
    BigDecimal sumUsdValueByUserAddress(@Param("userAddress") String userAddress);

    /**
     * 根据用户地址和链类型统计钱包数量
     */
    @Query("SELECT COUNT(w) FROM UserWallet w WHERE w.user.address = :userAddress AND w.chain = :chain")
    long countByUserAddressAndChain(@Param("userAddress") String userAddress, @Param("chain") String chain);

    /**
     * 根据用户地址和链类型计算资产价值
     */
    @Query("SELECT COALESCE(SUM(w.usdValue), 0) FROM UserWallet w WHERE w.user.address = :userAddress AND w.chain = :chain")
    BigDecimal sumUsdValueByUserAddressAndChain(@Param("userAddress") String userAddress, @Param("chain") String chain);

    /**
     * 根据用户地址删除所有钱包
     */
    @Modifying
    @Query("DELETE FROM UserWallet w WHERE w.user.address = :userAddress")
    void deleteByUserAddress(@Param("userAddress") String userAddress);
}
