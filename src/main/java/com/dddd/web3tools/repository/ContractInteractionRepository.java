package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.ContractInteraction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 合约交互记录数据仓库
 */
@Repository
public interface ContractInteractionRepository extends JpaRepository<ContractInteraction, Long> {
    
    /**
     * 查找指定合约在指定时间范围内的交互记录
     */
    List<ContractInteraction> findByContractAddressAndInteractionTimeBetween(
        String contractAddress, LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计指定合约在指定时间范围内的交互次数
     */
    @Query("SELECT COUNT(ci) FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress AND ci.interactionTime BETWEEN :startTime AND :endTime")
    Long countInteractionsByContractAndTimeRange(
        @Param("contractAddress") String contractAddress, 
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 统计指定合约在指定时间范围内的独立用户数
     */
    @Query("SELECT COUNT(DISTINCT ci.userAddress) FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress AND ci.interactionTime BETWEEN :startTime AND :endTime")
    Long countUniqueUsersByContractAndTimeRange(
        @Param("contractAddress") String contractAddress, 
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 计算指定合约在指定时间范围内的总交易价值
     */
    @Query("SELECT COALESCE(SUM(ci.transactionValue), 0) FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress AND ci.interactionTime BETWEEN :startTime AND :endTime")
    BigDecimal sumTransactionValueByContractAndTimeRange(
        @Param("contractAddress") String contractAddress, 
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 计算指定合约在指定时间范围内的平均Gas使用量
     */
    @Query("SELECT COALESCE(AVG(ci.gasUsed), 0) FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress AND ci.interactionTime BETWEEN :startTime AND :endTime AND ci.gasUsed IS NOT NULL")
    Double avgGasUsedByContractAndTimeRange(
        @Param("contractAddress") String contractAddress, 
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 计算指定合约在指定时间范围内的总Gas使用量
     */
    @Query("SELECT COALESCE(SUM(ci.gasUsed), 0) FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress AND ci.interactionTime BETWEEN :startTime AND :endTime AND ci.gasUsed IS NOT NULL")
    Long sumGasUsedByContractAndTimeRange(
        @Param("contractAddress") String contractAddress, 
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 计算指定合约在指定时间范围内的平均交易价值
     */
    @Query("SELECT COALESCE(AVG(ci.transactionValue), 0) FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress AND ci.interactionTime BETWEEN :startTime AND :endTime")
    BigDecimal avgTransactionValueByContractAndTimeRange(
        @Param("contractAddress") String contractAddress, 
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取指定合约在指定时间范围内的最大交易价值
     */
    @Query("SELECT COALESCE(MAX(ci.transactionValue), 0) FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress AND ci.interactionTime BETWEEN :startTime AND :endTime")
    BigDecimal maxTransactionValueByContractAndTimeRange(
        @Param("contractAddress") String contractAddress, 
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取指定合约的最新交互时间
     */
    @Query("SELECT MAX(ci.interactionTime) FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress")
    LocalDateTime findLatestInteractionTime(@Param("contractAddress") String contractAddress);
    
    /**
     * 删除指定时间之前的交互记录（清理旧数据）
     */
    @Modifying
    @Query("DELETE FROM ContractInteraction ci WHERE ci.interactionTime < :cutoffTime")
    void deleteOldInteractions(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 检查交易是否已存在
     */
    boolean existsByTransactionHash(String transactionHash);
    
    /**
     * 获取指定合约的热门方法签名
     */
    @Query("SELECT ci.methodSignature, COUNT(ci) as count FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress AND ci.interactionTime BETWEEN :startTime AND :endTime AND ci.methodSignature IS NOT NULL GROUP BY ci.methodSignature ORDER BY count DESC")
    List<Object[]> getPopularMethodsByContract(
        @Param("contractAddress") String contractAddress, 
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime);
    
    /**
     * 获取指定合约的活跃用户列表
     */
    @Query("SELECT ci.userAddress, COUNT(ci) as interactionCount FROM ContractInteraction ci WHERE ci.contractAddress = :contractAddress AND ci.interactionTime BETWEEN :startTime AND :endTime GROUP BY ci.userAddress ORDER BY interactionCount DESC")
    List<Object[]> getActiveUsersByContract(
        @Param("contractAddress") String contractAddress, 
        @Param("startTime") LocalDateTime startTime, 
        @Param("endTime") LocalDateTime endTime);
}
