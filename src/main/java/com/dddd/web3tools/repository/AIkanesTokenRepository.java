package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.AIkanesToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.util.List;
import java.util.Optional;

public interface AIkanesTokenRepository extends JpaRepository<AIkanesToken, Long> {

    @Query("SELECT t FROM AIkanesToken t WHERE t.actualMintActive = 1 AND t.mempoolCount > 0")
    List<AIkanesToken> findActiveTokensWithMempoolActivity();

    Optional<AIkanesToken> findByTokenId(String tokenId);
    Optional<AIkanesToken> findByName(String name);
}