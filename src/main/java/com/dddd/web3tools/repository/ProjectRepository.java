package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.Project;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 项目数据访问层
 */
@Repository
public interface ProjectRepository extends JpaRepository<Project, Long> {

    /**
     * 根据创建者查询项目
     */
    Page<Project> findByCreatedBy(String createdBy, Pageable pageable);

    /**
     * 根据创建者和状态查询项目
     */
    Page<Project> findByCreatedByAndStatus(String createdBy, Project.ProjectStatus status, Pageable pageable);

    /**
     * 根据创建者和区块链类型查询项目
     */
    Page<Project> findByCreatedByAndChain(String createdBy, String chain, Pageable pageable);

    /**
     * 根据创建者、状态和区块链类型查询项目
     */
    Page<Project> findByCreatedByAndStatusAndChain(String createdBy, Project.ProjectStatus status, String chain, Pageable pageable);

    /**
     * 根据项目名称查询项目（模糊匹配）
     */
    Page<Project> findByNameContainingIgnoreCase(String name, Pageable pageable);

    /**
     * 根据创建者和项目名称查询项目（模糊匹配）
     */
    Page<Project> findByCreatedByAndNameContainingIgnoreCase(String createdBy, String name, Pageable pageable);

    /**
     * 查询指定日期范围内的项目
     */
    @Query("SELECT p FROM Project p WHERE p.startDate <= :endDate AND p.endDate >= :startDate")
    List<Project> findProjectsInDateRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 查询活跃项目
     */
    @Query("SELECT p FROM Project p WHERE p.status = 'active' AND p.startDate <= CURRENT_DATE AND p.endDate >= CURRENT_DATE")
    List<Project> findActiveProjects();

    /**
     * 查询即将结束的项目（7天内结束）
     */
    @Query("SELECT p FROM Project p WHERE p.status = 'active' AND p.endDate BETWEEN CURRENT_DATE AND :endDate")
    List<Project> findProjectsEndingSoon(@Param("endDate") LocalDate endDate);

    /**
     * 统计用户创建的项目数量
     */
    long countByCreatedBy(String createdBy);

    /**
     * 统计指定状态的项目数量
     */
    long countByStatus(Project.ProjectStatus status);

    /**
     * 统计指定区块链的项目数量
     */
    long countByChain(String chain);

    /**
     * 查询项目统计信息
     */
    @Query("SELECT p.chain as chain, COUNT(p) as count, SUM(p.walletCount) as totalWallets, SUM(p.totalValue) as totalValue " +
           "FROM Project p WHERE p.createdBy = :createdBy GROUP BY p.chain")
    List<Object[]> getProjectStatsByChain(@Param("createdBy") String createdBy);

    /**
     * 查询用户的项目概览
     */
    @Query("SELECT p.status as status, COUNT(p) as count FROM Project p WHERE p.createdBy = :createdBy GROUP BY p.status")
    List<Object[]> getProjectOverviewByUser(@Param("createdBy") String createdBy);

    /**
     * 检查项目名称是否已存在（同一用户）
     */
    boolean existsByCreatedByAndNameIgnoreCase(String createdBy, String name);

    /**
     * 查询用户最近创建的项目
     */
    List<Project> findTop10ByCreatedByOrderByCreatedAtDesc(String createdBy);

    /**
     * 根据ID和创建者查询项目（权限验证）
     */
    Optional<Project> findByIdAndCreatedBy(Long id, String createdBy);

    /**
     * 查询钱包数量最多的项目
     */
    List<Project> findTop10ByOrderByWalletCountDesc();

    /**
     * 查询总价值最高的项目
     */
    List<Project> findTop10ByOrderByTotalValueDesc();

    /**
     * 自定义查询：根据多个条件查询项目
     */
    @Query("SELECT p FROM Project p WHERE " +
           "(:createdBy IS NULL OR p.createdBy = :createdBy) AND " +
           "(:status IS NULL OR p.status = :status) AND " +
           "(:chain IS NULL OR p.chain = :chain) AND " +
           "(:name IS NULL OR LOWER(p.name) LIKE LOWER(CONCAT('%', :name, '%'))) AND " +
           "(:startDate IS NULL OR p.startDate >= :startDate) AND " +
           "(:endDate IS NULL OR p.endDate <= :endDate)")
    Page<Project> findProjectsByConditions(
            @Param("createdBy") String createdBy,
            @Param("status") Project.ProjectStatus status,
            @Param("chain") String chain,
            @Param("name") String name,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            Pageable pageable
    );
}
