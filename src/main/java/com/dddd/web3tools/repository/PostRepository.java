package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.Post;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 文章数据访问层
 */
@Repository
public interface PostRepository extends JpaRepository<Post, Long> {
    
    /**
     * 根据分类查询文章
     */
    Page<Post> findByCategoryOrderByCreatedAtDesc(String category, Pageable pageable);
    
    /**
     * 根据标题搜索文章
     */
    @Query("SELECT p FROM Post p WHERE p.title LIKE %:keyword% OR p.content LIKE %:keyword% ORDER BY p.createdAt DESC")
    Page<Post> findByTitleContainingOrContentContaining(@Param("keyword") String keyword, Pageable pageable);
    
    /**
     * 根据分类和关键词搜索文章
     */
    @Query("SELECT p FROM Post p WHERE p.category = :category AND (p.title LIKE %:keyword% OR p.content LIKE %:keyword%) ORDER BY p.createdAt DESC")
    Page<Post> findByCategoryAndTitleContainingOrContentContaining(
            @Param("category") String category, 
            @Param("keyword") String keyword, 
            Pageable pageable);
    
    /**
     * 获取所有文章，按创建时间倒序
     */
    Page<Post> findAllByOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * 根据分类统计文章数量
     */
    long countByCategory(String category);
    
    /**
     * 获取最近的文章
     */
    List<Post> findTop5ByOrderByCreatedAtDesc();
    
    /**
     * 获取总文章数
     */
    @Query("SELECT COUNT(p) FROM Post p")
    long getTotalPostCount();
}
