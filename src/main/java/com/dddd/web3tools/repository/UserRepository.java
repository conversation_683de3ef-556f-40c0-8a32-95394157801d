package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 用户数据访问接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据地址查找用户
     */
    Optional<User> findByAddress(String address);

    /**
     * 检查地址是否存在
     */
    boolean existsByAddress(String address);
}
