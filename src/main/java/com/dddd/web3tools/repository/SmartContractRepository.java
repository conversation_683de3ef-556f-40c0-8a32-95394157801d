package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.SmartContract;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 智能合约数据仓库
 */
@Repository
public interface SmartContractRepository extends JpaRepository<SmartContract, Long> {
    
    /**
     * 根据合约地址查找
     */
    Optional<SmartContract> findByContractAddress(String contractAddress);
    
    /**
     * 检查合约是否已存在
     */
    boolean existsByContractAddress(String contractAddress);
    
    /**
     * 根据部署交易哈希查找
     */
    Optional<SmartContract> findByDeployTransactionHash(String deployTransactionHash);
    
    /**
     * 查找未被过滤的合约
     */
    Page<SmartContract> findByIsFilteredFalseOrderByScoreDesc(Pageable pageable);
    
    /**
     * 根据评分范围查找合约
     */
    @Query("SELECT c FROM SmartContract c WHERE c.isFiltered = false AND c.score BETWEEN :minScore AND :maxScore ORDER BY c.score DESC")
    Page<SmartContract> findByScoreRange(@Param("minScore") BigDecimal minScore, @Param("maxScore") BigDecimal maxScore, Pageable pageable);
    
    /**
     * 查找高分合约（评分大于指定值）
     */
    @Query("SELECT c FROM SmartContract c WHERE c.isFiltered = false AND c.score >= :minScore ORDER BY c.score DESC")
    List<SmartContract> findHighScoreContracts(@Param("minScore") BigDecimal minScore);
    
    /**
     * 查找最近部署的合约
     */
    @Query("SELECT c FROM SmartContract c WHERE c.isFiltered = false AND c.createdAt >= :since ORDER BY c.createdAt DESC")
    List<SmartContract> findRecentContracts(@Param("since") LocalDateTime since);
    
    /**
     * 查找活跃合约（24小时内有交互）
     */
    @Query("SELECT c FROM SmartContract c WHERE c.isFiltered = false AND c.interactionCount24h > 0 ORDER BY c.interactionCount24h DESC")
    Page<SmartContract> findActiveContracts(Pageable pageable);
    
    /**
     * 查找高价值合约（24小时交易总价值大于指定值）
     */
    @Query("SELECT c FROM SmartContract c WHERE c.isFiltered = false AND c.totalValue24h >= :minValue ORDER BY c.totalValue24h DESC")
    List<SmartContract> findHighValueContracts(@Param("minValue") BigDecimal minValue);
    
    /**
     * 查找用户多样性高的合约
     */
    @Query("SELECT c FROM SmartContract c WHERE c.isFiltered = false AND c.uniqueUsers24h >= :minUsers ORDER BY c.uniqueUsers24h DESC")
    List<SmartContract> findDiverseContracts(@Param("minUsers") Integer minUsers);
    
    /**
     * 查找代币合约
     */
    Page<SmartContract> findByIsTokenTrueAndIsFilteredFalseOrderByScoreDesc(Pageable pageable);
    
    /**
     * 统计未被过滤的合约数量
     */
    long countByIsFilteredFalse();
    
    /**
     * 统计代币合约数量
     */
    long countByIsTokenTrueAndIsFilteredFalse();
    
    /**
     * 查找需要更新统计数据的合约
     */
    @Query("SELECT c FROM SmartContract c WHERE c.isFiltered = false AND (c.statsUpdatedAt IS NULL OR c.statsUpdatedAt < :cutoffTime)")
    List<SmartContract> findContractsNeedingStatsUpdate(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 根据部署者地址查找合约
     */
    List<SmartContract> findByDeployerAddressAndIsFilteredFalseOrderByCreatedAtDesc(String deployerAddress);
    
    /**
     * 查找被过滤的合约
     */
    Page<SmartContract> findByIsFilteredTrueOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * 根据过滤原因统计
     */
    @Query("SELECT c.filterReason, COUNT(c) FROM SmartContract c WHERE c.isFiltered = true GROUP BY c.filterReason")
    List<Object[]> getFilterReasonStats();
    
    /**
     * 获取评分分布统计
     */
    @Query("SELECT " +
           "SUM(CASE WHEN c.score >= 80 THEN 1 ELSE 0 END) as excellent, " +
           "SUM(CASE WHEN c.score >= 60 AND c.score < 80 THEN 1 ELSE 0 END) as good, " +
           "SUM(CASE WHEN c.score >= 40 AND c.score < 60 THEN 1 ELSE 0 END) as average, " +
           "SUM(CASE WHEN c.score < 40 THEN 1 ELSE 0 END) as poor " +
           "FROM SmartContract c WHERE c.isFiltered = false")
    Object[] getScoreDistribution();

    /**
     * 获取所有合约地址（用于缓存）
     */
    @Query("SELECT c.contractAddress FROM SmartContract c")
    List<String> findAllContractAddresses();

    /**
     * 获取最近活跃的合约地址（用于缓存优化）
     */
    @Query("SELECT c.contractAddress FROM SmartContract c " +
           "WHERE c.updatedAt >= :cutoffTime OR c.createdAt >= :cutoffTime " +
           "ORDER BY c.updatedAt DESC")
    List<String> findActiveContractAddresses(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    /**
     * 根据搜索条件查找合约
     */
    @Query("SELECT c FROM SmartContract c WHERE c.isFiltered = false AND " +
           "(LOWER(c.contractName) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(c.contractSymbol) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(c.contractAddress) LIKE LOWER(CONCAT('%', :query, '%')))")
    List<SmartContract> searchContracts(@Param("query") String query, Pageable pageable);
    
    /**
     * 获取异常分数高的合约数量
     */
    @Query("SELECT COUNT(c) FROM SmartContract c WHERE c.isFiltered = false AND c.score >= :threshold")
    Long countHighAnomalyContracts(@Param("threshold") BigDecimal threshold);
    
    /**
     * 获取24小时内变化最大的合约
     */
    @Query("SELECT c FROM SmartContract c WHERE c.isFiltered = false ORDER BY c.interactionCount24h DESC")
    List<SmartContract> findTopGainers(Pageable pageable);
    
    /**
     * 获取系统统计数据
     */
    @Query("SELECT COUNT(c), SUM(c.interactionCount24h), AVG(c.score) FROM SmartContract c WHERE c.isFiltered = false")
    Object[] getSystemStats();
}
