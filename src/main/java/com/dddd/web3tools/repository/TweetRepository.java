package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.Tweet;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TweetRepository extends JpaRepository<Tweet,String> {

    @Query("SELECT t FROM Tweet t WHERE t.createdAt >= :startTime and t.type = 0 ORDER BY t.createdAt DESC")
    List<Tweet> findRecentTweets(LocalDateTime startTime);

    Page<Tweet> findAll(Pageable pageable);

    // 新增查询方法
    List<Tweet> findByScreenNameAndTypeOrderByCreatedAtDesc(String screenName, Integer type);

    List<Tweet> findByTypeAndCreatedAtAfter(Integer type, LocalDateTime createdAt);

    Page<Tweet> findByType(Integer type, Pageable pageable);

    // 查询最近7天的推特ID（用于缓存初始化）
    @Query("SELECT t.id FROM Tweet t WHERE t.createdAt >= :startTime ORDER BY t.createdAt DESC")
    List<String> findRecentTweetIds(LocalDateTime startTime);
}
