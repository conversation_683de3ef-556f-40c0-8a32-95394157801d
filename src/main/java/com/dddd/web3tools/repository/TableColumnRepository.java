package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.TableColumn;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 表格列配置数据访问层
 */
@Repository
public interface TableColumnRepository extends JpaRepository<TableColumn, Long> {

    /**
     * 根据项目ID查询表格列配置
     */
    List<TableColumn> findByProject_IdOrderBySortOrder(Long projectId);

    /**
     * 根据项目ID和列键查询
     */
    Optional<TableColumn> findByProject_IdAndColumnKey(Long projectId, String columnKey);

    /**
     * 检查列键是否已存在于项目中
     */
    boolean existsByProject_IdAndColumnKey(Long projectId, String columnKey);

    /**
     * 根据项目删除所有列配置
     */
    void deleteByProject_Id(Long projectId);

    /**
     * 查询项目中可见的列配置
     */
    List<TableColumn> findByProject_IdAndVisibleTrueOrderBySortOrder(Long projectId);

    /**
     * 查询项目中可编辑的列配置
     */
    List<TableColumn> findByProject_IdAndEditableTrueOrderBySortOrder(Long projectId);

    /**
     * 获取项目中下一个排序顺序
     */
    @Query("SELECT COALESCE(MAX(tc.sortOrder), 0) + 1 FROM TableColumn tc WHERE tc.project.id = :projectId")
    Integer getNextSortOrder(@Param("projectId") Long projectId);

    /**
     * 统计项目中的列配置数量
     */
    long countByProject_Id(Long projectId);

    /**
     * 查询默认列配置（project_id = NULL）
     */
    List<TableColumn> findByProjectIsNullOrderBySortOrder();

    /**
     * 批量更新列的可见性
     */
    @Query("UPDATE TableColumn tc SET tc.visible = :visible WHERE tc.project.id = :projectId AND tc.columnKey IN :columnKeys")
    void updateVisibilityByProject_IdAndColumnKeys(@Param("projectId") Long projectId,
                                                 @Param("columnKeys") List<String> columnKeys,
                                                 @Param("visible") Boolean visible);

    /**
     * 批量更新列的可编辑性
     */
    @Query("UPDATE TableColumn tc SET tc.editable = :editable WHERE tc.project.id = :projectId AND tc.columnKey IN :columnKeys")
    void updateEditabilityByProject_IdAndColumnKeys(@Param("projectId") Long projectId,
                                                  @Param("columnKeys") List<String> columnKeys,
                                                  @Param("editable") Boolean editable);

    /**
     * 重置列的排序顺序
     */
    @Query("UPDATE TableColumn tc SET tc.sortOrder = :sortOrder WHERE tc.id = :id")
    void updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);
}
