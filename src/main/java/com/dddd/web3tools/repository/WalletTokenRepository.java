package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.WalletToken;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 钱包代币数据访问接口
 */
@Repository
public interface WalletTokenRepository extends JpaRepository<WalletToken, Long> {

    /**
     * 根据钱包ID查找所有代币
     */
    @Query("SELECT t FROM WalletToken t WHERE t.wallet.id = :walletId ORDER BY t.usdValue DESC")
    List<WalletToken> findByWalletIdOrderByUsdValueDesc(@Param("walletId") Long walletId);

    /**
     * 根据钱包ID和代币符号查找代币
     */
    @Query("SELECT t FROM WalletToken t WHERE t.wallet.id = :walletId AND t.symbol = :symbol")
    Optional<WalletToken> findByWalletIdAndSymbol(@Param("walletId") Long walletId, @Param("symbol") String symbol);

    /**
     * 根据钱包ID删除所有代币
     */
    @Modifying
    @Query("DELETE FROM WalletToken t WHERE t.wallet.id = :walletId")
    void deleteByWalletId(@Param("walletId") Long walletId);

    /**
     * 根据钱包ID列表删除所有代币
     */
    @Modifying
    @Query("DELETE FROM WalletToken t WHERE t.wallet.id IN :walletIds")
    void deleteByWalletIdIn(@Param("walletIds") List<Long> walletIds);

    /**
     * 根据用户ID查找所有代币（通过钱包关联）
     */
    @Query("SELECT t FROM WalletToken t WHERE t.wallet.user.id = :userId ORDER BY t.usdValue DESC")
    List<WalletToken> findByUserId(@Param("userId") Long userId);

    /**
     * 根据代币符号查找所有代币记录
     */
    List<WalletToken> findBySymbol(String symbol);
}
