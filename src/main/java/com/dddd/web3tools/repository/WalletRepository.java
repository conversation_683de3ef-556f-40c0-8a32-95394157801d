package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.Wallet;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 钱包数据访问层
 */
@Repository
public interface WalletRepository extends JpaRepository<Wallet, Long> {

    /**
     * 根据项目ID查询钱包
     */
    Page<Wallet> findByProject_Id(Long projectId, Pageable pageable);

    /**
     * 根据项目ID查询所有钱包
     */
    List<Wallet> findByProject_Id(Long projectId);

    /**
     * 根据钱包地址查询
     */
    List<Wallet> findByAddress(String address);

    /**
     * 根据项目ID和钱包地址查询
     */
    Optional<Wallet> findByProject_IdAndAddress(Long projectId, String address);

    /**
     * 根据添加者查询钱包
     */
    Page<Wallet> findByAddedBy(String addedBy, Pageable pageable);

    /**
     * 根据项目ID和添加者查询钱包
     */
    Page<Wallet> findByProject_IdAndAddedBy(Long projectId, String addedBy, Pageable pageable);

    /**
     * 统计项目中的钱包数量
     */
    long countByProject_Id(Long projectId);

    /**
     * 统计用户添加的钱包数量
     */
    long countByAddedBy(String addedBy);

    /**
     * 检查钱包是否已存在于项目中
     */
    boolean existsByProject_IdAndAddress(Long projectId, String address);

    /**
     * 查询项目中余额最高的钱包
     */
    List<Wallet> findTop10ByProject_IdOrderByBalanceDesc(Long projectId);

    /**
     * 查询项目中最近活跃的钱包
     */
    List<Wallet> findTop10ByProject_IdAndLastActivityIsNotNullOrderByLastActivityDesc(Long projectId);

    /**
     * 查询项目中最近添加的钱包
     */
    List<Wallet> findTop10ByProject_IdOrderByCreatedAtDesc(Long projectId);

    /**
     * 计算项目中钱包的总余额
     */
    @Query("SELECT SUM(w.balance) FROM Wallet w WHERE w.project.id = :projectId")
    BigDecimal sumBalanceByProjectId(@Param("projectId") Long projectId);

    /**
     * 计算项目中钱包的平均余额
     */
    @Query("SELECT AVG(w.balance) FROM Wallet w WHERE w.project.id = :projectId")
    BigDecimal avgBalanceByProjectId(@Param("projectId") Long projectId);

    /**
     * 查询指定日期范围内活跃的钱包数量
     */
    @Query("SELECT COUNT(w) FROM Wallet w WHERE w.project.id = :projectId AND w.lastActivity BETWEEN :startDate AND :endDate")
    long countActiveWalletsByDateRange(@Param("projectId") Long projectId, 
                                      @Param("startDate") LocalDate startDate, 
                                      @Param("endDate") LocalDate endDate);

    /**
     * 查询项目中有余额的钱包数量
     */
    @Query("SELECT COUNT(w) FROM Wallet w WHERE w.project.id = :projectId AND w.balance > 0")
    long countWalletsWithBalance(@Param("projectId") Long projectId);

    /**
     * 查询项目中无余额的钱包数量
     */
    @Query("SELECT COUNT(w) FROM Wallet w WHERE w.project.id = :projectId AND (w.balance IS NULL OR w.balance = 0)")
    long countWalletsWithoutBalance(@Param("projectId") Long projectId);

    /**
     * 查询项目中从未活跃的钱包数量
     */
    @Query("SELECT COUNT(w) FROM Wallet w WHERE w.project.id = :projectId AND w.lastActivity IS NULL")
    long countInactiveWallets(@Param("projectId") Long projectId);

    /**
     * 根据余额范围查询钱包
     */
    @Query("SELECT w FROM Wallet w WHERE w.project.id = :projectId AND w.balance BETWEEN :minBalance AND :maxBalance")
    Page<Wallet> findByProjectIdAndBalanceBetween(@Param("projectId") Long projectId, 
                                                 @Param("minBalance") BigDecimal minBalance, 
                                                 @Param("maxBalance") BigDecimal maxBalance, 
                                                 Pageable pageable);

    /**
     * 查询项目中余额分布统计
     */
    @Query("SELECT " +
           "CASE " +
           "WHEN w.balance = 0 THEN '0' " +
           "WHEN w.balance <= 0.1 THEN '0-0.1' " +
           "WHEN w.balance <= 1 THEN '0.1-1' " +
           "WHEN w.balance <= 10 THEN '1-10' " +
           "WHEN w.balance <= 100 THEN '10-100' " +
           "ELSE '100+' " +
           "END as range, COUNT(w) as count " +
           "FROM Wallet w WHERE w.project.id = :projectId GROUP BY " +
           "CASE " +
           "WHEN w.balance = 0 THEN '0' " +
           "WHEN w.balance <= 0.1 THEN '0-0.1' " +
           "WHEN w.balance <= 1 THEN '0.1-1' " +
           "WHEN w.balance <= 10 THEN '1-10' " +
           "WHEN w.balance <= 100 THEN '10-100' " +
           "ELSE '100+' " +
           "END")
    List<Object[]> getBalanceDistribution(@Param("projectId") Long projectId);

    /**
     * 查询项目中钱包的活跃度统计
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN w.lastActivity >= :last7Days THEN 1 END) as last7Days, " +
           "COUNT(CASE WHEN w.lastActivity >= :last30Days THEN 1 END) as last30Days, " +
           "COUNT(CASE WHEN w.lastActivity >= :last90Days THEN 1 END) as last90Days " +
           "FROM Wallet w WHERE w.project.id = :projectId")
    Object[] getActivityStats(@Param("projectId") Long projectId, 
                             @Param("last7Days") LocalDate last7Days, 
                             @Param("last30Days") LocalDate last30Days, 
                             @Param("last90Days") LocalDate last90Days);

    /**
     * 根据钱包名称模糊查询
     */
    Page<Wallet> findByProject_IdAndNameContainingIgnoreCase(Long projectId, String name, Pageable pageable);

    /**
     * 根据备注模糊查询
     */
    Page<Wallet> findByProject_IdAndNotesContainingIgnoreCase(Long projectId, String notes, Pageable pageable);

    /**
     * 自定义查询：根据多个条件查询钱包
     */
    @Query("SELECT w FROM Wallet w WHERE w.project.id = :projectId AND " +
           "(:address IS NULL OR LOWER(w.address) LIKE LOWER(CONCAT('%', :address, '%'))) AND " +
           "(:name IS NULL OR LOWER(w.name) LIKE LOWER(CONCAT('%', :name, '%'))) AND " +
           "(:addedBy IS NULL OR w.addedBy = :addedBy) AND " +
           "(:minBalance IS NULL OR w.balance >= :minBalance) AND " +
           "(:maxBalance IS NULL OR w.balance <= :maxBalance) AND " +
           "(:hasActivity IS NULL OR (:hasActivity = true AND w.lastActivity IS NOT NULL) OR (:hasActivity = false AND w.lastActivity IS NULL))")
    Page<Wallet> findWalletsByConditions(
            @Param("projectId") Long projectId,
            @Param("address") String address,
            @Param("name") String name,
            @Param("addedBy") String addedBy,
            @Param("minBalance") BigDecimal minBalance,
            @Param("maxBalance") BigDecimal maxBalance,
            @Param("hasActivity") Boolean hasActivity,
            Pageable pageable
    );

    /**
     * 批量删除钱包
     */
    void deleteByProject_IdAndIdIn(Long projectId, List<Long> walletIds);

    /**
     * 根据项目ID和钱包ID查询（权限验证）
     */
    Optional<Wallet> findByIdAndProject_Id(Long id, Long projectId);
}
