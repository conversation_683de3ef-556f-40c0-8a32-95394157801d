package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.TwitterUser;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface TwitterUserRepository extends JpaRepository<TwitterUser, Long> {
    boolean existsByUserId(String userId);

    Optional<TwitterUser> findById(Long id);
    Page<TwitterUser> findBytwitterNameContaining(String twitterName, Pageable pageable);
}
