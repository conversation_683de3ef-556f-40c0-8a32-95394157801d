package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.SmartWallet;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 聪明钱包Repository
 */
@Repository
public interface SmartWalletRepository extends JpaRepository<SmartWallet, Long> {

    /**
     * 根据地址查找钱包
     */
    Optional<SmartWallet> findByAddress(String address);

    /**
     * 根据状态查找钱包
     */
    Page<SmartWallet> findByStatus(String status, Pageable pageable);

    /**
     * 根据状态查找钱包（不分页）
     */
    List<SmartWallet> findByStatus(String status);

    /**
     * 根据状态和标签查找钱包
     */
    @Query("SELECT sw FROM SmartWallet sw WHERE " +
           "(:status = 'all' OR sw.status = :status) AND " +
           "(:tags IS NULL OR :tags = '' OR sw.tags LIKE %:tags%)")
    Page<SmartWallet> findByStatusAndTags(@Param("status") String status, 
                                         @Param("tags") String tags, 
                                         Pageable pageable);

    /**
     * 获取活跃钱包数量
     */
    @Query("SELECT COUNT(sw) FROM SmartWallet sw WHERE sw.status = 'active'")
    Long countActiveWallets();

    /**
     * 获取总钱包数量
     */
    @Query("SELECT COUNT(sw) FROM SmartWallet sw")
    Long countTotalWallets();

    /**
     * 获取总跟单人数
     */
    @Query("SELECT COALESCE(SUM(sw.followers), 0) FROM SmartWallet sw")
    Long getTotalFollowers();

    /**
     * 获取平均胜率
     */
    @Query("SELECT AVG(sw.winRate) FROM SmartWallet sw WHERE sw.winRate IS NOT NULL")
    Double getAverageWinRate();

    /**
     * 获取总盈利
     */
    @Query("SELECT COALESCE(SUM(sw.totalProfit), 0) FROM SmartWallet sw WHERE sw.totalProfit > 0")
    Long getTotalProfit();

    /**
     * 获取平均收益率
     */
    @Query("SELECT AVG(sw.profitPercent) FROM SmartWallet sw WHERE sw.profitPercent IS NOT NULL")
    Double getAverageProfitPercent();

    /**
     * 获取表现最佳的钱包
     */
    @Query("SELECT sw FROM SmartWallet sw WHERE sw.status = 'active' ORDER BY sw.profitPercent DESC")
    List<SmartWallet> findTopPerformers(Pageable pageable);

    /**
     * 检查地址是否存在
     */
    boolean existsByAddress(String address);
}
