package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.FollowTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 跟单任务Repository
 */
@Repository
public interface FollowTaskRepository extends JpaRepository<FollowTask, Long> {

    /**
     * 根据任务ID查找
     */
    Optional<FollowTask> findByTaskId(String taskId);

    /**
     * 根据用户钱包地址查找任务
     */
    Page<FollowTask> findByUserWallet(String userWallet, Pageable pageable);

    /**
     * 根据用户钱包地址和状态查找任务
     */
    @Query("SELECT ft FROM FollowTask ft WHERE ft.userWallet = :userWallet AND " +
           "(:status = 'all' OR ft.status = :status)")
    Page<FollowTask> findByUserWalletAndStatus(@Param("userWallet") String userWallet, 
                                              @Param("status") String status, 
                                              Pageable pageable);

    /**
     * 根据聪明钱包ID查找任务
     */
    List<FollowTask> findBySmartWalletId(Long smartWalletId);

    /**
     * 检查用户是否已经跟单某个聪明钱包
     */
    boolean existsByUserWalletAndSmartWalletIdAndStatusIn(String userWallet, 
                                                         Long smartWalletId, 
                                                         List<String> statuses);

    /**
     * 获取活跃跟单任务数量
     */
    @Query("SELECT COUNT(ft) FROM FollowTask ft WHERE ft.status = 'active'")
    Long countActiveTasks();

    /**
     * 获取总跟单任务数量
     */
    @Query("SELECT COUNT(ft) FROM FollowTask ft")
    Long countTotalTasks();

    /**
     * 根据用户钱包地址和任务ID查找
     */
    Optional<FollowTask> findByUserWalletAndTaskId(String userWallet, String taskId);

    /**
     * 获取用户的活跃任务数量
     */
    @Query("SELECT COUNT(ft) FROM FollowTask ft WHERE ft.userWallet = :userWallet AND ft.status = 'active'")
    Long countActiveTasksByUser(@Param("userWallet") String userWallet);
}
