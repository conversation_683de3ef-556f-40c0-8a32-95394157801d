package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.WalletHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * 钱包历史数据访问接口
 */
@Repository
public interface WalletHistoryRepository extends JpaRepository<WalletHistory, Long> {

    /**
     * 根据用户ID和类型查找历史记录
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.id = :userId AND h.type = :type ORDER BY h.date DESC")
    List<WalletHistory> findByUserIdAndTypeOrderByDateDesc(@Param("userId") Long userId, @Param("type") String type);

    /**
     * 根据用户ID、类型和日期范围查找历史记录
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.id = :userId AND h.type = :type AND h.date BETWEEN :startDate AND :endDate ORDER BY h.date ASC")
    List<WalletHistory> findByUserIdAndTypeAndDateBetweenOrderByDateAsc(
            @Param("userId") Long userId, @Param("type") String type, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据用户ID、类型和日期查找历史记录
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.id = :userId AND h.type = :type AND h.date = :date")
    Optional<WalletHistory> findByUserIdAndTypeAndDate(@Param("userId") Long userId, @Param("type") String type, @Param("date") LocalDate date);

    /**
     * 根据用户ID和日期范围查找历史记录（所有类型）
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.id = :userId AND h.date BETWEEN :startDate AND :endDate ORDER BY h.date ASC")
    List<WalletHistory> findByUserIdAndDateBetweenOrderByDateAsc(
            @Param("userId") Long userId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据用户ID查找最近的历史记录
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.id = :userId AND h.type = :type ORDER BY h.date DESC")
    List<WalletHistory> findLatestByUserIdAndType(@Param("userId") Long userId, @Param("type") String type);

    /**
     * 根据用户ID删除所有历史记录
     */
    @Modifying
    @Query("DELETE FROM WalletHistory h WHERE h.user.id = :userId")
    void deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据日期和类型删除历史记录
     */
    @Modifying
    @Query("DELETE FROM WalletHistory h WHERE h.date < :date AND h.type = :type")
    void deleteByDateBeforeAndType(@Param("date") LocalDate date, @Param("type") String type);

    // ==================== 基于地址的查询方法 ====================

    /**
     * 根据用户地址和类型查找历史记录
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.address = :userAddress AND h.type = :type ORDER BY h.date DESC")
    List<WalletHistory> findByUserAddressAndTypeOrderByDateDesc(@Param("userAddress") String userAddress, @Param("type") String type);

    /**
     * 根据用户地址、类型和日期范围查找历史记录
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.address = :userAddress AND h.type = :type AND h.date BETWEEN :startDate AND :endDate ORDER BY h.date ASC")
    List<WalletHistory> findByUserAddressAndTypeAndDateBetweenOrderByDateAsc(
            @Param("userAddress") String userAddress, @Param("type") String type, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据用户地址、类型和日期查找历史记录
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.address = :userAddress AND h.type = :type AND h.date = :date")
    Optional<WalletHistory> findByUserAddressAndTypeAndDate(@Param("userAddress") String userAddress, @Param("type") String type, @Param("date") LocalDate date);

    /**
     * 根据用户地址和日期范围查找历史记录（所有类型）
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.address = :userAddress AND h.date BETWEEN :startDate AND :endDate ORDER BY h.date ASC")
    List<WalletHistory> findByUserAddressAndDateBetweenOrderByDateAsc(
            @Param("userAddress") String userAddress, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 根据用户地址查找最近的历史记录
     */
    @Query("SELECT h FROM WalletHistory h WHERE h.user.address = :userAddress AND h.type = :type ORDER BY h.date DESC")
    List<WalletHistory> findLatestByUserAddressAndType(@Param("userAddress") String userAddress, @Param("type") String type);

    /**
     * 根据用户地址删除所有历史记录
     */
    @Modifying
    @Query("DELETE FROM WalletHistory h WHERE h.user.address = :userAddress")
    void deleteByUserAddress(@Param("userAddress") String userAddress);
}
