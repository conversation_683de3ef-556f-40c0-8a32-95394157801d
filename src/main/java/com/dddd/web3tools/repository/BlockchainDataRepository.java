package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.BlockchainData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDateTime;
import java.util.List;

public interface BlockchainDataRepository extends JpaRepository<BlockchainData, Long> {

    @Query("SELECT bd FROM BlockchainData bd WHERE bd.createdAt >= :startTime ORDER BY bd.createdAt DESC")
    List<BlockchainData> findAllDataInLast30Minutes(@Param("startTime") LocalDateTime startTime);

    // 新增：查询指定时间之后的所有记录（用于获取最近10分钟的所有gas记录）
    List<BlockchainData> findByCreatedAtAfter(LocalDateTime createdAt);

    BlockchainData findFirstByChainTypeOrderByCreatedAtDesc(String chainType);

}