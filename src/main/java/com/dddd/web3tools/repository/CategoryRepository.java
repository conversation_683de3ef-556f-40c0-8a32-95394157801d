package com.dddd.web3tools.repository;

import com.dddd.web3tools.entity.Category;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 分类数据访问层
 */
@Repository
public interface CategoryRepository extends JpaRepository<Category, String> {
    
    /**
     * 根据名称查找分类
     */
    Category findByName(String name);
    
    /**
     * 获取所有分类，按创建时间排序
     */
    List<Category> findAllByOrderByCreatedAtAsc();
    
    /**
     * 检查分类名称是否存在
     */
    boolean existsByName(String name);
    
    /**
     * 获取分类总数
     */
    @Query("SELECT COUNT(c) FROM Category c")
    long getTotalCategoryCount();
}
