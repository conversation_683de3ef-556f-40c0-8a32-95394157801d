package com.dddd.web3tools.controller;

import com.dddd.web3tools.dto.AuthDTO;
import com.dddd.web3tools.dto.CategoryDTO;
import com.dddd.web3tools.dto.PostDTO;
import com.dddd.web3tools.service.BlogService;
import com.dddd.web3tools.vo.ApiResponse;
import com.dddd.web3tools.vo.CategoryVO;
import com.dddd.web3tools.vo.PageResponse;
import com.dddd.web3tools.vo.PostVO;
import com.dddd.web3tools.vo.StatsVO;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 博客管理统一控制器
 */
@RestController
@RequestMapping("/api")
@CrossOrigin(origins = "*")
public class BlogController {

    private static final Logger log = LoggerFactory.getLogger(BlogController.class);

    private final BlogService blogService;

    // 构造函数注入
    public BlogController(BlogService blogService) {
        this.blogService = blogService;
    }
    
    // ========== 文章管理接口 ==========
    
    /**
     * 获取文章列表
     */
    @GetMapping("/posts")
    public ApiResponse<PageResponse<PostVO>> getPosts(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int limit,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String search) {
        
        try {
            // 参数验证
            if (page < 1) page = 1;
            if (limit < 1 || limit > 100) limit = 10;
            
            Page<PostVO> posts = blogService.getPosts(page, limit, category, search);
            PageResponse<PostVO> response = PageResponse.fromPage(posts);
            
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("获取文章列表失败: {}", e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "获取文章列表失败", e.getMessage());
        }
    }
    
    /**
     * 获取单篇文章
     */
    @GetMapping("/posts/{id}")
    public ApiResponse<PostVO> getPost(@PathVariable Long id) {
        try {
            Optional<PostVO> post = blogService.getPostById(id);
            if (post.isPresent()) {
                return ApiResponse.success(post.get());
            } else {
                return ApiResponse.error("NOT_FOUND", "文章不存在");
            }
        } catch (Exception e) {
            log.error("获取文章失败，ID: {}, 错误: {}", id, e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "获取文章失败", e.getMessage());
        }
    }
    
    /**
     * 创建新文章
     */
    @PostMapping("/posts")
    public ApiResponse<PostVO> createPost(@Valid @RequestBody PostDTO postDTO) {
        try {
            PostVO post = blogService.createPost(postDTO);
            return ApiResponse.success(post, "文章创建成功");
        } catch (RuntimeException e) {
            log.error("创建文章失败: {}", e.getMessage());
            return ApiResponse.error("VALIDATION_ERROR", e.getMessage());
        } catch (Exception e) {
            log.error("创建文章失败: {}", e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "创建文章失败", e.getMessage());
        }
    }
    
    /**
     * 更新文章
     */
    @PutMapping("/posts/{id}")
    public ApiResponse<PostVO> updatePost(@PathVariable Long id, @Valid @RequestBody PostDTO postDTO) {
        try {
            PostVO post = blogService.updatePost(id, postDTO);
            return ApiResponse.success(post, "文章更新成功");
        } catch (RuntimeException e) {
            log.error("更新文章失败，ID: {}, 错误: {}", id, e.getMessage());
            if ("文章不存在".equals(e.getMessage())) {
                return ApiResponse.error("NOT_FOUND", "文章不存在");
            }
            return ApiResponse.error("VALIDATION_ERROR", e.getMessage());
        } catch (Exception e) {
            log.error("更新文章失败，ID: {}, 错误: {}", id, e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "更新文章失败", e.getMessage());
        }
    }
    
    /**
     * 删除文章
     */
    @DeleteMapping("/posts/{id}")
    public ApiResponse<Void> deletePost(@PathVariable Long id) {
        try {
            blogService.deletePost(id);
            return ApiResponse.success(null, "文章删除成功");
        } catch (RuntimeException e) {
            log.error("删除文章失败，ID: {}, 错误: {}", id, e.getMessage());
            if ("文章不存在".equals(e.getMessage())) {
                return ApiResponse.error("NOT_FOUND", "文章不存在");
            }
            return ApiResponse.error("VALIDATION_ERROR", e.getMessage());
        } catch (Exception e) {
            log.error("删除文章失败，ID: {}, 错误: {}", id, e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "删除文章失败", e.getMessage());
        }
    }
    
    // ========== 分类管理接口 ==========
    
    /**
     * 获取分类列表
     */
    @GetMapping("/categories")
    public ApiResponse<List<CategoryVO>> getCategories() {
        try {
            List<CategoryVO> categories = blogService.getAllCategories();
            return ApiResponse.success(categories);
        } catch (Exception e) {
            log.error("获取分类列表失败: {}", e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "获取分类列表失败", e.getMessage());
        }
    }
    
    /**
     * 获取单个分类
     */
    @GetMapping("/categories/{id}")
    public ApiResponse<CategoryVO> getCategory(@PathVariable String id) {
        try {
            Optional<CategoryVO> category = blogService.getCategoryById(id);
            if (category.isPresent()) {
                return ApiResponse.success(category.get());
            } else {
                return ApiResponse.error("NOT_FOUND", "分类不存在");
            }
        } catch (Exception e) {
            log.error("获取分类失败，ID: {}, 错误: {}", id, e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "获取分类失败", e.getMessage());
        }
    }
    
    /**
     * 创建分类
     */
    @PostMapping("/categories")
    public ApiResponse<CategoryVO> createCategory(@Valid @RequestBody CategoryDTO categoryDTO) {
        try {
            CategoryVO category = blogService.createCategory(categoryDTO);
            return ApiResponse.success(category, "分类创建成功");
        } catch (RuntimeException e) {
            log.error("创建分类失败: {}", e.getMessage());
            if (e.getMessage().contains("已存在")) {
                return ApiResponse.error("DUPLICATE_ENTRY", e.getMessage());
            }
            return ApiResponse.error("VALIDATION_ERROR", e.getMessage());
        } catch (Exception e) {
            log.error("创建分类失败: {}", e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "创建分类失败", e.getMessage());
        }
    }
    
    /**
     * 更新分类
     */
    @PutMapping("/categories/{id}")
    public ApiResponse<CategoryVO> updateCategory(@PathVariable String id, @Valid @RequestBody CategoryDTO categoryDTO) {
        try {
            CategoryVO category = blogService.updateCategory(id, categoryDTO);
            return ApiResponse.success(category, "分类更新成功");
        } catch (RuntimeException e) {
            log.error("更新分类失败，ID: {}, 错误: {}", id, e.getMessage());
            if ("分类不存在".equals(e.getMessage())) {
                return ApiResponse.error("NOT_FOUND", "分类不存在");
            }
            if (e.getMessage().contains("已存在")) {
                return ApiResponse.error("DUPLICATE_ENTRY", e.getMessage());
            }
            return ApiResponse.error("VALIDATION_ERROR", e.getMessage());
        } catch (Exception e) {
            log.error("更新分类失败，ID: {}, 错误: {}", id, e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "更新分类失败", e.getMessage());
        }
    }
    
    /**
     * 删除分类
     */
    @DeleteMapping("/categories/{id}")
    public ApiResponse<Void> deleteCategory(@PathVariable String id) {
        try {
            blogService.deleteCategory(id);
            return ApiResponse.success(null, "分类删除成功");
        } catch (RuntimeException e) {
            log.error("删除分类失败，ID: {}, 错误: {}", id, e.getMessage());
            if ("分类不存在".equals(e.getMessage())) {
                return ApiResponse.error("NOT_FOUND", "分类不存在");
            }
            return ApiResponse.error("VALIDATION_ERROR", e.getMessage());
        } catch (Exception e) {
            log.error("删除分类失败，ID: {}, 错误: {}", id, e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "删除分类失败", e.getMessage());
        }
    }
    
    // ========== 文件上传接口 ==========
    
    /**
     * 上传Markdown文件
     */
    @PostMapping("/upload/markdown")
    public ApiResponse<PostVO> uploadMarkdown(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "category", defaultValue = "default") String category) {

        try {
            PostVO post = blogService.uploadMarkdownFile(file, category);
            return ApiResponse.success(post, "文件上传成功");

        } catch (RuntimeException e) {
            log.error("文件上传失败: {}", e.getMessage());
            if (e.getMessage().contains("文件")) {
                return ApiResponse.error("FILE_TOO_LARGE", e.getMessage());
            }
            if (e.getMessage().contains("格式")) {
                return ApiResponse.error("INVALID_FILE_TYPE", e.getMessage());
            }
            return ApiResponse.error("VALIDATION_ERROR", e.getMessage());
        } catch (Exception e) {
            log.error("文件上传失败: {}", e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "文件上传失败", e.getMessage());
        }
    }
    
    // ========== 认证接口 ==========
    
    /**
     * 验证管理密码
     */
    @PostMapping("/auth/verify")
    public ApiResponse<Map<String, Object>> verifyPassword(@Valid @RequestBody AuthDTO authDTO) {
        try {
            Map<String, Object> data = blogService.verifyPassword(authDTO);
            
            if ((Boolean) data.get("valid")) {
                return ApiResponse.success(data, "密码验证成功");
            } else {
                return ApiResponse.error("INVALID_PASSWORD", "密码错误");
            }
            
        } catch (Exception e) {
            log.error("密码验证失败: {}", e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "密码验证失败", e.getMessage());
        }
    }
    
    // ========== 统计接口 ==========
    
    /**
     * 获取博客统计信息
     */
    @GetMapping("/stats")
    public ApiResponse<StatsVO> getBlogStats() {
        try {
            StatsVO stats = blogService.getBlogStats();
            return ApiResponse.success(stats);
        } catch (Exception e) {
            log.error("获取统计信息失败: {}", e.getMessage());
            return ApiResponse.error("SERVER_ERROR", "获取统计信息失败", e.getMessage());
        }
    }
}
