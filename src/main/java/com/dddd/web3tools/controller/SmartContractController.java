package com.dddd.web3tools.controller;

import com.dddd.web3tools.entity.SmartContract;
import com.dddd.web3tools.repository.SmartContractRepository;
import com.dddd.web3tools.service.SmartContractDiscoveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

/**
 * 智能合约发现控制器
 */
@RestController
@RequestMapping("/api/contracts")
@Slf4j
public class SmartContractController {

    @Autowired
    private SmartContractDiscoveryService discoveryService;
    
    @Autowired
    private SmartContractRepository contractRepository;

    /**
     * 启动合约发现
     */
    @PostMapping("/discovery/start")
    public ResponseEntity<Map<String, Object>> startDiscovery() {
        try {
            discoveryService.startMonitoring();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "智能合约发现服务已启动",
                "status", discoveryService.isMonitoring()
            ));
        } catch (Exception e) {
            log.error("启动合约发现失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "启动合约发现失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 停止合约发现
     */
    @PostMapping("/discovery/stop")
    public ResponseEntity<Map<String, Object>> stopDiscovery() {
        try {
            discoveryService.stopMonitoring();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "智能合约发现服务已停止",
                "status", discoveryService.isMonitoring()
            ));
        } catch (Exception e) {
            log.error("停止合约发现失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "停止合约发现失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取发现状态
     */
    @GetMapping("/discovery/status")
    public ResponseEntity<Map<String, Object>> getDiscoveryStatus() {
        try {
            SmartContractDiscoveryService.MonitorStats stats = discoveryService.getMonitorStats();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", Map.of(
                    "isRunning", stats.isRunning(),
                    "totalContractsDiscovered", stats.getTotalContractsDiscovered(),
                    "activeContracts24h", stats.getActiveContracts24h(),
                    "filteredContracts", stats.getFilteredContracts(),
                    "lastProcessedBlock", stats.getLastProcessedBlock(),
                    "startTime", stats.getStartTime(),
                    "processingErrors", stats.getProcessingErrors()
                )
            ));
        } catch (Exception e) {
            log.error("获取发现状态失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取发现状态失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 手动处理指定区块
     */
    @PostMapping("/discovery/process-block/{blockNumber}")
    public ResponseEntity<Map<String, Object>> processBlock(@PathVariable String blockNumber) {
        try {
            BigInteger blockNum = new BigInteger(blockNumber);
            discoveryService.processBlockForContracts(blockNum);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "区块处理完成",
                "blockNumber", blockNumber
            ));
        } catch (Exception e) {
            log.error("处理区块失败: {}", blockNumber, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "处理区块失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取高分合约列表
     */
    @GetMapping("/top")
    public ResponseEntity<Map<String, Object>> getTopContracts(
            @RequestParam(defaultValue = "20") int limit) {
        try {
            List<SmartContract> contracts = discoveryService.getTopContracts(limit);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", contracts,
                "count", contracts.size()
            ));
        } catch (Exception e) {
            log.error("获取高分合约失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取高分合约失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取最近发现的合约
     */
    @GetMapping("/recent")
    public ResponseEntity<Map<String, Object>> getRecentContracts(
            @RequestParam(defaultValue = "24") int hours,
            @RequestParam(defaultValue = "20") int limit) {
        try {
            List<SmartContract> contracts = discoveryService.getRecentContracts(hours, limit);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", contracts,
                "count", contracts.size(),
                "hours", hours
            ));
        } catch (Exception e) {
            log.error("获取最近合约失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取最近合约失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 根据地址查询合约详情
     */
    @GetMapping("/address/{contractAddress}")
    public ResponseEntity<Map<String, Object>> getContractByAddress(@PathVariable String contractAddress) {
        try {
            return contractRepository.findByContractAddress(contractAddress)
                .map(contract -> ResponseEntity.ok(Map.of(
                    "success", true,
                    "data", contract
                )))
                .orElse(ResponseEntity.ok(Map.of(
                    "success", false,
                    "message", "合约不存在"
                )));
        } catch (Exception e) {
            log.error("查询合约失败: {}", contractAddress, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "查询合约失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取活跃合约列表
     */
    @GetMapping("/active")
    public ResponseEntity<Map<String, Object>> getActiveContracts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<SmartContract> contracts = contractRepository.findActiveContracts(pageable);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", contracts.getContent(),
                "totalElements", contracts.getTotalElements(),
                "totalPages", contracts.getTotalPages(),
                "currentPage", page,
                "pageSize", size
            ));
        } catch (Exception e) {
            log.error("获取活跃合约失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取活跃合约失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 根据评分范围查询合约
     */
    @GetMapping("/score-range")
    public ResponseEntity<Map<String, Object>> getContractsByScoreRange(
            @RequestParam BigDecimal minScore,
            @RequestParam BigDecimal maxScore,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<SmartContract> contracts = contractRepository.findByScoreRange(minScore, maxScore, pageable);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", contracts.getContent(),
                "totalElements", contracts.getTotalElements(),
                "totalPages", contracts.getTotalPages(),
                "currentPage", page,
                "pageSize", size,
                "scoreRange", Map.of("min", minScore, "max", maxScore)
            ));
        } catch (Exception e) {
            log.error("根据评分查询合约失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "根据评分查询合约失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取代币合约列表
     */
    @GetMapping("/tokens")
    public ResponseEntity<Map<String, Object>> getTokenContracts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<SmartContract> contracts = contractRepository.findByIsTokenTrueAndIsFilteredFalseOrderByScoreDesc(pageable);
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", contracts.getContent(),
                "totalElements", contracts.getTotalElements(),
                "totalPages", contracts.getTotalPages(),
                "currentPage", page,
                "pageSize", size
            ));
        } catch (Exception e) {
            log.error("获取代币合约失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取代币合约失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 手动更新合约统计
     */
    @PostMapping("/update-stats/{contractAddress}")
    public ResponseEntity<Map<String, Object>> updateContractStats(@PathVariable String contractAddress) {
        try {
            discoveryService.updateContractStats(contractAddress);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "合约统计更新完成",
                "contractAddress", contractAddress
            ));
        } catch (Exception e) {
            log.error("更新合约统计失败: {}", contractAddress, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "更新合约统计失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 获取统计概览
     */
    @GetMapping("/stats/overview")
    public ResponseEntity<Map<String, Object>> getStatsOverview() {
        try {
            long totalContracts = contractRepository.countByIsFilteredFalse();
            long tokenContracts = contractRepository.countByIsTokenTrueAndIsFilteredFalse();
            Object[] scoreDistribution = contractRepository.getScoreDistribution();
            List<Object[]> filterReasons = contractRepository.getFilterReasonStats();
            
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", Map.of(
                    "totalContracts", totalContracts,
                    "tokenContracts", tokenContracts,
                    "scoreDistribution", Map.of(
                        "excellent", scoreDistribution[0],
                        "good", scoreDistribution[1],
                        "average", scoreDistribution[2],
                        "poor", scoreDistribution[3]
                    ),
                    "filterReasons", filterReasons
                )
            ));
        } catch (Exception e) {
            log.error("获取统计概览失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "message", "获取统计概览失败: " + e.getMessage()
            ));
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        return ResponseEntity.ok(Map.of(
            "success", true,
            "message", "智能合约发现服务运行正常",
            "timestamp", System.currentTimeMillis()
        ));
    }
}
