package com.dddd.web3tools.controller;

import com.dddd.web3tools.job.WalletBalanceRefreshJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 */
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
@Slf4j
public class TestController {

    private final WalletBalanceRefreshJob walletBalanceRefreshJob;

    /**
     * 手动触发钱包余额刷新任务
     */
    @PostMapping("/refresh-wallet-balances")
    public String triggerWalletBalanceRefresh() {
        try {
            log.info("手动触发钱包余额刷新任务");
            walletBalanceRefreshJob.refreshAllWalletBalances();
            return "钱包余额刷新任务已触发";
        } catch (Exception e) {
            log.error("手动触发钱包余额刷新任务失败: {}", e.getMessage(), e);
            return "钱包余额刷新任务触发失败: " + e.getMessage();
        }
    }
}
