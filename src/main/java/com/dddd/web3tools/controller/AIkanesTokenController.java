package com.dddd.web3tools.controller;

import com.dddd.web3tools.dto.AikanesTokenDTO;
import com.dddd.web3tools.entity.AIkanesToken;
import com.dddd.web3tools.service.AIkanesTokenService;
import com.dddd.web3tools.service.impl.AIkanesTokenServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/aikanesTokens")
public class AIkanesTokenController {

    private final AIkanesTokenService tokenService;

    public AIkanesTokenController(AIkanesTokenServiceImpl tokenService) {
        this.tokenService = tokenService;
    }

    @GetMapping("/active-with-mempool")
    public List<AIkanesToken> getActiveTokensWithMempoolActivity() {
        return tokenService.getActiveTokensWithMempoolActivity();
    }

    @PostMapping("/search")
    public ResponseEntity<AIkanesToken> searchToken(@RequestBody AikanesTokenDTO dto) {
        if (dto.getKeyword() == null) {
            return ResponseEntity.badRequest().build();
        }
        try {
            AIkanesToken token = tokenService.getTokenByIdOrName(dto.getKeyword());
            return ResponseEntity.ok(token);
        } catch (Exception e) {
            log.error(e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
}
