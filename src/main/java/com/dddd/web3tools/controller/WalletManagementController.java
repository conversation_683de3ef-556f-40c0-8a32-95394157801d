package com.dddd.web3tools.controller;

import com.dddd.web3tools.dto.*;
import com.dddd.web3tools.service.WalletManagementService;
import com.dddd.web3tools.vo.ApiResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 钱包管理控制器
 */
@RestController
@RequestMapping("/v1")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class WalletManagementController {

    private final WalletManagementService walletManagementService;

    // ==================== 钱包管理接口 ====================

    /**
     * 获取用户所有钱包（通过钱包地址）
     */
    @GetMapping("/wallets")
    public ApiResponse<UserWalletDTO.WalletsResponse> getUserWallets(
            @RequestParam String userAddress) {

        try {
            UserWalletDTO.WalletsResponse response = walletManagementService.getUserWalletsByAddress(userAddress);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取钱包列表失败: address={}, error={}", userAddress, e.getMessage());
            return ApiResponse.error("GET_WALLETS_FAILED", e.getMessage());
        }
    }

    /**
     * 添加钱包（通过钱包地址）
     */
    @PostMapping("/wallets")
    public ApiResponse<UserWalletDTO.WalletInfo> addWallet(@Valid @RequestBody UserWalletDTO.AddWalletRequest request) {

        try {
            UserWalletDTO.WalletInfo response = walletManagementService.addWalletByAddress(request.getUserAddress(), request);
            return ApiResponse.success(response, "钱包添加成功");
        } catch (Exception e) {
            log.error("添加钱包失败: userAddress={}, error={}", request.getUserAddress(), e.getMessage());
            return ApiResponse.error("ADD_WALLET_FAILED", e.getMessage());
        }
    }

    /**
     * 更新钱包信息（通过钱包地址）
     */
    @PutMapping("/wallets/{walletAddress}")
    public ApiResponse<UserWalletDTO.WalletInfo> updateWallet(
            @RequestParam String userAddress,
            @PathVariable String walletAddress,
            @Valid @RequestBody UserWalletDTO.UpdateWalletRequest request) {

        try {
            UserWalletDTO.WalletInfo response = walletManagementService.updateWalletByAddress(userAddress, walletAddress, request);
            return ApiResponse.success(response, "钱包更新成功");
        } catch (Exception e) {
            log.error("更新钱包失败: userAddress={}, walletAddress={}, error={}", userAddress, walletAddress, e.getMessage());
            return ApiResponse.error("UPDATE_WALLET_FAILED", e.getMessage());
        }
    }

    /**
     * 删除钱包（通过钱包地址）
     */
    @DeleteMapping("/wallets/{walletAddress}")
    public ApiResponse<Void> deleteWallet(
            @RequestParam String userAddress,
            @PathVariable String walletAddress) {

        try {
            walletManagementService.deleteWalletByAddress(userAddress, walletAddress);
            return ApiResponse.success(null, "钱包删除成功");
        } catch (Exception e) {
            log.error("删除钱包失败: userAddress={}, walletAddress={}, error={}", userAddress, walletAddress, e.getMessage());
            return ApiResponse.error("DELETE_WALLET_FAILED", e.getMessage());
        }
    }

    /**
     * 批量删除钱包（通过钱包地址）
     */
    @DeleteMapping("/wallets/batch")
    public ApiResponse<UserWalletDTO.BatchDeleteResponse> batchDeleteWallets(
            @RequestParam String userAddress,
            @Valid @RequestBody UserWalletDTO.BatchDeleteRequest request) {

        try {
            UserWalletDTO.BatchDeleteResponse response = walletManagementService.batchDeleteWalletsByAddress(userAddress, request);
            return ApiResponse.success(response, "批量删除完成");
        } catch (Exception e) {
            log.error("批量删除钱包失败: userAddress={}, error={}", userAddress, e.getMessage());
            return ApiResponse.error("BATCH_DELETE_FAILED", e.getMessage());
        }
    }

    // ==================== 资产数据查询接口 ====================

    /**
     * 刷新钱包余额（从缓存和数据库获取）
     */
    @PostMapping("/wallets/{walletAddress}/refresh")
    public ApiResponse<UserWalletDTO.RefreshResponse> refreshWalletBalance(
            @RequestParam String userAddress,
            @PathVariable String walletAddress) {

        try {
            UserWalletDTO.RefreshResponse response = walletManagementService.refreshWalletBalanceByAddress(userAddress, walletAddress);
            return ApiResponse.success(response, "余额刷新成功");
        } catch (Exception e) {
            log.error("刷新钱包余额失败: userAddress={}, walletAddress={}, error={}", userAddress, walletAddress, e.getMessage());
            return ApiResponse.error("REFRESH_WALLET_FAILED", e.getMessage());
        }
    }

    /**
     * 批量刷新所有钱包（从缓存和数据库获取）
     */
    @PostMapping("/wallets/refresh-all")
    public ApiResponse<UserWalletDTO.RefreshAllResponse> refreshAllWallets(
            @RequestParam String userAddress) {

        try {
            UserWalletDTO.RefreshAllResponse response = walletManagementService.refreshAllWalletsByAddress(userAddress);
            return ApiResponse.success(response, "所有钱包刷新完成");
        } catch (Exception e) {
            log.error("刷新所有钱包失败: address={}, error={}", userAddress, e.getMessage());
            return ApiResponse.error("REFRESH_ALL_FAILED", e.getMessage());
        }
    }

    // ==================== 统计数据接口 ====================

    /**
     * 获取资产总览（通过钱包地址）
     */
    @GetMapping("/wallets/overview")
    public ApiResponse<WalletOverviewDTO> getWalletOverview(
            @RequestParam String userAddress) {

        try {
            WalletOverviewDTO response = walletManagementService.getWalletOverviewByAddress(userAddress);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取资产总览失败: address={}, error={}", userAddress, e.getMessage());
            return ApiResponse.error("GET_OVERVIEW_FAILED", e.getMessage());
        }
    }

    /**
     * 获取资产趋势数据
     */
    @GetMapping("/wallets/trends")
    public ApiResponse<WalletTrendDTO> getWalletTrends(
            @RequestParam String userAddress,
            @RequestParam(defaultValue = "7d") String timeRange) {

        try {
            WalletTrendDTO response = walletManagementService.getWalletTrends(userAddress, timeRange);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取资产趋势失败: {}", e.getMessage());
            return ApiResponse.error("GET_TRENDS_FAILED", e.getMessage());
        }
    }

    // ==================== 市场数据接口 ====================

    /**
     * 获取代币价格
     */
    @GetMapping("/market/prices")
    public ApiResponse<MarketDataDTO> getTokenPrices(@RequestParam String symbols) {
        
        try {
            MarketDataDTO response = walletManagementService.getTokenPrices(symbols);
            return ApiResponse.success(response);
        } catch (Exception e) {
            log.error("获取代币价格失败: {}", e.getMessage());
            return ApiResponse.error("GET_PRICES_FAILED", e.getMessage());
        }
    }


}
