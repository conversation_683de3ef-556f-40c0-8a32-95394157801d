package com.dddd.web3tools.controller;

import com.dddd.web3tools.dto.TweetAnalysisResult;
import com.dddd.web3tools.service.GeminiService;
import com.dddd.web3tools.service.impl.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 推文分析测试控制器
 */
@RestController
@RequestMapping("/api/tweet-analysis")
@Slf4j
public class TweetAnalysisController {

    @Autowired
    private GeminiService geminiService;

    @Autowired
    private NotificationService notificationService;

    /**
     * 测试单条推文分析
     */
    @PostMapping("/analyze")
    public ResponseEntity<TweetAnalysisResult> analyzeTweet(@RequestBody Map<String, String> request) {
        try {
            String tweetContent = request.get("content");
            if (tweetContent == null || tweetContent.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            log.info("收到推文分析请求: {}", tweetContent.substring(0, Math.min(100, tweetContent.length())));
            
            TweetAnalysisResult result = geminiService.analyzeTweetForMint(tweetContent);
            
            log.info("分析完成，结果: shouldNotify={}, confidence={}", 
                result.isShouldNotify(), result.getConfidence());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("分析推文时发生错误", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 测试批量推文分析
     */
    @PostMapping("/batch-analyze")
    public ResponseEntity<Map<String, Object>> batchAnalyzeTweets(@RequestBody Map<String, List<String>> request) {
        try {
            List<String> tweetContents = request.get("contents");
            if (tweetContents == null || tweetContents.isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            log.info("收到批量推文分析请求，数量: {}", tweetContents.size());
            
            List<String> qualifiedTweets = geminiService.batchAnalyzeTweets(tweetContents);
            
            Map<String, Object> response = new HashMap<>();
            response.put("totalAnalyzed", tweetContents.size());
            response.put("qualifiedCount", qualifiedTweets.size());
            response.put("qualifiedTweets", qualifiedTweets);
            
            log.info("批量分析完成，符合条件的推文数量: {}/{}", qualifiedTweets.size(), tweetContents.size());
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("批量分析推文时发生错误", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 测试推文分析并发送邮件
     */
    @PostMapping("/analyze-and-notify")
    public ResponseEntity<Map<String, Object>> analyzeTweetAndNotify(@RequestBody Map<String, String> request) {
        try {
            String tweetContent = request.get("content");
            if (tweetContent == null || tweetContent.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            log.info("收到推文分析并通知请求");
            
            TweetAnalysisResult result = geminiService.analyzeTweetForMint(tweetContent);
            
            Map<String, Object> response = new HashMap<>();
            response.put("analysisResult", result);
            
            if (result.isShouldNotify()) {
                // 构建邮件内容
                StringBuilder emailContent = new StringBuilder();
                emailContent.append("🚀 发现mint机会！\n\n");
                emailContent.append("推文内容：\n").append(tweetContent).append("\n\n");
                emailContent.append("分析结果：\n");
                emailContent.append("- 包含mint内容：").append(result.isContainsMintContent() ? "✅" : "❌").append("\n");
                emailContent.append("- 包含网址：").append(result.isContainsUrl() ? "✅" : "❌").append("\n");
                emailContent.append("- 置信度：").append(result.getConfidence()).append("%\n");
                
                if (!result.getExtractedUrls().isEmpty()) {
                    emailContent.append("- 发现链接：\n");
                    for (String url : result.getExtractedUrls()) {
                        emailContent.append("  • ").append(url).append("\n");
                    }
                }
                
                if (!result.getMintKeywords().isEmpty()) {
                    emailContent.append("- 关键词：").append(String.join(", ", result.getMintKeywords())).append("\n");
                }
                
                emailContent.append("- AI分析：").append(result.getAnalysisDetails()).append("\n");
                
                // 发送邮件
                notificationService.sendNotification(
                    Collections.singletonList(emailContent.toString()), 
                    "🚀 Mint机会提醒 - 测试"
                );
                
                response.put("emailSent", true);
                response.put("emailContent", emailContent.toString());
                
                log.info("符合条件的推文，已发送邮件通知");
            } else {
                response.put("emailSent", false);
                response.put("reason", result.getReason());
                
                log.info("推文不符合通知条件: {}", result.getReason());
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("分析推文并发送通知时发生错误", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 快速检查推文是否包含mint内容
     */
    @PostMapping("/quick-check")
    public ResponseEntity<Map<String, Object>> quickCheckMintContent(@RequestBody Map<String, String> request) {
        try {
            String tweetContent = request.get("content");
            if (tweetContent == null || tweetContent.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            boolean containsMint = geminiService.quickCheckMintContent(tweetContent);

            Map<String, Object> response = new HashMap<>();
            response.put("containsMint", containsMint);
            response.put("content", tweetContent);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("快速检查推文时发生错误", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 测试基础功能 - 不需要请求体
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> testBasicFunction() {
        try {
            log.info("开始测试推文分析基础功能");

            // 测试推文样例
            String testTweet = "🚀 New NFT collection minting now! Get yours at https://example.com/mint #NFT #mint";

            TweetAnalysisResult result = geminiService.analyzeTweetForMint(testTweet);

            Map<String, Object> response = new HashMap<>();
            response.put("testTweet", testTweet);
            response.put("analysisResult", result);
            response.put("status", "success");
            response.put("message", "测试完成");

            log.info("测试完成，结果: shouldNotify={}, confidence={}",
                result.isShouldNotify(), result.getConfidence());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试基础功能时发生错误", e);

            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "测试失败: " + e.getMessage());
            errorResponse.put("error", e.getClass().getSimpleName());

            return ResponseEntity.ok(errorResponse);
        }
    }
}
