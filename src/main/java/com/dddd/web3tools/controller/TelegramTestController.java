package com.dddd.web3tools.controller;

import com.dddd.web3tools.service.TelegramService;
import com.dddd.web3tools.service.impl.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * Telegram测试控制器
 * 用于测试Telegram推送功能
 */
@Slf4j
@RestController
@RequestMapping("/api/telegram")
public class TelegramTestController {

    @Autowired
    private TelegramService telegramService;

    @Autowired
    private NotificationService notificationService;

    /**
     * 测试发送单条Telegram消息
     */
    @PostMapping("/test/single")
    public ResponseEntity<Map<String, Object>> testSingleMessage(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String message = request.getOrDefault("message", "这是一条测试消息 🚀");
            
            if (telegramService.isAvailable()) {
                telegramService.sendMessage(message);
                response.put("success", true);
                response.put("message", "Telegram消息发送成功");
                log.info("测试Telegram单条消息发送成功");
            } else {
                response.put("success", false);
                response.put("message", "Telegram服务不可用，请检查配置");
                log.warn("Telegram服务不可用");
            }
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "发送失败: " + e.getMessage());
            log.error("测试Telegram消息发送失败", e);
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 测试发送多条Telegram消息
     */
    @PostMapping("/test/multiple")
    public ResponseEntity<Map<String, Object>> testMultipleMessages() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (telegramService.isAvailable()) {
                telegramService.sendMessages(
                    Arrays.asList(
                        "作者: TestUser (粉丝数: 10000)\n内容: 这是第一条测试推文 🚀\n发布时间: 2024-01-01 12:00:00",
                        "作者: AnotherUser (粉丝数: 5000)\n内容: 这是第二条测试推文 💎\n发布时间: 2024-01-01 12:05:00",
                        "作者: CryptoExpert (粉丝数: 50000)\n内容: 重要的mint机会来了！不要错过 🔥\n发布时间: 2024-01-01 12:10:00"
                    ),
                    "🚀 测试推文通知"
                );
                response.put("success", true);
                response.put("message", "Telegram多条消息发送成功");
                log.info("测试Telegram多条消息发送成功");
            } else {
                response.put("success", false);
                response.put("message", "Telegram服务不可用，请检查配置");
                log.warn("Telegram服务不可用");
            }
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "发送失败: " + e.getMessage());
            log.error("测试Telegram多条消息发送失败", e);
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 测试完整的通知流程（邮件+Telegram）
     */
    @PostMapping("/test/notification")
    public ResponseEntity<Map<String, Object>> testNotificationFlow() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            notificationService.sendNotification(
                Arrays.asList(
                    "作者: TestBot (粉丝数: 1000)\n内容: 测试完整通知流程 📧📱\n发布时间: 2024-01-01 12:00:00"
                ),
                "🔔 完整通知测试"
            );
            
            response.put("success", true);
            response.put("message", "完整通知流程测试成功（邮件+Telegram）");
            log.info("完整通知流程测试成功");
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "通知流程测试失败: " + e.getMessage());
            log.error("完整通知流程测试失败", e);
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * 检查Telegram服务状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean available = telegramService.isAvailable();
            response.put("available", available);
            response.put("message", available ? "Telegram服务正常" : "Telegram服务不可用");
            
        } catch (Exception e) {
            response.put("available", false);
            response.put("message", "检查状态失败: " + e.getMessage());
            log.error("检查Telegram服务状态失败", e);
        }
        
        return ResponseEntity.ok(response);
    }
}
