package com.dddd.web3tools.controller;

import com.dddd.web3tools.entity.Tweet;
import com.dddd.web3tools.entity.TwitterUser;
import com.dddd.web3tools.service.TwitterUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/twitter-users")
public class TwitterUserController {

    @Autowired
    private TwitterUserService twitterUserService;

    @PostMapping("/import")
    public ResponseEntity<String> importUsersAndTweets(@RequestParam("listId") String listId) {
        try {
            twitterUserService.importTwitterUsersAndTweets(listId);
            return ResponseEntity.ok("数据导入成功");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导入失败: " + e.getMessage());
        }
    }
    @PostMapping("/update")
    public ResponseEntity<?> updateTwitterUser(@RequestBody TwitterUser updateRequest) {
        try {
            TwitterUser updatedUser = twitterUserService.updateUser(updateRequest);
            return ResponseEntity.ok(updatedUser);
        } catch (RuntimeException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
        }
    }

    @GetMapping("/tweets/{screenName}")
    public ResponseEntity<List<Tweet>> getTweetsByScreenName(
            @PathVariable String screenName) {
        List<Tweet> tweets = twitterUserService.getTweetsByScreenName(screenName);
        return ResponseEntity.ok(tweets);
    }

    @GetMapping("/search")
    public ResponseEntity<Page<TwitterUser>> searchUsers(
            @RequestParam(required = false) String twitterName,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        Page<TwitterUser> users = twitterUserService.searchUsers(twitterName, page, size);
        return ResponseEntity.ok(users);
    }


    @PostMapping("/import-by-username")
    public ResponseEntity<String> importUserAndTweetsByUsername(
            @RequestParam("username") String username) {
        try {
            twitterUserService.importUserAndTweetsByUsername(username);
            return ResponseEntity.ok("用户及推文数据导入成功");
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("导入失败: " + e.getMessage());
        }
    }
}
