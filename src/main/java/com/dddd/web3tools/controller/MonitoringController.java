package com.dddd.web3tools.controller;

import com.dddd.web3tools.dto.common.ApiResponse;
import com.dddd.web3tools.dto.monitoring.*;
import com.dddd.web3tools.service.MonitoringService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 链上异常监控控制器
 */
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
@Slf4j
public class MonitoringController {
    
    private final MonitoringService monitoringService;
    
    @GetMapping("/contracts/monitoring")
    public ApiResponse<ContractMonitoringListDTO> getContractMonitoring(
            @RequestParam(defaultValue = "24h") String timeRange,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "interactions") String sortBy,
            @RequestParam(defaultValue = "desc") String sortOrder,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "50") int limit) {
        
        try {
            ContractMonitoringListDTO result = monitoringService.getContractMonitoringData(
                timeRange, category, sortBy, sortOrder, page, limit);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取合约监控数据失败", e);
            return ApiResponse.error(500, "获取合约监控数据失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/contracts/{contractId}/history")
    public ApiResponse<ContractHistoryDTO> getContractHistory(
            @PathVariable Long contractId,
            @RequestParam(defaultValue = "24h") String timeRange,
            @RequestParam(defaultValue = "1h") String interval) {
        
        try {
            ContractHistoryDTO result = monitoringService.getContractHistory(contractId, timeRange, interval);
            return ApiResponse.success(result);
        } catch (RuntimeException e) {
            log.error("获取合约历史数据失败, contractId: {}", contractId, e);
            return ApiResponse.error(404, "合约不存在");
        } catch (Exception e) {
            log.error("获取合约历史数据失败, contractId: {}", contractId, e);
            return ApiResponse.error(500, "获取合约历史数据失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/stats/overview")
    public ApiResponse<SystemOverviewDTO> getSystemOverview(
            @RequestParam(defaultValue = "24h") String timeRange) {
        
        try {
            SystemOverviewDTO result = monitoringService.getSystemOverview(timeRange);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取系统统计概览失败", e);
            return ApiResponse.error(500, "获取系统统计概览失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/contracts/{contractId}/details")
    public ApiResponse<ContractDetailDTO> getContractDetails(
            @PathVariable Long contractId) {
        
        try {
            ContractDetailDTO result = monitoringService.getContractDetails(contractId);
            return ApiResponse.success(result);
        } catch (RuntimeException e) {
            log.error("获取合约详细信息失败, contractId: {}", contractId, e);
            return ApiResponse.error(404, "合约不存在");
        } catch (Exception e) {
            log.error("获取合约详细信息失败, contractId: {}", contractId, e);
            return ApiResponse.error(500, "获取合约详细信息失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/contracts/search")
    public ApiResponse<ContractSearchDTO> searchContracts(
            @RequestParam String query,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "20") int limit) {
        
        try {
            if (query == null || query.trim().isEmpty()) {
                return ApiResponse.error(400, "搜索关键词不能为空");
            }
            
            ContractSearchDTO result = monitoringService.searchContracts(query.trim(), category, limit);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("搜索合约失败, query: {}", query, e);
            return ApiResponse.error(500, "搜索合约失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/anomaly/rules")
    public ApiResponse<AnomalyRulesDTO> getAnomalyRules() {
        try {
            // 模拟异常检测规则数据
            AnomalyRulesDTO.AnomalyRuleDTO rule1 = new AnomalyRulesDTO.AnomalyRuleDTO(
                1L, "交互量突增检测", "检测24小时内交互量增长超过阈值的合约", 
                java.math.BigDecimal.valueOf(50.0), java.math.BigDecimal.valueOf(0.3), true);
            
            AnomalyRulesDTO.AnomalyRuleDTO rule2 = new AnomalyRulesDTO.AnomalyRuleDTO(
                2L, "Gas费用异常检测", "检测平均Gas费用异常高或低的合约", 
                java.math.BigDecimal.valueOf(200.0), java.math.BigDecimal.valueOf(0.2), true);
            
            AnomalyRulesDTO.AnomalyRuleDTO rule3 = new AnomalyRulesDTO.AnomalyRuleDTO(
                3L, "新用户涌入检测", "检测新用户增长率异常的合约", 
                java.math.BigDecimal.valueOf(100.0), java.math.BigDecimal.valueOf(0.25), true);
            
            AnomalyRulesDTO result = new AnomalyRulesDTO(
                java.util.Arrays.asList(rule1, rule2, rule3), 
                "weighted_average", 300);
                
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取异常检测规则失败", e);
            return ApiResponse.error(500, "获取异常检测规则失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/config/monitoring")
    public ApiResponse<MonitoringConfigDTO> getMonitoringConfig() {
        try {
            // 模拟监控配置数据
            MonitoringConfigDTO.MonitoringStatusDTO monitoring = new MonitoringConfigDTO.MonitoringStatusDTO(
                "active", 30, 90, 
                java.util.Arrays.asList("ethereum", "bsc", "polygon"), 
                "ethereum");
            
            java.util.Map<String, Integer> thresholds = new java.util.HashMap<>();
            thresholds.put("highAnomaly", 80);
            thresholds.put("mediumAnomaly", 60);
            thresholds.put("lowAnomaly", 40);
            
            MonitoringConfigDTO.AlertsConfigDTO alerts = new MonitoringConfigDTO.AlertsConfigDTO(
                true, thresholds, java.util.Arrays.asList("webhook", "email"));
            
            MonitoringConfigDTO.RateLimitDTO rateLimit = new MonitoringConfigDTO.RateLimitDTO(
                1000, 50000);
            
            MonitoringConfigDTO result = new MonitoringConfigDTO(monitoring, alerts, rateLimit);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取监控配置失败", e);
            return ApiResponse.error(500, "获取监控配置失败: " + e.getMessage());
        }
    }
    
    @PostMapping("/alerts/create")
    public ApiResponse<AlertDTO.CreateAlertResponseDTO> createAlert(
            @RequestBody AlertDTO.CreateAlertRequestDTO request) {
        try {
            // 简单验证
            if (request.getContractId() == null) {
                return ApiResponse.error(400, "合约ID不能为空");
            }
            if (request.getThreshold() == null) {
                return ApiResponse.error(400, "阈值不能为空");
            }
            
            // 模拟创建警报
            String alertId = "alert_" + System.currentTimeMillis();
            AlertDTO.CreateAlertResponseDTO result = new AlertDTO.CreateAlertResponseDTO(
                alertId, request.getContractId(), request.getAlertType(),
                request.getCondition(), request.getThreshold(), "active",
                java.time.LocalDateTime.now());
            
            return ApiResponse.success("警报创建成功", result);
        } catch (Exception e) {
            log.error("创建监控警报失败", e);
            return ApiResponse.error(500, "创建监控警报失败: " + e.getMessage());
        }
    }
    
    @GetMapping("/alerts/history")
    public ApiResponse<AlertDTO.AlertHistoryListDTO> getAlertHistory(
            @RequestParam(required = false) Long contractId,
            @RequestParam(required = false) String alertType,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int limit) {
        
        try {
            // 模拟历史警报数据
            AlertDTO.AlertHistoryDTO alert1 = new AlertDTO.AlertHistoryDTO(
                "alert_123456", 1L, "Uniswap Token", "anomaly_score",
                java.math.BigDecimal.valueOf(80), java.math.BigDecimal.valueOf(95),
                java.time.LocalDateTime.now().minusHours(1),
                "异常分数达到95，超过阈值80", "high", false);
            
            java.util.List<AlertDTO.AlertHistoryDTO> alerts = java.util.Arrays.asList(alert1);
            com.dddd.web3tools.dto.common.PaginationDTO pagination = 
                new com.dddd.web3tools.dto.common.PaginationDTO(page, limit, 1);
            
            AlertDTO.AlertHistoryListDTO result = new AlertDTO.AlertHistoryListDTO(alerts, pagination);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取历史警报失败", e);
            return ApiResponse.error(500, "获取历史警报失败: " + e.getMessage());
        }
    }
}