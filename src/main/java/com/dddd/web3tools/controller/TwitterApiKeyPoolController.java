package com.dddd.web3tools.controller;

import com.dddd.web3tools.service.TwitterApiKeyPoolService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Twitter API Key池管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/twitter-key-pool")
public class TwitterApiKeyPoolController {

    @Autowired
    private TwitterApiKeyPoolService keyPoolService;

    /**
     * 获取API Key池状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getPoolStatus() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("status", keyPoolService.getPoolStatus());
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取API Key池状态失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "获取状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 获取当前使用的API Key（掩码显示）
     */
    @GetMapping("/current-key")
    public ResponseEntity<Map<String, Object>> getCurrentKey() {
        try {
            String currentKey = keyPoolService.getCurrentApiKey();
            Map<String, Object> response = new HashMap<>();

            if (currentKey != null) {
                response.put("currentKey", keyPoolService.maskApiKey(currentKey));
                response.put("usage", keyPoolService.getKeyUsageCount(currentKey));
            } else {
                response.put("currentKey", null);
                response.put("usage", 0);
            }

            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("获取当前API Key失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "获取当前Key失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 重置所有API Key的使用计数和失败状态
     */
    @PostMapping("/reset")
    public ResponseEntity<Map<String, Object>> resetAllKeyUsage() {
        try {
            keyPoolService.resetAllKeyUsage();
            Map<String, Object> response = new HashMap<>();
            response.put("message", "所有API Key使用计数和失败状态已重置");
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("重置API Key使用计数失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "重置失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 测试API Key池功能
     */
    @PostMapping("/test")
    public ResponseEntity<Map<String, Object>> testKeyPool() {
        try {
            Map<String, Object> response = new HashMap<>();

            // 获取当前key
            String currentKey = keyPoolService.getCurrentApiKey();
            if (currentKey != null) {
                // 模拟使用
                keyPoolService.recordKeyUsage(currentKey);

                response.put("message", "测试成功");
                response.put("usedKey", keyPoolService.maskApiKey(currentKey));
                response.put("newUsage", keyPoolService.getKeyUsageCount(currentKey));
            } else {
                response.put("message", "无可用的API Key");
            }

            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("测试API Key池失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "测试失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 清除指定API Key的失败状态
     */
    @PostMapping("/clear-failure")
    public ResponseEntity<Map<String, Object>> clearFailureStatus(@RequestParam String apiKey) {
        try {
            keyPoolService.clearKeyFailureStatus(apiKey);
            Map<String, Object> response = new HashMap<>();
            response.put("message", "已清除API Key失败状态: " + keyPoolService.maskApiKey(apiKey));
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("清除API Key失败状态失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "清除失败状态失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    /**
     * 手动切换到下一个可用的API Key
     */
    @PostMapping("/switch-key")
    public ResponseEntity<Map<String, Object>> switchToNextKey() {
        try {
            String oldKey = keyPoolService.getCurrentApiKey();
            String newKey = keyPoolService.getNextAvailableKey(null); // 不标记当前key为失败，只是切换

            Map<String, Object> response = new HashMap<>();
            response.put("message", "已切换到下一个可用的API Key");
            response.put("oldKey", oldKey != null ? keyPoolService.maskApiKey(oldKey) : null);
            response.put("newKey", newKey != null ? keyPoolService.maskApiKey(newKey) : null);
            response.put("timestamp", System.currentTimeMillis());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("切换API Key失败", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "切换Key失败: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }
}
