package com.dddd.web3tools.controller;

import com.dddd.web3tools.entity.Keyword;
import com.dddd.web3tools.service.impl.KeywordService;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

@RestController
@RequestMapping("/api/keywords")
public class KeywordController {
    private final KeywordService keywordService;

    public KeywordController(KeywordService keywordService) {
        this.keywordService = keywordService;
    }

    @GetMapping
    public Set<String> getAllKeywords() {
        return keywordService.getAllKeywords();
    }

    @PostMapping
    public Keyword addKeyword(@RequestBody Keyword keyword) {
        return keywordService.addKeyword(keyword.getWord());
    }

    @DeleteMapping("/{id}")
    public void deleteKeyword(@PathVariable Long id) {
        keywordService.deleteKeyword(id);
    }
}