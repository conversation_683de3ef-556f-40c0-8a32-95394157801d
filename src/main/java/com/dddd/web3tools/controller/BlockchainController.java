package com.dddd.web3tools.controller;

import com.dddd.web3tools.repository.BlockchainDataRepository;
import com.dddd.web3tools.service.BlockchainDataService;
import com.dddd.web3tools.vo.GasDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/blockchain")
@Slf4j
public class BlockchainController {

    private final BlockchainDataService blockchainDataService;
    private final BlockchainDataRepository repository;

    public BlockchainController(BlockchainDataService blockchainDataService,
                                BlockchainDataRepository repository) {
        this.blockchainDataService = blockchainDataService;
        this.repository = repository;
    }

    @GetMapping("/gas")
    public ResponseEntity<List<GasDataVO>> getRecent30MinutesGasData() {
        return ResponseEntity.ok(blockchainDataService.getGasData());
    }
}
