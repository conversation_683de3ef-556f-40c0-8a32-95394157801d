package com.dddd.web3tools.controller;

import com.dddd.web3tools.entity.Approval;
import com.dddd.web3tools.service.ApprovalService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/approvals")
public class ApprovalController {

    private final ApprovalService approvalService;

    public ApprovalController(ApprovalService approvalService) {
        this.approvalService = approvalService;
    }

    @PostMapping("/add")
    public Approval createApproval(@RequestBody Approval approval) {
        return approvalService.saveApproval(approval);
    }
}
