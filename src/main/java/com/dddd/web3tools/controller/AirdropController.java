package com.dddd.web3tools.controller;

import com.dddd.web3tools.dto.ProjectDTO;
import com.dddd.web3tools.dto.ProjectStatsDTO;
import com.dddd.web3tools.dto.WalletDTO;
import com.dddd.web3tools.service.AirdropService;
import com.dddd.web3tools.vo.ApiResponse;
import com.dddd.web3tools.vo.PageResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 空投项目管理控制器
 */
@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class AirdropController {

    private final AirdropService airdropService;

    // ==================== 项目管理接口 ====================

    /**
     * 获取项目列表
     */
    @GetMapping("/projects")
    public ApiResponse<PageResponse<ProjectDTO>> getProjects(
            @RequestParam String userAddress,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer limit,
            @RequestParam(required = false) String chain,
            @RequestParam(required = false) String status) {
        
        log.info("获取项目列表请求: userAddress={}, page={}, limit={}, chain={}, status={}", 
                userAddress, page, limit, chain, status);

        ProjectDTO.QueryRequest request = ProjectDTO.QueryRequest.builder()
                .userAddress(userAddress)
                .page(page)
                .limit(limit)
                .chain(chain)
                .status(status)
                .build();

        PageResponse<ProjectDTO> result = airdropService.getProjects(request);
        return ApiResponse.success(result);
    }

    /**
     * 创建项目
     */
    @PostMapping("/projects")
    public ApiResponse<ProjectDTO> createProject(@Valid @RequestBody ProjectDTO.CreateRequest request) {
        log.info("创建项目请求: {}", request.getName());

        ProjectDTO result = airdropService.createProject(request);
        return ApiResponse.success(result, "项目创建成功");
    }

    /**
     * 更新项目
     */
    @PutMapping("/projects/{projectId}")
    public ApiResponse<ProjectDTO> updateProject(
            @PathVariable Long projectId,
            @Valid @RequestBody ProjectDTO.UpdateRequest request) {
        
        log.info("更新项目请求: projectId={}", projectId);

        ProjectDTO result = airdropService.updateProject(projectId, request);
        return ApiResponse.success(result, "项目更新成功");
    }

    /**
     * 删除项目
     */
    @DeleteMapping("/projects/{projectId}")
    public ApiResponse<Void> deleteProject(@PathVariable Long projectId) {
        log.info("删除项目请求: projectId={}", projectId);

        airdropService.deleteProject(projectId);
        return ApiResponse.success(null, "项目删除成功");
    }

    /**
     * 获取项目详情
     */
    @GetMapping("/projects/{projectId}")
    public ApiResponse<ProjectDTO> getProjectById(@PathVariable Long projectId) {
        log.info("获取项目详情请求: projectId={}", projectId);

        ProjectDTO result = airdropService.getProjectById(projectId);
        return ApiResponse.success(result);
    }

    // ==================== 钱包管理接口 ====================

    /**
     * 添加钱包到项目
     */
    @PostMapping("/projects/{projectId}/wallets")
    public ApiResponse<WalletDTO> addWalletToProject(
            @PathVariable Long projectId,
            @Valid @RequestBody WalletDTO.AddRequest request) {
        
        log.info("添加钱包到项目请求: projectId={}, address={}", projectId, request.getAddress());

        WalletDTO result = airdropService.addWalletToProject(projectId, request);
        return ApiResponse.success(result, "钱包添加成功");
    }

    /**
     * 批量导入钱包
     */
    @PostMapping("/projects/{projectId}/wallets/batch")
    public ApiResponse<WalletDTO.BatchImportResponse> batchImportWallets(
            @PathVariable Long projectId,
            @Valid @RequestBody WalletDTO.BatchImportRequest request) {
        
        log.info("批量导入钱包请求: projectId={}, count={}", projectId, request.getWallets().size());

        WalletDTO.BatchImportResponse result = airdropService.batchImportWallets(projectId, request);
        return ApiResponse.success(result, "批量导入完成");
    }

    /**
     * 更新钱包信息
     */
    @PutMapping("/projects/{projectId}/wallets/{walletId}")
    public ApiResponse<WalletDTO> updateWallet(
            @PathVariable Long projectId,
            @PathVariable Long walletId,
            @Valid @RequestBody WalletDTO.UpdateRequest request) {
        
        log.info("更新钱包信息请求: projectId={}, walletId={}", projectId, walletId);

        WalletDTO result = airdropService.updateWallet(projectId, walletId, request);
        return ApiResponse.success(result, "钱包信息更新成功");
    }

    /**
     * 删除钱包
     */
    @DeleteMapping("/projects/{projectId}/wallets/{walletId}")
    public ApiResponse<Void> deleteWallet(
            @PathVariable Long projectId,
            @PathVariable Long walletId) {
        
        log.info("删除钱包请求: projectId={}, walletId={}", projectId, walletId);

        airdropService.deleteWallet(projectId, walletId);
        return ApiResponse.success(null, "钱包删除成功");
    }

    /**
     * 获取项目中的钱包列表（按添加者过滤）
     */
    @GetMapping("/projects/{projectId}/{addedBy}")
    public ApiResponse<PageResponse<WalletDTO>> getProjectWallets(
            @PathVariable Long projectId,
            @PathVariable String addedBy,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer limit) {

        log.info("获取项目钱包列表请求: projectId={}, addedBy={}, page={}, limit={}", projectId, addedBy, page, limit);

        PageResponse<WalletDTO> result = airdropService.getProjectWallets(projectId, addedBy, page, limit);
        return ApiResponse.success(result);
    }

    /**
     * 获取项目中的所有钱包列表（管理员接口）
     */
    @GetMapping("/projects/{projectId}/wallets/all")
    public ApiResponse<PageResponse<WalletDTO>> getAllProjectWallets(
            @PathVariable Long projectId,
            @RequestParam String adminAddress,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer limit) {

        log.info("获取项目所有钱包列表请求: projectId={}, adminAddress={}, page={}, limit={}",
                projectId, adminAddress, page, limit);

        // 验证管理员权限
        if (!airdropService.isAdmin(adminAddress)) {
            throw new IllegalArgumentException("无管理员权限");
        }

        PageResponse<WalletDTO> result = airdropService.getAllProjectWallets(projectId, page, limit);
        return ApiResponse.success(result);
    }


    // ==================== 数据统计接口 ====================

    /**
     * 获取项目统计信息
     */
    @GetMapping("/projects/{projectId}/stats")
    public ApiResponse<ProjectStatsDTO> getProjectStats(@PathVariable Long projectId) {
        log.info("获取项目统计信息请求: projectId={}", projectId);

        ProjectStatsDTO result = airdropService.getProjectStats(projectId);
        return ApiResponse.success(result);
    }

    // ==================== 工具接口 ====================

    /**
     * 验证钱包地址
     */
    @PostMapping("/wallets/validate")
    public ApiResponse<WalletDTO.ValidationResponse> validateWalletAddress(
            @Valid @RequestBody WalletDTO.ValidationRequest request) {
        
        log.info("验证钱包地址请求: address={}, chain={}", request.getAddress(), request.getChain());

        WalletDTO.ValidationResponse result = airdropService.validateWalletAddress(request);
        return ApiResponse.success(result);
    }

    /**
     * 导出项目数据
     */
    @GetMapping("/projects/{projectId}/export")
    public ResponseEntity<Resource> exportProjectData(
            @PathVariable Long projectId,
            @RequestParam(defaultValue = "json") String format) {
        
        log.info("导出项目数据请求: projectId={}, format={}", projectId, format);

        try {
            Resource resource = airdropService.exportProjectData(projectId, format);
            
            ProjectStatsDTO.ExportFormat exportFormat = ProjectStatsDTO.ExportFormat.fromString(format);
            String filename = "project_" + projectId + "_data." + exportFormat.getFormat();
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(exportFormat.getContentType()))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .body(resource);
                    
        } catch (UnsupportedOperationException e) {
            return ResponseEntity.status(501).build();
        }
    }

    /**
     * 获取表格列配置
     */
    @GetMapping("/projects/{projectId}/table-columns")
    public ApiResponse<List<ProjectStatsDTO.TableColumnDTO>> getTableColumns(@PathVariable Long projectId) {
        log.info("获取表格列配置请求: projectId={}", projectId);

        List<ProjectStatsDTO.TableColumnDTO> result = airdropService.getTableColumns(projectId);
        return ApiResponse.success(result);
    }

    /**
     * 更新表格列配置
     */
    @PutMapping("/projects/{projectId}/table-columns")
    public ApiResponse<List<ProjectStatsDTO.TableColumnDTO>> updateTableColumns(
            @PathVariable Long projectId,
            @Valid @RequestBody List<ProjectStatsDTO.TableColumnDTO> columns) {

        log.info("更新表格列配置请求: projectId={}, columnCount={}", projectId, columns.size());

        List<ProjectStatsDTO.TableColumnDTO> result = airdropService.updateTableColumns(projectId, columns);
        return ApiResponse.success(result, "表格列配置更新成功");
    }

    // ==================== 区块链数据同步接口 ====================

    /**
     * 同步钱包余额
     */
    @PostMapping("/projects/{projectId}/wallets/{walletAddress}/sync")
    public ApiResponse<Void> syncWalletBalance(
            @PathVariable Long projectId,
            @PathVariable String walletAddress) {

        log.info("同步钱包余额请求: projectId={}, address={}", projectId, walletAddress);

        airdropService.syncWalletBalance(projectId, walletAddress);
        return ApiResponse.success(null, "钱包余额同步成功");
    }

    /**
     * 批量同步项目中所有钱包余额
     */
    @PostMapping("/projects/{projectId}/wallets/sync-all")
    public ApiResponse<Void> syncAllWalletBalances(@PathVariable Long projectId) {
        log.info("批量同步钱包余额请求: projectId={}", projectId);

        // 异步执行同步任务
        new Thread(() -> {
            try {
                airdropService.syncAllWalletBalances(projectId);
            } catch (Exception e) {
                log.error("批量同步钱包余额失败: projectId={}", projectId, e);
            }
        }).start();

        return ApiResponse.success(null, "批量同步任务已启动");
    }

    /**
     * 更新钱包最后活动时间
     */
    @PostMapping("/wallets/{walletAddress}/activity")
    public ApiResponse<Void> updateWalletLastActivity(
            @PathVariable String walletAddress,
            @RequestParam String chain) {

        log.info("更新钱包活动时间请求: address={}, chain={}", walletAddress, chain);

        airdropService.updateWalletLastActivity(walletAddress, chain);
        return ApiResponse.success(null, "钱包活动时间更新成功");
    }

    // ==================== 权限验证接口 ====================

    /**
     * 验证用户是否有项目访问权限
     */
    @GetMapping("/projects/{projectId}/access")
    public ApiResponse<Boolean> hasProjectAccess(
            @PathVariable Long projectId,
            @RequestParam String userAddress) {

        log.debug("验证项目访问权限请求: projectId={}, userAddress={}", projectId, userAddress);

        boolean hasAccess = airdropService.hasProjectAccess(projectId, userAddress);
        return ApiResponse.success(hasAccess);
    }

    /**
     * 验证用户是否有项目编辑权限
     */
    @GetMapping("/projects/{projectId}/edit-access")
    public ApiResponse<Boolean> hasProjectEditAccess(
            @PathVariable Long projectId,
            @RequestParam String userAddress) {

        log.debug("验证项目编辑权限请求: projectId={}, userAddress={}", projectId, userAddress);

        boolean hasAccess = airdropService.hasProjectEditAccess(projectId, userAddress);
        return ApiResponse.success(hasAccess);
    }

    /**
     * 验证用户是否是管理员
     */
    @GetMapping("/admin/check")
    public ApiResponse<Boolean> isAdmin(@RequestParam String userAddress) {
        log.debug("验证管理员权限请求: userAddress={}", userAddress);

        boolean isAdmin = airdropService.isAdmin(userAddress);
        return ApiResponse.success(isAdmin);
    }

    // ==================== 健康检查接口 ====================

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ApiResponse<String> health() {
        return ApiResponse.success("OK", "空投项目管理系统运行正常");
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/info")
    public ApiResponse<Object> info() {
        return ApiResponse.success(java.util.Map.of(
                "service", "Airdrop Management System",
                "version", "1.0.0",
                "timestamp", java.time.LocalDateTime.now()
        ));
    }
}
