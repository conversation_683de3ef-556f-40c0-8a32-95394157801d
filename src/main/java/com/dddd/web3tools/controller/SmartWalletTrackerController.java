package com.dddd.web3tools.controller;

import com.dddd.web3tools.dto.*;
import com.dddd.web3tools.service.SmartWalletTrackerService;
import com.dddd.web3tools.vo.ApiResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SmartWalletTracker统一控制器
 * 包含聪明钱包管理、交易记录、跟单任务管理、统计分析等所有API接口
 */
@RestController
@RequestMapping("/api/v1")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class SmartWalletTrackerController {

    private final SmartWalletTrackerService smartWalletTrackerService;

    // ==================== 1. 聪明钱包管理 API ====================

    /**
     * 1.1 获取聪明钱包列表
     */
    @GetMapping("/smart-wallets")
    public ApiResponse<SmartWalletDTO.ListResponse> getSmartWallets(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer limit,
            @RequestParam(defaultValue = "profitPercent") String sortBy,
            @RequestParam(defaultValue = "desc") String order,
            @RequestParam(defaultValue = "active") String status,
            @RequestParam(required = false) List<String> tags) {

        try {
            SmartWalletDTO.QueryRequest request = SmartWalletDTO.QueryRequest.builder()
                    .page(page)
                    .limit(limit)
                    .sortBy(sortBy)
                    .order(order)
                    .status(status)
                    .tags(tags)
                    .build();

            SmartWalletDTO.ListResponse response = smartWalletTrackerService.getSmartWallets(request);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("获取聪明钱包列表失败", e);
            return ApiResponse.error("INTERNAL_ERROR", "获取聪明钱包列表失败: " + e.getMessage());
        }
    }

    /**
     * 1.2 获取单个聪明钱包详情
     */
    @GetMapping("/smart-wallets/{walletId}")
    public ApiResponse<SmartWalletDTO> getSmartWalletDetail(@PathVariable String walletId) {
        try {
            SmartWalletDTO response = smartWalletTrackerService.getSmartWalletDetail(walletId);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("获取聪明钱包详情失败: walletId={}", walletId, e);
            return ApiResponse.error("INTERNAL_ERROR", "获取聪明钱包详情失败: " + e.getMessage());
        }
    }

    // ==================== 2. 交易记录 API ====================

    /**
     * 2.1 获取钱包交易记录
     */
    @GetMapping("/smart-wallets/{walletId}/transactions")
    public ApiResponse<TransactionDTO.ListResponse> getWalletTransactions(
            @PathVariable String walletId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "50") Integer limit,
            @RequestParam(defaultValue = "all") String status,
            @RequestParam(defaultValue = "all") String action,
            @RequestParam(required = false) String tokenAddress,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime) {

        try {
            TransactionDTO.QueryRequest request = TransactionDTO.QueryRequest.builder()
                    .page(page)
                    .limit(limit)
                    .status(status)
                    .action(action)
                    .tokenAddress(tokenAddress)
                    // TODO: 解析时间参数
                    .build();

            TransactionDTO.ListResponse response = smartWalletTrackerService.getWalletTransactions(walletId, request);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("获取钱包交易记录失败: walletId={}", walletId, e);
            return ApiResponse.error("INTERNAL_ERROR", "获取钱包交易记录失败: " + e.getMessage());
        }
    }

    // ==================== 3. 跟单任务管理 API ====================

    /**
     * 3.1 创建跟单任务
     */
    @PostMapping("/follow-tasks")
    public ApiResponse<FollowTaskDTO> createFollowTask(@Valid @RequestBody FollowTaskDTO.CreateRequest request) {
        try {
            FollowTaskDTO response = smartWalletTrackerService.createFollowTask(request);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("创建跟单任务失败", e);
            return ApiResponse.error("INTERNAL_ERROR", "创建跟单任务失败: " + e.getMessage());
        }
    }

    /**
     * 3.2 获取用户跟单任务列表
     */
    @GetMapping("/follow-tasks")
    public ApiResponse<FollowTaskDTO.ListResponse> getFollowTasks(
            @RequestParam String wallet,
            @RequestParam(defaultValue = "all") String status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer limit) {

        try {
            FollowTaskDTO.QueryRequest request = FollowTaskDTO.QueryRequest.builder()
                    .wallet(wallet)
                    .status(status)
                    .page(page)
                    .limit(limit)
                    .build();

            FollowTaskDTO.ListResponse response = smartWalletTrackerService.getFollowTasks(request);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("获取跟单任务列表失败", e);
            return ApiResponse.error("INTERNAL_ERROR", "获取跟单任务列表失败: " + e.getMessage());
        }
    }

    /**
     * 3.3 更新跟单任务状态
     */
    @PatchMapping("/follow-tasks/{taskId}/status")
    public ApiResponse<FollowTaskDTO> updateFollowTaskStatus(
            @PathVariable String taskId,
            @Valid @RequestBody FollowTaskDTO.StatusUpdateRequest request) {

        try {
            request.setTaskId(taskId);
            FollowTaskDTO response = smartWalletTrackerService.updateFollowTaskStatus(request);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("更新跟单任务状态失败: taskId={}", taskId, e);
            return ApiResponse.error("INTERNAL_ERROR", "更新跟单任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 3.4 删除跟单任务
     */
    @DeleteMapping("/follow-tasks/{taskId}")
    public ApiResponse<String> deleteFollowTask(
            @PathVariable String taskId,
            @RequestBody FollowTaskDTO.DeleteRequest request) {

        try {
            request.setTaskId(taskId);
            smartWalletTrackerService.deleteFollowTask(request);
            return ApiResponse.success("跟单任务已删除");

        } catch (Exception e) {
            log.error("删除跟单任务失败: taskId={}", taskId, e);
            return ApiResponse.error("INTERNAL_ERROR", "删除跟单任务失败: " + e.getMessage());
        }
    }

    /**
     * 3.5 获取跟单任务详情
     */
    @GetMapping("/follow-tasks/{taskId}")
    public ApiResponse<TransactionDTO.TaskDetailResponse> getFollowTaskDetail(
            @PathVariable String taskId,
            @RequestParam String wallet) {

        try {
            TransactionDTO.TaskDetailResponse response = smartWalletTrackerService.getFollowTaskDetail(wallet, taskId);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("获取跟单任务详情失败: taskId={}", taskId, e);
            return ApiResponse.error("INTERNAL_ERROR", "获取跟单任务详情失败: " + e.getMessage());
        }
    }

    // ==================== 6. 统计分析 API ====================

    /**
     * 6.1 获取平台统计数据
     */
    @GetMapping("/stats/platform")
    public ApiResponse<PlatformStatsDTO> getPlatformStats() {
        try {
            PlatformStatsDTO response = smartWalletTrackerService.getPlatformStats();
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("获取平台统计数据失败", e);
            return ApiResponse.error("INTERNAL_ERROR", "获取平台统计数据失败: " + e.getMessage());
        }
    }
}
