package com.dddd.web3tools.controller;

import com.dddd.web3tools.dto.TweetStateUpdateDTO;
import com.dddd.web3tools.entity.Tweet;
import com.dddd.web3tools.service.TwitterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/tweets")
public class TwitterController {

    @Autowired
    private TwitterService twitterService;

    @GetMapping("/recent")
    public List<Tweet> getRecentTweets() {
        return twitterService.getRecentTweets();
    }

    @PostMapping("/updateState")
    public Tweet updateTweetState(@RequestBody TweetStateUpdateDTO request) {
        return twitterService.updateTweetState(request.getId(), request.getState());
    }
}