package com.dddd.web3tools.job;

import com.dddd.web3tools.entity.User;
import com.dddd.web3tools.entity.WalletHistory;
import com.dddd.web3tools.repository.UserRepository;
import com.dddd.web3tools.repository.UserWalletRepository;
import com.dddd.web3tools.repository.WalletHistoryRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 钱包历史数据记录定时任务
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WalletHistoryJob {

    private final UserRepository userRepository;
    private final UserWalletRepository userWalletRepository;
    private final WalletHistoryRepository walletHistoryRepository;

    /**
     * 每天0点记录用户资产快照
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void recordDailySnapshot() {
        log.info("开始记录每日资产快照");

        LocalDate today = LocalDate.now();
        List<User> users = userRepository.findAll();

        for (User user : users) {
            try {
                // 检查今天是否已经记录过
                if (walletHistoryRepository.findByUserIdAndTypeAndDate(user.getId(), "DAILY", today).isPresent()) {
                    continue;
                }

                // 计算用户总资产
                BigDecimal totalValue = userWalletRepository.sumUsdValueByUserId(user.getId());

                // 记录历史数据
                WalletHistory history = WalletHistory.builder()
                        .user(user)
                        .date(today)
                        .type("DAILY")
                        .totalValue(totalValue)
                        .build();

                walletHistoryRepository.save(history);
                log.debug("记录用户{}的每日资产快照: {}", user.getAddress(), totalValue);

            } catch (Exception e) {
                log.error("记录用户{}的每日资产快照失败: {}", user.getAddress(), e.getMessage());
            }
        }

        log.info("每日资产快照记录完成");
    }

    /**
     * 每月1号0点记录用户月度资产快照
     */
    @Scheduled(cron = "0 0 0 1 * ?")
    public void recordMonthlySnapshot() {
        log.info("开始记录月度资产快照");

        LocalDate today = LocalDate.now();
        LocalDate lastMonth = today.minusMonths(1);
        List<User> users = userRepository.findAll();

        for (User user : users) {
            try {
                // 检查上个月是否已经记录过
                if (walletHistoryRepository.findByUserIdAndTypeAndDate(user.getId(), "MONTHLY", lastMonth).isPresent()) {
                    continue;
                }

                // 计算用户总资产
                BigDecimal totalValue = userWalletRepository.sumUsdValueByUserId(user.getId());

                // 记录历史数据
                WalletHistory history = WalletHistory.builder()
                        .user(user)
                        .date(lastMonth)
                        .type("MONTHLY")
                        .totalValue(totalValue)
                        .build();

                walletHistoryRepository.save(history);
                log.debug("记录用户{}的月度资产快照: {}", user.getAddress(), totalValue);

            } catch (Exception e) {
                log.error("记录用户{}的月度资产快照失败: {}", user.getAddress(), e.getMessage());
            }
        }

        log.info("月度资产快照记录完成");
    }

    /**
     * 清理过期的历史数据（保留2年）
     */
    @Scheduled(cron = "0 0 2 1 * ?") // 每月1号凌晨2点执行
    public void cleanupExpiredHistory() {
        log.info("开始清理过期历史数据");

        LocalDate cutoffDate = LocalDate.now().minusYears(2);

        try {
            // 删除2年前的每日数据
            walletHistoryRepository.deleteByDateBeforeAndType(cutoffDate, "DAILY");
            
            // 删除2年前的月度数据
            walletHistoryRepository.deleteByDateBeforeAndType(cutoffDate, "MONTHLY");
            
            log.info("过期历史数据清理完成");
        } catch (Exception e) {
            log.error("清理过期历史数据失败: {}", e.getMessage());
        }
    }
}
