package com.dddd.web3tools.job;

import com.dddd.web3tools.entity.BlockchainData;
import com.dddd.web3tools.entity.Tweet;
import com.dddd.web3tools.repository.BlockchainDataRepository;
import com.dddd.web3tools.repository.TweetRepository;
import com.dddd.web3tools.service.impl.NotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;

@Slf4j
@Service
public class CheckTwitterAndGasJob {

    @Autowired
    private BlockchainDataRepository blockchainDataRepository;

    @Autowired
    private TweetRepository tweetRepository; // 注入TweetRepository

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private RestTemplate restTemplate; // 用于发送HTTP请求

    @Value("${bark.push.url}")
    private String barkPushUrl; // Bark推送URL


    @Scheduled(cron = "0 0/20 0-9 * * ?") // 晚上12点到早上10点，每20分钟执行一次
    public void checkGasAndTweetsAtNight() {
        try {
            log.info("开始检查晚上20分钟内的tweet和gas波动...");

            // 获取20分钟前的时间
            LocalDateTime twentyMinutesAgo = LocalDateTime.now().minusMinutes(20);

            // 查询20分钟内的tweet数据
            List<Tweet> recentTweets = tweetRepository.findRecentTweets(twentyMinutesAgo);

            if (recentTweets.isEmpty()) {
                log.info("最近20分钟内无tweet数据，无需处理");
                return;
            }

            // 查询20分钟内的blockchaindata数据
            List<BlockchainData> recentBlockchainData = blockchainDataRepository.findByCreatedAtAfter(twentyMinutesAgo);

            if (recentBlockchainData.size() < 2) {
                log.info("最近20分钟内blockchaindata记录不足2条，无法计算gas波动");
                return;
            }

            // 按时间排序记录（升序）
            recentBlockchainData.sort(Comparator.comparing(BlockchainData::getCreatedAt));

            // 取最早和最新的gas值计算波动
            double earliestGas = Double.parseDouble(recentBlockchainData.get(0).getGasPrice());
            double latestGas = Double.parseDouble(recentBlockchainData.get(recentBlockchainData.size() - 1).getGasPrice());

            // 如果链类型是ETH，将gas价格从Wei转换为Gwei
            if ("ETH".equals(recentBlockchainData.get(0).getChainType())) {
                final double WEI_TO_GWEI = 1e9;
                earliestGas /= WEI_TO_GWEI;
                latestGas /= WEI_TO_GWEI;
            }

            double gasFluctuation = Math.abs(latestGas - earliestGas);

            if (gasFluctuation > 3) {
                // 发送Bark通知
                String notificationMessage = "20分钟内tweet有数据，且gas波动超过3！";
                String url = barkPushUrl + "/" + notificationMessage + "?call=1&level=critical&volue=10";
                restTemplate.getForObject(url, String.class);
                log.info("已发送Bark通知: {}", notificationMessage);
            } else {
                log.info("20分钟内gas波动未超过3，不发送通知");
            }
        } catch (Exception e) {
            log.error("晚上检查tweet和gas波动任务失败", e);
        }
    }
}
