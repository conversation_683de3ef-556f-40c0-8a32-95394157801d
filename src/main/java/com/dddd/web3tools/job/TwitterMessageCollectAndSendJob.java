package com.dddd.web3tools.job;

import com.dddd.web3tools.dto.TweetAnalysisResult;
import com.dddd.web3tools.entity.Tweet;
import com.dddd.web3tools.repository.BlacklistUserRepository;
import com.dddd.web3tools.repository.KeywordRepository;
import com.dddd.web3tools.repository.TweetRepository;
import com.dddd.web3tools.service.GeminiService;
import com.dddd.web3tools.service.TweetAnalysisService;
import com.dddd.web3tools.service.TweetCacheService;
import com.dddd.web3tools.service.impl.KeywordService;
import com.dddd.web3tools.service.impl.NotificationService;
import com.dddd.web3tools.util.TwitterAPIUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class TwitterMessageCollectAndSendJob {
    @Autowired
    private TweetRepository tweetRepository;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private KeywordService keywordService;

    @Autowired
    private KeywordRepository keywordRepository;

    @Autowired
    private BlacklistUserRepository blacklistUserRepository;

    @Autowired
    private TwitterAPIUtil twitterAPIUtil;

    @Autowired
    private GeminiService geminiService;

    @Autowired
    private TweetAnalysisService tweetAnalysisService;

    @Autowired
    private TweetCacheService tweetCacheService;

    @Value("${twitter.api.url}")
    private String apiUrl;

    @Value("${search.twitter.api.url}")
    private String searchApiUrl;

    @Value("${twitter.api.list_id}")
    private String listId;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final String SEARCH_TYPE = "Latest";  // Top or Latest
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss Z yyyy", Locale.ENGLISH);

    private static final Logger logger = LoggerFactory.getLogger(TwitterMessageCollectAndSendJob.class);


    @Scheduled(fixedRate = 600000)
    public void fetchTwitterTimeline() {
        try {
            logger.info("开始获取Twitter时间线...");
            JsonNode response = twitterAPIUtil.getTwitterListByListId(listId);
            extractTweets(response);
        } catch (Exception e) {
            logger.error("获取Twitter时间线失败", e);
        }
    }

    @Scheduled(fixedRate = 1200000)
    public void searchTwitterByKeywords() {
        try {
            Set<String> keywords = keywordService.getKewordByBaseType();
            List<String> allTweets = new ArrayList<>();

            keywords.forEach(keyword -> {
                try {
                    logger.info("搜索关键词 {} begin...",keyword);
                    String url = TwitterAPIUtil.buildUrl(searchApiUrl, "search_type", SEARCH_TYPE, "query", keyword);
                    String response = twitterAPIUtil.sendHttpRequestWithPool(url);
                    List<String> tweets = extractSearchTweets(objectMapper.readTree(response));
                    logger.info("本次搜索 {} 缝合条件推文数量: {}", keyword,tweets.size());
                    allTweets.addAll(tweets);
                } catch (Exception e) {
                    logger.error("搜索关键词[{}]失败", keyword, e);
                }
            });

            if (!allTweets.isEmpty()) {
                logger.info("search-总共处理以下数量的推文: {}", allTweets.size());
                processSearchTweets(allTweets);
            }
        } catch (Exception e) {
            logger.error("搜索Twitter关键词失败", e);
        }
    }

    /**
     * 定时清理推特缓存 - 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanTweetCache() {
        try {
            logger.info("开始清理推特缓存...");
            tweetCacheService.cleanExpiredTweetIdsCache();
            long cacheCount = tweetCacheService.getCachedTweetIdsCount();
            logger.info("推特缓存清理完成，当前缓存推特ID数量: {}", cacheCount);
        } catch (Exception e) {
            logger.error("清理推特缓存失败", e);
        }
    }

    private List<String> extractTweets(JsonNode rootNode) {
        List<String> tweets = new ArrayList<>();
        List<Tweet> tweetsToSave = new ArrayList<>();

        if (rootNode.has("timeline")) {
            JsonNode dataNode = rootNode.get("timeline");
            for (JsonNode tweetNode : dataNode) {
                if (tweetNode.has("text") && tweetNode.get("text") != null) {
                    String tweetId = tweetNode.get("tweet_id").asText();

                    // 使用缓存服务检查推特是否存在
                    if (!tweetCacheService.existsTweetId(tweetId)) {
                        JsonNode authorNode = tweetNode.get("author");
                        String content = tweetNode.get("text").asText();
                        int followers = authorNode.has("followers_count") ? authorNode.get("followers_count").asInt() : 0;

                        Tweet tweet = createTweetFromNode(tweetId, content, authorNode, followers, tweetNode, 1);

                        // 添加到批量保存列表
                        tweetsToSave.add(tweet);
                    }
                }
            }

            // 批量保存推特
            if (!tweetsToSave.isEmpty()) {
                logger.info("准备批量保存{}条时间线推特", tweetsToSave.size());
                int savedCount = tweetCacheService.batchSaveTweets(tweetsToSave);
                logger.info("批量保存时间线推特完成，实际保存{}条", savedCount);
            }
        }
        return tweets;
    }

    private List<String> extractSearchTweets(JsonNode rootNode) {
        List<String> tweets = new ArrayList<>();
        List<Tweet> tweetsToSave = new ArrayList<>();

        if (rootNode.has("timeline")) {
            JsonNode dataNode = rootNode.get("timeline");
            for (JsonNode tweetNode : dataNode) {
                if (tweetNode.has("text")) {
                    String tweetId = tweetNode.get("tweet_id").asText();
                    JsonNode authorNode = tweetNode.get("user_info");
                    String content = tweetNode.get("text").asText();
                    int followers = authorNode.get("followers_count").asInt();
                    String authorId = authorNode.get("screen_name").asText();

                    // 这里增加的额外判断是为了排除掉，包含了关键字，但是链接其实是一些图片或者视频的情况 并不需要
                    // 检查链接条件和 media 条件
//                    boolean shouldSkip = false;
//                    // 正则表达式匹配链接
//                    java.util.regex.Pattern urlPattern = java.util.regex.Pattern.compile("https?://\\S+");
//                    java.util.regex.Matcher matcher = urlPattern.matcher(content);
//                    int urlCount = 0;
//                    String lastUrl = null;
//                    while (matcher.find()) {
//                        urlCount++;
//                        lastUrl = matcher.group();
//                    }
//                    if (urlCount == 1 && content.endsWith(lastUrl) && tweetNode.has("media") && !tweetNode.get("media").isNull()) {
//                        shouldSkip = true;
//                    }

                    // 使用缓存服务检查推特是否存在，并检查是否应该保存
                    if (!tweetCacheService.existsTweetId(tweetId) &&
                            notificationService.shouldSaveTweet(content, followers, authorId)) {

                        Tweet tweet = createTweetFromNode(tweetId, content, authorNode, followers, tweetNode, 0);
                        tweetsToSave.add(tweet);

                        // 添加到通知列表
                        tweets.add(String.format("作者: %s (粉丝数: %d)\n内容: %s\n发布时间: %s",
                                tweet.getAuthor(), followers, content, tweet.getCreatedAt()));
                    }
                }
            }

            // 批量保存推特
            if (!tweetsToSave.isEmpty()) {
                int savedCount = tweetCacheService.batchSaveTweets(tweetsToSave);
                logger.info("批量保存搜索推特完成，实际保存{}条", savedCount);
            }
        }
        return tweets;
    }

    /**
     * 创建Tweet对象的辅助方法
     */
    private Tweet createTweetFromNode(String tweetId, String content, JsonNode authorNode, int followers, JsonNode tweetNode, int type) {
        Tweet tweet = new Tweet();
        tweet.setId(tweetId);
        tweet.setContent(content);
        tweet.setAuthor(authorNode.get("name").asText());
        tweet.setScreenName(authorNode.get("screen_name").asText());
        tweet.setFollowers(followers);

        if (!tweetNode.get("created_at").isNull()) {
            tweet.setCreatedAt(
                    ZonedDateTime.parse(tweetNode.get("created_at").asText(), formatter)
                            .withZoneSameInstant(ZoneId.of("Asia/Shanghai"))
                            .toLocalDateTime()
            );
        } else {
            tweet.setCreatedAt(LocalDateTime.now()); // 如果created_at为null，使用当前时间
        }
        tweet.setType(type);
        tweet.setState(0);

        return tweet;
    }

//    /**
//     * @deprecated 此方法已被批量保存方式替代，保留用于兼容性
//     */
//    @Deprecated
//    private void saveTweet(String tweetId, String content, JsonNode authorNode, int followers, List<String> tweets, String tweetTime,JsonNode tweetNode) {
//        String authorId = authorNode.get("screen_name").asText();
//        // 修改：使用缓存服务检查推特是否存在
//        if (!tweetCacheService.existsTweetId(tweetId) &&
//                notificationService.shouldSaveTweet(content, followers,authorId)) {  // 新增关键词检查
//
//            Tweet tweet = createTweetFromNode(tweetId, content, authorNode, followers, tweetNode, 0);
//
//            // 单个保存（已废弃，建议使用批量保存）
//            tweetRepository.save(tweet);
//            tweetCacheService.addTweetIdToCache(tweetId);
//
//            tweets.add(String.format("作者: %s (粉丝数: %d)\n内容: %s\n发布时间: %s",
//                    tweet.getAuthor(), followers, content, tweet.getCreatedAt()));
//        }
//    }


    private void processSearchTweets(List<String> tweets) {
//        // 原有的关键词匹配处理
//        notificationService.processNotify(tweets);

        // 新增：使用Gemini AI分析mint相关内容
        processMintTweets(tweets);
    }

    /**
     * 处理mint相关推文
     */
    private void processMintTweets(List<String> tweets) {
        try {
            logger.info("=== 开始分析{}条推文的mint内容 ===", tweets.size());

            List<String> mintTweets = new ArrayList<>();
            List<String> analysisResults = new ArrayList<>();
            int analyzedCount = 0;
            int qualifiedCount = 0;

            for (String tweetContent : tweets) {
                try {
                    analyzedCount++;
                    logger.info("正在分析第{}/{}条推文", analyzedCount, tweets.size());

                    // 使用TweetAnalysisService分析推文
                    TweetAnalysisResult result = tweetAnalysisService.analyzeTweet(tweetContent);

                    logger.info("推文分析完成 - 是否推送: {}, 置信度: {}%", result.isShouldNotify(), result.getConfidence());

                    // 只有shouldNotify为true的推文才会被推送
                    if (result.isShouldNotify()) {
                        qualifiedCount++;
                        mintTweets.add(tweetContent);

                        // 构建详细的分析结果
                        StringBuilder analysisDetail = new StringBuilder();
                        analysisDetail.append("🚀 发现mint机会！\n\n");
                        analysisDetail.append("推文内容：\n").append(tweetContent).append("\n\n");
                        analysisDetail.append("分析结果：\n");
                        analysisDetail.append("- 包含mint内容：").append(result.isContainsMintContent() ? "✅" : "❌").append("\n");
                        analysisDetail.append("- 包含网址：").append(result.isContainsUrl() ? "✅" : "❌").append("\n");
                        analysisDetail.append("- 是否垃圾内容：").append(result.isSpam() ? "❌" : "✅").append("\n");
                        analysisDetail.append("- 置信度：").append(result.getConfidence()).append("%\n");

                        if (!result.getExtractedUrls().isEmpty()) {
                            analysisDetail.append("- 发现链接：\n");
                            for (String url : result.getExtractedUrls()) {
                                analysisDetail.append("  • ").append(url).append("\n");
                            }
                        }

                        if (!result.getMintKeywords().isEmpty()) {
                            analysisDetail.append("- 关键词：").append(String.join(", ", result.getMintKeywords())).append("\n");
                        }

                        analysisDetail.append("- AI分析：").append(result.getAnalysisDetails()).append("\n");
                        analysisDetail.append("- 分析原因：").append(result.getReason()).append("\n");

                        analysisResults.add(analysisDetail.toString());

                        logger.info("✅ 发现符合条件的mint推文，置信度: {}%", result.getConfidence());
                    } else {
                        logger.info("❌ 推文不符合推送条件，原因: {}", result.getReason());
                    }
                } catch (Exception e) {
                    logger.error("分析单条推文失败: {}", tweetContent.substring(0, Math.min(50, tweetContent.length())), e);
                }
            }

            logger.info("=== 推文分析完成 ===");
            logger.info("总分析数量: {}, 符合条件数量: {}", analyzedCount, qualifiedCount);

            // 只有符合条件的mint推文才发送邮件通知
            if (!mintTweets.isEmpty()) {
                logger.info("发现{}条符合条件的mint推文，准备发送邮件通知", mintTweets.size());

                // 发送详细分析结果
                notificationService.sendNotification(analysisResults, "🚀 Mint机会提醒 - AI智能分析");

                logger.info("mint推文邮件通知已发送，共{}条推文", mintTweets.size());
            } else {
                logger.info("本次分析未发现符合条件的mint推文");
            }

        } catch (Exception e) {
            logger.error("处理mint推文时发生错误", e);
        }
    }

//    @Scheduled(cron = "0 0 9-23 * * *") // 每小时整点执行一次
//    public void sendHourlyTweetsSummary() {
//        try {
//            // 获取最近一小时的推文
//            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
//            List<Tweet> recentTweets = tweetRepository.findRecentTweets(oneHourAgo);
//
//            if (!recentTweets.isEmpty()) {
//                // 格式化邮件内容
//                StringBuilder emailContent = new StringBuilder();
//                emailContent.append("最近一小时内的推文摘要:\n\n");
//
//                for (Tweet tweet : recentTweets) {
//                    emailContent.append("作者: ").append(tweet.getAuthor())
//                            .append(" (粉丝数: ").append(tweet.getFollowers()).append(")\n")
//                            .append("内容: ").append(tweet.getContent()).append("\n")
////                            .append("AI分析: ").append(tweet.getAiAnalysis()).append("\n")
//                            .append("发布时间: ").append(tweet.getCreatedAt()).append("\n")
//                            .append("\n\n++++++++++++新消息++++++++++++\n\n");
//                }
//                // 发送邮件通知
//                notificationService.sendNotification(Collections.singletonList(emailContent.toString()), "1小时推文摘要");
//                logger.info("已发送最近一小时的推文摘要邮件，共{}条推文", recentTweets.size());
//            } else {
//                logger.info("最近一小时没有新推文，不发送邮件");
//            }
//        } catch (Exception e) {
//            logger.error("发送每小时推文摘要失败: " + e.getMessage());
//        }
//    }

    @Scheduled(cron = "0 0 */3 * * *") // 每3小时整点执行一次
    public void sendHourlyTweetsSummaryforNight() {
        try {
            // 获取最近一小时的推文
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(3);
            List<Tweet> recentTweets = tweetRepository.findRecentTweets(oneHourAgo);

            if (!recentTweets.isEmpty()) {
                // 格式化邮件内容
                StringBuilder emailContent = new StringBuilder();
                emailContent.append("最近三小时内的推文摘要:\n\n");

                for (Tweet tweet : recentTweets) {
                    emailContent.append("作者: ").append(tweet.getAuthor())
                            .append(" (粉丝数: ").append(tweet.getFollowers()).append(")\n")
                            .append("内容: ").append(tweet.getContent()).append("\n")
                            .append("发布时间: ").append(tweet.getCreatedAt()).append("\n")
                            .append("\n\n++++++++++++新消息++++++++++++\n\n");
                }
                // 发送邮件通知
                notificationService.sendNotification(Collections.singletonList(emailContent.toString()), "3小时推文摘要");
                logger.info("已发送最近三小时的推文摘要邮件，共{}条推文", recentTweets.size());
            } else {
                logger.info("最近三小时没有新推文，不发送邮件");
            }
        } catch (Exception e) {
            logger.error("发送3小时推文摘要失败: " + e.getMessage());
        }
    }
}