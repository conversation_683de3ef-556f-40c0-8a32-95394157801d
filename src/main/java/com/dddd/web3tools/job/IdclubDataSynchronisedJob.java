package com.dddd.web3tools.job;

import com.dddd.web3tools.entity.AIkanesToken;
import com.dddd.web3tools.repository.AIkanesTokenRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
@Slf4j
public class IdclubDataSynchronisedJob {

    private final RestTemplate restTemplate;
    private final AIkanesTokenRepository tokenRepository;

    public IdclubDataSynchronisedJob(RestTemplate restTemplate, AIkanesTokenRepository tokenRepository) {
        this.restTemplate = restTemplate;
        this.tokenRepository = tokenRepository;
    }

    @Scheduled(fixedRate = 300000) // 30秒
    public void collectBlockchainData() {
        log.info("开始同步IDClub代币数据...");

        try {
            // 构建请求参数
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("name", "");
            requestParams.put("mintActive", 1);
            requestParams.put("page", 1);
            requestParams.put("size", 10);
            requestParams.put("orderType", "mempoolTxCountDesc");
            requestParams.put("noPremine", 0);

            // 发送请求
            String url = "https://alkanes-api.idclub.io/token/page";
            ResponseEntity<Map> response = restTemplate.postForEntity(url, requestParams, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> responseData = (Map<String, Object>) response.getBody().get("data");
                if (responseData != null && responseData.containsKey("records")) {
                    List<Map<String, Object>> records = (List<Map<String, Object>>) responseData.get("records");
                    for (Map<String, Object> record : records) {
                        String tokenId = (String) record.get("id");
                        // 检查token是否已存在
                        Optional<AIkanesToken> existingToken = tokenRepository.findByTokenId(tokenId);
                        AIkanesToken token = existingToken.orElse(new AIkanesToken());

                        // 设置/更新token属性
                        token.setTokenId(tokenId);
                        token.setName((String) record.get("name"));
                        token.setMintActive((Integer) record.get("mintActive"));
                        token.setActualMintActive((Integer) record.get("actualMintActive"));
                        token.setSymbol((String) record.get("symbol"));
                        token.setImage((String) record.get("image"));
                        token.setCap((String) record.get("cap"));
                        token.setPremine((String) record.get("premine"));
                        token.setMinted((String) record.get("minted"));
                        token.setMintAmount((String) record.get("mintAmount"));
                        token.setTotalSupply(new BigDecimal(record.get("totalSupply").toString()));
                        token.setProgress(Double.valueOf(record.get("progress").toString()));
                        token.setHolders((Integer) record.get("holders"));
                        token.setFloorPrice(new BigDecimal(record.get("floorPrice").toString()));
                        token.setMarketCap((String) record.get("marketCap"));
                        token.setPriceChange24h((String) record.get("priceChange24h"));
                        token.setPriceChange7d((String) record.get("priceChange7d"));
                        token.setPriceChange30d((String) record.get("priceChange30d"));
                        token.setTradingVolume24h((String) record.get("tradingVolume24h"));
                        token.setTradingVolume7d((String) record.get("tradingVolume7d"));
                        token.setTradingVolume30d((String) record.get("tradingVolume30d"));
                        token.setTotalTradingVolume((String) record.get("totalTradingVolume"));
                        token.setTradingCount24h((Integer) record.get("tradingCount24h"));
                        token.setTradingCount7d((Integer) record.get("tradingCount7d"));
                        token.setTradingCount30d((Integer) record.get("tradingCount30d"));
                        token.setTotalTradingCount((Integer) record.get("totalTradingCount"));
                        token.setData((String) record.get("data"));
                        token.setReserveNumber((Integer) record.get("reserveNumber"));
                        token.setIsNftCollection((Boolean) record.get("isNftCollection"));

                        // mempool信息
                        Map<String, Object> mempool = (Map<String, Object>) record.get("mempool");
                        if (mempool != null) {
                            token.setMempoolCount((Integer) mempool.get("count"));
                            token.setMempoolAddressCount((Integer) mempool.get("addressCount"));
                            token.setMempoolNextBlockCount((Integer) mempool.get("nextBlockCount"));
                            token.setMempoolMedianFeeRate(safeToString(mempool.get("medianFeeRate")));
                            Map<String, Object> feeRateRanges = (Map<String, Object>) record.get("feeRateRanges");
                            if (feeRateRanges != null) {
                                token.setMempoolFeeRateRange((String) feeRateRanges.get("feeRateRange"));
                            }

                        }
                        tokenRepository.save(token);
                    }
                }
            }
        } catch (Exception e) {
            log.error("同步IDClub代币数据时发生错误", e);
        }
        log.info("IDClub代币数据同步完成");
    }

    private static String safeToString(Object obj) {
        return obj != null ? obj.toString() : null;
    }

}