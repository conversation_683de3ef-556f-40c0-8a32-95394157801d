package com.dddd.web3tools.job;

import com.dddd.web3tools.service.SmartContractDiscoveryService;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 智能合约发现任务
 * 在应用启动时自动开始监控新合约部署和交互
 */
@Component
@Slf4j
public class SmartContractDiscoveryJob implements CommandLineRunner {

    @Autowired
    private SmartContractDiscoveryService contractDiscoveryService;

    @Value("${ethereum.contract.discovery.enabled:true}")
    private boolean autoStart;

    @Override
    public void run(String... args) throws Exception {
        if (autoStart) {
            log.info("应用启动完成，开始自动启动智能合约发现服务...");
            try {
                // 延迟10秒启动，确保所有组件都已初始化
                Thread.sleep(10000);
                contractDiscoveryService.startMonitoring();
                log.info("智能合约发现服务自动启动成功");

                // 打印监控配置信息
                printMonitorConfig();
            } catch (Exception e) {
                log.error("自动启动智能合约发现服务失败", e);
            }
        } else {
            log.info("智能合约发现服务自动启动已禁用");
        }
    }

    @PreDestroy
    public void shutdown() {
        log.info("应用关闭中，停止智能合约发现服务...");
        try {
            if (contractDiscoveryService.isMonitoring()) {
                // 打印最终统计信息
                printFinalStats();
                contractDiscoveryService.stopMonitoring();
                log.info("智能合约发现服务已停止");
            }
        } catch (Exception e) {
            log.error("停止智能合约发现服务时发生错误", e);
        }
    }

    /**
     * 打印监控配置信息
     */
    private void printMonitorConfig() {
        log.info("=== 智能合约发现服务配置 ===");
        log.info("🔧 自动启动: {}", autoStart);
        log.info("🔧 监控状态: {}", contractDiscoveryService.isMonitoring() ? "运行中" : "已停止");
        log.info("============================");
    }

    /**
     * 打印最终统计信息
     */
    private void printFinalStats() {
        try {
            SmartContractDiscoveryService.MonitorStats stats = contractDiscoveryService.getMonitorStats();
            log.info("=== 智能合约发现服务最终统计 ===");
            log.info("📊 发现的合约数: {}", stats.getTotalContractsDiscovered());
            log.info("📊 活跃合约数: {}", stats.getActiveContracts24h());
            log.info("📊 过滤的合约数: {}", stats.getFilteredContracts());
            log.info("📊 最后处理的区块: {}", stats.getLastProcessedBlock());
            log.info("📊 处理错误数: {}", stats.getProcessingErrors());
            log.info("📊 运行时间: 从 {} 开始", stats.getStartTime());
            log.info("================================");
        } catch (Exception e) {
            log.warn("获取监控统计信息失败", e);
        }
    }
}
