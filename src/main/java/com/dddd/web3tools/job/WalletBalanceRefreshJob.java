package com.dddd.web3tools.job;

import com.dddd.web3tools.entity.UserWallet;
import com.dddd.web3tools.entity.WalletToken;
import com.dddd.web3tools.repository.UserWalletRepository;
import com.dddd.web3tools.repository.WalletTokenRepository;
import com.dddd.web3tools.service.WalletBalanceCacheService;
import com.dddd.web3tools.service.BlockchainQueryService;
import com.dddd.web3tools.dto.UserWalletDTO;
import com.dddd.web3tools.config.WalletCacheConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 钱包余额刷新定时任务
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WalletBalanceRefreshJob {

    private final UserWalletRepository userWalletRepository;
    private final WalletTokenRepository walletTokenRepository;
    private final WalletBalanceCacheService walletBalanceCacheService;
    private final BlockchainQueryService blockchainQueryService;
    private final WalletCacheConfig cacheConfig;

    /**
     * 每5分钟刷新所有钱包余额
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300,000毫秒
    @Transactional
    public void refreshAllWalletBalances() {
        log.info("🔄 开始定时刷新所有钱包余额 - WalletBalanceRefreshJob [线程: {}]", Thread.currentThread().getName());
        
        try {
            // 获取所有钱包
            List<UserWallet> allWallets = userWalletRepository.findAll();
            log.info("找到{}个钱包需要刷新", allWallets.size());
            
            if (allWallets.isEmpty()) {
                log.info("没有钱包需要刷新");
                return;
            }
            
            int successCount = 0;
            int failCount = 0;
            
            // 分批处理，避免一次性处理太多钱包
            int batchSize = cacheConfig.getBatchSize();
            for (int i = 0; i < allWallets.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, allWallets.size());
                List<UserWallet> batch = allWallets.subList(i, endIndex);
                
                log.info("处理第{}批钱包，数量: {}", (i / batchSize) + 1, batch.size());
                
                for (UserWallet wallet : batch) {
                    try {
                        refreshSingleWallet(wallet);
                        successCount++;
                        
                        // 避免请求过于频繁，每个钱包之间间隔
                        Thread.sleep(cacheConfig.getRequestIntervalMs());
                        
                    } catch (Exception e) {
                        log.error("刷新钱包余额失败: walletId={}, chain={}, address={}, error={}", 
                                wallet.getId(), wallet.getChain(), wallet.getAddress(), e.getMessage());
                        failCount++;
                    }
                }
                
                // 每批之间间隔1秒
                Thread.sleep(1000);
            }
            
            log.info("定时刷新钱包余额完成，成功: {}, 失败: {}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("定时刷新钱包余额任务失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 刷新单个钱包余额
     */
    private void refreshSingleWallet(UserWallet wallet) {
        log.debug("刷新钱包余额: walletId={}, chain={}, address={}", 
                wallet.getId(), wallet.getChain(), wallet.getAddress());
        
        // 查询最新余额
        UserWalletDTO.WalletInfo balanceInfo = blockchainQueryService.getWalletBalance(
                wallet.getChain(), wallet.getAddress());
        
        if (balanceInfo == null) {
            log.warn("获取钱包余额失败: walletId={}", wallet.getId());
            return;
        }
        
        // 更新数据库中的钱包余额
        boolean updated = false;
        if (balanceInfo.getBalance() != null && 
            balanceInfo.getBalance().compareTo(wallet.getBalance()) != 0) {
            wallet.setBalance(balanceInfo.getBalance());
            updated = true;
        }
        
        if (balanceInfo.getUsdValue() != null && 
            balanceInfo.getUsdValue().compareTo(wallet.getUsdValue()) != 0) {
            wallet.setUsdValue(balanceInfo.getUsdValue());
            updated = true;
        }
        
        if (updated) {
            userWalletRepository.save(wallet);
            log.debug("更新钱包余额到数据库: walletId={}, balance={}, usdValue={}", 
                    wallet.getId(), wallet.getBalance(), wallet.getUsdValue());
        }
        
        // 更新代币信息
        updateWalletTokens(wallet, balanceInfo.getTokens());
        
        // 更新缓存
        walletBalanceCacheService.cacheWalletBalance(
                wallet.getChain(), wallet.getAddress(), balanceInfo);
    }

    /**
     * 更新钱包代币信息
     */
    private void updateWalletTokens(UserWallet wallet, List<UserWalletDTO.TokenInfo> tokens) {
        if (tokens == null || tokens.isEmpty()) {
            return;
        }
        
        // 删除旧的代币记录
        walletTokenRepository.deleteByWalletId(wallet.getId());
        
        // 插入新的代币记录
        List<WalletToken> walletTokens = tokens.stream()
                .map(tokenInfo -> WalletToken.builder()
                        .wallet(wallet)
                        .symbol(tokenInfo.getSymbol())
                        .name(tokenInfo.getName())
                        .balance(tokenInfo.getBalance() != null ? tokenInfo.getBalance() : BigDecimal.ZERO)
                        .usdValue(tokenInfo.getUsdValue() != null ? tokenInfo.getUsdValue() : BigDecimal.ZERO)
                        .price(tokenInfo.getPrice() != null ? tokenInfo.getPrice() : BigDecimal.ZERO)
                        .change24h(tokenInfo.getChange24h() != null ? tokenInfo.getChange24h() : BigDecimal.ZERO)
                        .build())
                .collect(Collectors.toList());
        
        if (!walletTokens.isEmpty()) {
            walletTokenRepository.saveAll(walletTokens);
            log.debug("更新钱包代币信息: walletId={}, tokenCount={}", wallet.getId(), walletTokens.size());
        }
    }

    /**
     * 清理过期缓存（每小时执行一次）
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3,600,000毫秒
    public void cleanExpiredCache() {
        log.debug("开始清理过期缓存");
        try {
            walletBalanceCacheService.cleanExpiredCache();
            log.debug("清理过期缓存完成");
        } catch (Exception e) {
            log.error("清理过期缓存失败: {}", e.getMessage());
        }
    }


}
