package com.dddd.web3tools.service;

import com.dddd.web3tools.entity.SmartContract;
import org.web3j.protocol.core.methods.response.Transaction;

import java.math.BigInteger;
import java.util.List;

/**
 * 智能合约发现服务接口
 */
public interface SmartContractDiscoveryService {
    
    /**
     * 开始监控新合约
     */
    void startMonitoring();
    
    /**
     * 停止监控
     */
    void stopMonitoring();
    
    /**
     * 检查监控状态
     */
    boolean isMonitoring();
    
    /**
     * 处理单个区块，发现新合约
     */
    void processBlockForContracts(BigInteger blockNumber);
    
    /**
     * 分析交易，检查是否为合约部署或交互
     */
    void analyzeTransaction(Transaction transaction, BigInteger blockNumber, Long blockTimestamp);
    
    /**
     * 检查地址是否为合约
     */
    boolean isContract(String address);
    
    /**
     * 检查合约是否应该被过滤
     */
    boolean shouldFilterContract(String contractAddress, String deployerAddress);
    
    /**
     * 更新合约的24小时统计数据
     */
    void updateContractStats(String contractAddress);
    
    /**
     * 更新所有合约的统计数据
     */
    void updateAllContractStats();
    
    /**
     * 计算合约评分
     */
    void calculateContractScore(SmartContract contract);
    
    /**
     * 获取高分合约列表
     */
    List<SmartContract> getTopContracts(int limit);
    
    /**
     * 获取最近发现的合约
     */
    List<SmartContract> getRecentContracts(int hours, int limit);
    
    /**
     * 清理旧的交互记录
     */
    void cleanupOldInteractions();
    
    /**
     * 获取监控统计信息
     */
    MonitorStats getMonitorStats();
    
    /**
     * 监控统计信息
     */
    class MonitorStats {
        private long totalContractsDiscovered;
        private long activeContracts24h;
        private long filteredContracts;
        private BigInteger lastProcessedBlock;
        private boolean isRunning;
        private String startTime;
        private long processingErrors;
        
        // Getters and Setters
        public long getTotalContractsDiscovered() { return totalContractsDiscovered; }
        public void setTotalContractsDiscovered(long totalContractsDiscovered) { this.totalContractsDiscovered = totalContractsDiscovered; }
        
        public long getActiveContracts24h() { return activeContracts24h; }
        public void setActiveContracts24h(long activeContracts24h) { this.activeContracts24h = activeContracts24h; }
        
        public long getFilteredContracts() { return filteredContracts; }
        public void setFilteredContracts(long filteredContracts) { this.filteredContracts = filteredContracts; }
        
        public BigInteger getLastProcessedBlock() { return lastProcessedBlock; }
        public void setLastProcessedBlock(BigInteger lastProcessedBlock) { this.lastProcessedBlock = lastProcessedBlock; }
        
        public boolean isRunning() { return isRunning; }
        public void setRunning(boolean running) { isRunning = running; }
        
        public String getStartTime() { return startTime; }
        public void setStartTime(String startTime) { this.startTime = startTime; }
        
        public long getProcessingErrors() { return processingErrors; }
        public void setProcessingErrors(long processingErrors) { this.processingErrors = processingErrors; }
    }
}
