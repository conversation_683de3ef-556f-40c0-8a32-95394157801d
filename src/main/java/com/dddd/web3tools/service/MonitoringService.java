package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.monitoring.*;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 链上异常监控服务接口
 */
public interface MonitoringService {
    
    /**
     * 获取合约监控数据
     */
    ContractMonitoringListDTO getContractMonitoringData(
        String timeRange, String category, String sortBy, String sortOrder, 
        int page, int limit);
    
    /**
     * 获取合约历史数据
     */
    ContractHistoryDTO getContractHistory(Long contractId, String timeRange, String interval);
    
    /**
     * 获取系统统计概览
     */
    SystemOverviewDTO getSystemOverview(String timeRange);
    
    /**
     * 获取合约详细信息
     */
    ContractDetailDTO getContractDetails(Long contractId);
    
    /**
     * 搜索合约
     */
    ContractSearchDTO searchContracts(String query, String category, int limit);
}