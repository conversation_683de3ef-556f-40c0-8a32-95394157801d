package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.TweetAnalysisResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class GeminiService {


    @Autowired
    private TweetAnalysisService tweetAnalysisService;


    /**
     * 分析推文内容是否包含mint信息并符合推送条件
     * @param tweetContent 推文内容
     * @return 分析结果
     */
    public TweetAnalysisResult analyzeTweetForMint(String tweetContent) {
        log.info("使用Gemini分析推文mint内容");
        return tweetAnalysisService.analyzeTweet(tweetContent);
    }

    /**
     * 快速检查推文是否可能包含mint信息
     * @param tweetContent 推文内容
     * @return 是否可能包含mint信息
     */
    public boolean quickCheckMintContent(String tweetContent) {
        try {
            TweetAnalysisResult result = tweetAnalysisService.analyzeTweet(tweetContent);
            return result.isShouldNotify();
        } catch (Exception e) {
            log.error("快速检查mint内容失败", e);
            return false;
        }
    }

    /**
     * 批量分析推文
     * @param tweetContents 推文内容列表
     * @return 符合条件的推文内容列表
     */
    public java.util.List<String> batchAnalyzeTweets(java.util.List<String> tweetContents) {
        java.util.List<String> qualifiedTweets = new java.util.ArrayList<>();

        for (String content : tweetContents) {
            try {
                TweetAnalysisResult result = tweetAnalysisService.analyzeTweet(content);
                if (result.isShouldNotify()) {
                    qualifiedTweets.add(content);
                    log.info("发现符合条件的mint推文: {}", content.substring(0, Math.min(100, content.length())));
                }
            } catch (Exception e) {
                log.error("分析推文失败: {}", content.substring(0, Math.min(50, content.length())), e);
            }
        }

        return qualifiedTweets;
    }
}
