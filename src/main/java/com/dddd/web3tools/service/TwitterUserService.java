package com.dddd.web3tools.service;

import com.dddd.web3tools.entity.Tweet;
import com.dddd.web3tools.entity.TwitterUser;
import org.springframework.data.domain.Page;

import java.util.List;

public interface TwitterUserService {

    void importTwitterUsersAndTweets(String listId);
    TwitterUser updateUser(TwitterUser updateRequest);
    List<Tweet> getTweetsByScreenName(String twitterName);
    Page<TwitterUser> searchUsers(String twitterName, int page, int size);
    void importUserAndTweetsByUsername(String username) throws Exception;
}
