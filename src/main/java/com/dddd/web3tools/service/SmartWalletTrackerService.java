package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.*;

/**
 * 聪明钱包追踪服务接口
 */
public interface SmartWalletTrackerService {

    /**
     * 获取聪明钱包列表
     */
    SmartWalletDTO.ListResponse getSmartWallets(SmartWalletDTO.QueryRequest request);

    /**
     * 获取单个聪明钱包详情
     */
    SmartWalletDTO getSmartWalletDetail(String walletId);

    /**
     * 获取钱包交易记录
     */
    TransactionDTO.ListResponse getWalletTransactions(String walletId, TransactionDTO.QueryRequest request);

    /**
     * 创建跟单任务
     */
    FollowTaskDTO createFollowTask(FollowTaskDTO.CreateRequest request);

    /**
     * 获取用户跟单任务列表
     */
    FollowTaskDTO.ListResponse getFollowTasks(FollowTaskDTO.QueryRequest request);

    /**
     * 更新跟单任务状态
     */
    FollowTaskDTO updateFollowTaskStatus(FollowTaskDTO.StatusUpdateRequest request);

    /**
     * 删除跟单任务
     */
    void deleteFollowTask(FollowTaskDTO.DeleteRequest request);

    /**
     * 获取跟单任务详情
     */
    TransactionDTO.TaskDetailResponse getFollowTaskDetail(String wallet, String taskId);

    /**
     * 获取平台统计数据
     */
    PlatformStatsDTO getPlatformStats();
}
