package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.UserWalletDTO;
import com.dddd.web3tools.entity.UserWallet;

import java.util.List;

/**
 * 钱包余额缓存服务接口
 */
public interface WalletBalanceCacheService {

    /**
     * 从缓存获取钱包余额信息
     */
    UserWalletDTO.WalletInfo getCachedWalletBalance(String chain, String address);

    /**
     * 缓存钱包余额信息
     */
    void cacheWalletBalance(String chain, String address, UserWalletDTO.WalletInfo walletInfo);

    /**
     * 删除钱包余额缓存
     */
    void removeCachedWalletBalance(String chain, String address);

    /**
     * 获取钱包余额（优先从缓存，缓存未命中则查询区块链并缓存）
     */
    UserWalletDTO.WalletInfo getWalletBalance(String chain, String address);

    /**
     * 批量刷新钱包余额缓存
     */
    void refreshWalletBalancesCache(List<UserWallet> wallets);

    /**
     * 清理过期缓存
     */
    void cleanExpiredCache();
}
