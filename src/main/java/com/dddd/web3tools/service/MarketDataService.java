package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.MarketDataDTO;
import java.math.BigDecimal;
import java.util.List;

/**
 * 市场数据服务接口
 */
public interface MarketDataService {

    /**
     * 获取代币价格
     */
    MarketDataDTO getTokenPrices(List<String> symbols);

    /**
     * 获取单个代币价格
     */
    BigDecimal getTokenPrice(String symbol);

    /**
     * 获取代币24小时涨跌幅
     */
    BigDecimal getToken24hChange(String symbol);

    /**
     * 刷新价格缓存
     */
    void refreshPriceCache();
}
