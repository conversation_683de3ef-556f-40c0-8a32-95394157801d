package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.UserWalletDTO;
import java.math.BigDecimal;
import java.util.List;

/**
 * 区块链查询服务接口
 */
public interface BlockchainQueryService {

    /**
     * 获取钱包余额和代币信息
     */
    UserWalletDTO.WalletInfo getWalletBalance(String chain, String address);

    /**
     * 验证钱包地址格式
     */
    boolean isValidAddress(String chain, String address);

    /**
     * 获取ETH钱包余额
     */
    BigDecimal getEthBalance(String address);

    /**
     * 获取ETH钱包代币列表
     */
    List<UserWalletDTO.TokenInfo> getEthTokens(String address);

    /**
     * 获取BTC钱包余额
     */
    BigDecimal getBtcBalance(String address);

    /**
     * 获取SOL钱包余额
     */
    BigDecimal getSolBalance(String address);

    /**
     * 获取SOL钱包代币列表
     */
    List<UserWalletDTO.TokenInfo> getSolTokens(String address);
}
