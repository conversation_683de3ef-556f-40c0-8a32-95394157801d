package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.*;

import java.util.List;

/**
 * 钱包管理服务接口
 */
public interface WalletManagementService {

    // ==================== 钱包管理接口 ====================

    /**
     * 获取用户所有钱包（通过钱包地址）
     */
    UserWalletDTO.WalletsResponse getUserWalletsByAddress(String address);

    /**
     * 添加钱包（通过钱包地址）
     */
    UserWalletDTO.WalletInfo addWalletByAddress(String ownerAddress, UserWalletDTO.AddWalletRequest request);

    /**
     * 更新钱包信息（通过钱包地址）
     */
    UserWalletDTO.WalletInfo updateWalletByAddress(String ownerAddress, String walletAddress, UserWalletDTO.UpdateWalletRequest request);


    /**
     * 删除钱包（通过钱包地址）
     */
    void deleteWalletByAddress(String ownerAddress, String walletAddress);

    /**
     * 批量删除钱包（通过钱包地址）
     */
    UserWalletDTO.BatchDeleteResponse batchDeleteWalletsByAddress(String ownerAddress, UserWalletDTO.BatchDeleteRequest request);


    // ==================== 资产数据查询接口 ====================

    /**
     * 刷新钱包余额（通过钱包地址）
     */
    UserWalletDTO.RefreshResponse refreshWalletBalanceByAddress(String ownerAddress, String walletAddress);


    /**
     * 批量刷新所有钱包（通过钱包地址）
     */
    UserWalletDTO.RefreshAllResponse refreshAllWalletsByAddress(String ownerAddress);


    // ==================== 统计数据接口 ====================

    /**
     * 获取资产总览（通过钱包地址）
     */
    WalletOverviewDTO getWalletOverviewByAddress(String ownerAddress);

    /**
     * 获取资产趋势数据
     */
    WalletTrendDTO getWalletTrends(String ownerAddress, String timeRange);

    // ==================== 市场数据接口 ====================

    /**
     * 获取代币价格
     */
    MarketDataDTO getTokenPrices(String symbols);
}
