package com.dddd.web3tools.service;

import com.dddd.web3tools.entity.Tweet;

import java.util.List;

/**
 * 推特缓存服务接口
 */
public interface TweetCacheService {

    /**
     * 检查推特ID是否存在（优先从Redis缓存检查）
     * @param tweetId 推特ID
     * @return true如果存在，false如果不存在
     */
    boolean existsTweetId(String tweetId);

    /**
     * 批量保存推特到数据库
     * @param tweets 推特列表
     * @return 实际保存的推特数量
     */
    int batchSaveTweets(List<Tweet> tweets);

    /**
     * 将推特ID添加到Redis缓存
     * @param tweetId 推特ID
     */
    void addTweetIdToCache(String tweetId);

    /**
     * 初始化Redis缓存（从数据库加载最近7天的推特ID）
     */
    void initializeTweetIdsCache();

    /**
     * 清理过期的推特ID缓存
     */
    void cleanExpiredTweetIdsCache();

    /**
     * 获取缓存中的推特ID数量
     * @return 缓存中的推特ID数量
     */
    long getCachedTweetIdsCount();
}
