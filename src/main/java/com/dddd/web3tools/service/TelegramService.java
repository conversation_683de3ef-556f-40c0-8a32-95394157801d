package com.dddd.web3tools.service;

import com.dddd.web3tools.config.TelegramConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.telegram.telegrambots.bots.TelegramLongPollingBot;
import org.telegram.telegrambots.meta.api.methods.send.SendMessage;
import org.telegram.telegrambots.meta.api.objects.Update;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;

import jakarta.annotation.PostConstruct;
import java.util.List;

/**
 * Telegram服务类
 * 用于发送消息到Telegram群组
 */
@Slf4j
@Service
public class TelegramService extends TelegramLongPollingBot {

    @Autowired
    private TelegramConfig telegramConfig;

    private boolean isInitialized = false;

    @PostConstruct
    public void init() {
        try {
            if (telegramConfig.isEnabled() && 
                telegramConfig.getToken() != null && 
                !telegramConfig.getToken().equals("YOUR_BOT_TOKEN_HERE") &&
                telegramConfig.getChatId() != null && 
                !telegramConfig.getChatId().equals("YOUR_CHAT_ID_HERE")) {
                
                isInitialized = true;
                log.info("Telegram服务初始化成功");
            } else {
                log.warn("Telegram服务未启用或配置不完整，请检查配置文件");
            }
        } catch (Exception e) {
            log.error("Telegram服务初始化失败", e);
        }
    }

    @Override
    public String getBotUsername() {
        return "Web3ToolsBot"; // 可以配置化
    }

    @Override
    public String getBotToken() {
        return telegramConfig.getToken();
    }

    @Override
    public void onUpdateReceived(Update update) {
        // 这里可以处理接收到的消息，目前我们只需要发送功能
        log.debug("收到Telegram更新: {}", update);
    }

    /**
     * 发送单条消息到Telegram群组
     * 
     * @param message 要发送的消息
     */
    public void sendMessage(String message) {
        if (!isInitialized) {
            log.warn("Telegram服务未初始化，跳过消息发送");
            return;
        }

        try {
            SendMessage sendMessage = new SendMessage();
            sendMessage.setChatId(telegramConfig.getChatId());
            sendMessage.setText(message);
            sendMessage.setParseMode("HTML"); // 支持HTML格式

            execute(sendMessage);
            log.info("Telegram消息发送成功");
        } catch (TelegramApiException e) {
            log.error("发送Telegram消息失败", e);
        }
    }

    /**
     * 发送多条消息到Telegram群组
     * 
     * @param messages 要发送的消息列表
     * @param title 消息标题
     */
    public void sendMessages(List<String> messages, String title) {
        if (!isInitialized) {
            log.warn("Telegram服务未初始化，跳过消息发送");
            return;
        }

        if (messages == null || messages.isEmpty()) {
            log.warn("消息列表为空，跳过发送");
            return;
        }

        try {
            StringBuilder content = new StringBuilder();
            content.append("<b>").append(title).append("</b>\n\n");

            if (messages.size() > 1) {
                for (int i = 0; i < messages.size(); i++) {
                    content.append(messages.get(i));
                    if (i < messages.size() - 1) {
                        content.append("\n\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n");
                    }
                }
            } else {
                content.append(messages.get(0));
            }

            // Telegram消息长度限制为4096字符
            String messageText = content.toString();
            if (messageText.length() > 4096) {
                // 如果消息太长，分割发送
                sendLongMessage(messageText, title);
            } else {
                sendMessage(messageText);
            }

        } catch (Exception e) {
            log.error("发送Telegram消息列表失败", e);
        }
    }

    /**
     * 发送长消息（分割发送）
     * 
     * @param longMessage 长消息
     * @param title 标题
     */
    private void sendLongMessage(String longMessage, String title) {
        try {
            int maxLength = 4000; // 留一些余量
            int start = 0;
            int part = 1;

            while (start < longMessage.length()) {
                int end = Math.min(start + maxLength, longMessage.length());
                
                // 尝试在合适的位置分割（避免在单词中间分割）
                if (end < longMessage.length()) {
                    int lastNewline = longMessage.lastIndexOf('\n', end);
                    if (lastNewline > start) {
                        end = lastNewline;
                    }
                }

                String partMessage = longMessage.substring(start, end);
                if (part > 1) {
                    partMessage = String.format("<b>%s (第%d部分)</b>\n\n%s", title, part, partMessage);
                }

                sendMessage(partMessage);
                
                start = end;
                part++;
                
                // 避免发送过快
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            log.error("发送长Telegram消息失败", e);
        }
    }

    /**
     * 检查Telegram服务是否可用
     * 
     * @return true如果服务可用
     */
    public boolean isAvailable() {
        return isInitialized;
    }
}
