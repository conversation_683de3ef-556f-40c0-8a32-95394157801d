package com.dddd.web3tools.service;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * 博客认证服务
 */
@Service
public class BlogAuthService {
    
    @Value("${blog.admin.password:admin123}")
    private String adminPassword;
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    /**
     * 验证管理员密码
     */
    public boolean verifyPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }
        
        // 简单的明文密码比较，生产环境建议使用加密密码
        return adminPassword.equals(password.trim());
    }
    
    /**
     * 加密密码（预留接口，用于将来升级为加密密码）
     */
    public String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }
    
    /**
     * 验证加密密码（预留接口）
     */
    public boolean verifyEncodedPassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
