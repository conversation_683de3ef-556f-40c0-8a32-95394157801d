package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.AuthDTO;
import com.dddd.web3tools.dto.CategoryDTO;
import com.dddd.web3tools.dto.PostDTO;
import com.dddd.web3tools.vo.CategoryVO;
import com.dddd.web3tools.vo.PostVO;
import com.dddd.web3tools.vo.StatsVO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 博客服务接口
 */
public interface BlogService {
    
    // ========== 文章管理 ==========
    
    /**
     * 获取文章列表
     */
    Page<PostVO> getPosts(int page, int limit, String category, String search);
    
    /**
     * 根据ID获取文章
     */
    Optional<PostVO> getPostById(Long id);
    
    /**
     * 创建文章
     */
    PostVO createPost(PostDTO postDTO);
    
    /**
     * 更新文章
     */
    PostVO updatePost(Long id, PostDTO postDTO);
    
    /**
     * 删除文章
     */
    void deletePost(Long id);
    
    // ========== 分类管理 ==========
    
    /**
     * 获取所有分类
     */
    List<CategoryVO> getAllCategories();
    
    /**
     * 根据ID获取分类
     */
    Optional<CategoryVO> getCategoryById(String id);
    
    /**
     * 创建分类
     */
    CategoryVO createCategory(CategoryDTO categoryDTO);
    
    /**
     * 更新分类
     */
    CategoryVO updateCategory(String id, CategoryDTO categoryDTO);
    
    /**
     * 删除分类
     */
    void deleteCategory(String id);
    
    // ========== 文件上传 ==========
    
    /**
     * 上传Markdown文件
     */
    PostVO uploadMarkdownFile(MultipartFile file, String category);
    
    // ========== 认证 ==========
    
    /**
     * 验证管理员密码
     */
    Map<String, Object> verifyPassword(AuthDTO authDTO);
    
    // ========== 统计信息 ==========
    
    /**
     * 获取博客统计信息
     */
    StatsVO getBlogStats();
    
    // ========== 初始化 ==========
    
    /**
     * 初始化默认分类
     */
    void initDefaultCategories();
}
