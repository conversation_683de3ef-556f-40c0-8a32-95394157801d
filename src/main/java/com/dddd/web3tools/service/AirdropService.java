package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.ProjectDTO;
import com.dddd.web3tools.dto.ProjectStatsDTO;
import com.dddd.web3tools.dto.WalletDTO;
import com.dddd.web3tools.vo.PageResponse;
import org.springframework.core.io.Resource;

import java.util.List;

/**
 * 空投项目管理服务接口
 */
public interface AirdropService {

    // ==================== 项目管理接口 ====================

    /**
     * 获取项目列表
     */
    PageResponse<ProjectDTO> getProjects(ProjectDTO.QueryRequest request);

    /**
     * 创建项目
     */
    ProjectDTO createProject(ProjectDTO.CreateRequest request);

    /**
     * 更新项目
     */
    ProjectDTO updateProject(Long projectId, ProjectDTO.UpdateRequest request);

    /**
     * 删除项目
     */
    void deleteProject(Long projectId);

    /**
     * 根据ID获取项目详情
     */
    ProjectDTO getProjectById(Long projectId);

    // ==================== 钱包管理接口 ====================

    /**
     * 添加钱包到项目
     */
    WalletDTO addWalletToProject(Long projectId, WalletDTO.AddRequest request);

    /**
     * 批量导入钱包
     */
    WalletDTO.BatchImportResponse batchImportWallets(Long projectId, WalletDTO.BatchImportRequest request);

    /**
     * 更新钱包信息
     */
    WalletDTO updateWallet(Long projectId, Long walletId, WalletDTO.UpdateRequest request);

    /**
     * 删除钱包
     */
    void deleteWallet(Long projectId, Long walletId);

    /**
     * 获取项目中的钱包列表（根据添加者过滤）
     */
    PageResponse<WalletDTO> getProjectWallets(Long projectId, String addedBy, Integer page, Integer limit);

    /**
     * 获取项目中的所有钱包列表（管理员接口）
     */
    PageResponse<WalletDTO> getAllProjectWallets(Long projectId, Integer page, Integer limit);

    // ==================== 数据统计接口 ====================

    /**
     * 获取项目统计信息
     */
    ProjectStatsDTO getProjectStats(Long projectId);

    // ==================== 工具接口 ====================

    /**
     * 验证钱包地址
     */
    WalletDTO.ValidationResponse validateWalletAddress(WalletDTO.ValidationRequest request);

    /**
     * 导出项目数据
     */
    Resource exportProjectData(Long projectId, String format);

    /**
     * 获取表格列配置
     */
    List<ProjectStatsDTO.TableColumnDTO> getTableColumns(Long projectId);

    /**
     * 更新表格列配置
     */
    List<ProjectStatsDTO.TableColumnDTO> updateTableColumns(Long projectId, List<ProjectStatsDTO.TableColumnDTO> columns);

    // ==================== 区块链数据同步接口 ====================

    /**
     * 同步钱包余额
     */
    void syncWalletBalance(Long projectId, String walletAddress);

    /**
     * 批量同步项目中所有钱包余额
     */
    void syncAllWalletBalances(Long projectId);

    /**
     * 更新钱包最后活动时间
     */
    void updateWalletLastActivity(String walletAddress, String chain);

    // ==================== 权限验证接口 ====================

    /**
     * 验证用户是否有项目访问权限
     */
    boolean hasProjectAccess(Long projectId, String userAddress);

    /**
     * 验证用户是否有项目编辑权限
     */
    boolean hasProjectEditAccess(Long projectId, String userAddress);

    /**
     * 验证用户是否是管理员
     */
    boolean isAdmin(String userAddress);
}
