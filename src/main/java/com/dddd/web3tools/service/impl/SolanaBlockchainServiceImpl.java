package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.dto.TransactionDTO;
import com.dddd.web3tools.service.SolanaBlockchainService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import jakarta.annotation.PostConstruct;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Solana区块链服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SolanaBlockchainServiceImpl implements SolanaBlockchainService {

    private final ObjectMapper objectMapper;
    private final RestTemplate restTemplate = new RestTemplate();

    @Value("${solana.rpc.url:https://api.mainnet-beta.solana.com}")
    private String solanaRpcUrl;

    @Value("${solana.rpc.rate-limit:5}")
    private int rpcRateLimit; // 每秒请求限制

    @Value("${solana.rpc.retry-attempts:3}")
    private int retryAttempts; // 重试次数

    @Value("${solana.rpc.retry-delay:1000}")
    private long retryDelay; // 重试延迟(毫秒)

    // 请求频率控制
    private final AtomicLong lastRequestTime = new AtomicLong(0);
    private long requestInterval = 200; // 默认请求间隔(毫秒)

    @PostConstruct
    private void init() {
        this.requestInterval = 1000 / Math.max(rpcRateLimit, 1); // 计算请求间隔
        log.info("Solana RPC配置: URL={}, 频率限制={}/s, 请求间隔={}ms", 
            solanaRpcUrl, rpcRateLimit, requestInterval);
    }

    @Override
    public List<TransactionDTO> getWalletTransactions(String walletAddress, TransactionDTO.QueryRequest request) {
        try {
            log.info("查询钱包交易记录: address={}", walletAddress);

            // 构建RPC请求获取交易签名
            Map<String, Object> rpcRequest = buildGetSignaturesRequest(walletAddress, request.getLimit());
            
            // 使用重试机制发送请求
            String responseBody = executeRpcRequestWithRetry(rpcRequest);
            
            if (responseBody != null) {
                return parseTransactionResponse(responseBody, walletAddress);
            } else {
                log.error("Solana RPC请求失败");
                return new ArrayList<>();
            }

        } catch (Exception e) {
            log.error("查询钱包交易记录失败: address={}", walletAddress, e);
            return new ArrayList<>();
        }
    }

    @Override
    public BigDecimal getTokenPrice(String tokenAddress) {
        try {
            // TODO: 集成Jupiter API或其他价格API获取代币价格
            // 这里可以调用Jupiter Price API
            log.debug("获取代币价格: tokenAddress={}", tokenAddress);
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取代币价格失败: tokenAddress={}", tokenAddress, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Long getTokenBalance(String walletAddress, String tokenAddress) {
        try {
            log.debug("获取代币余额: wallet={}, token={}", walletAddress, tokenAddress);
            
            // 使用HTTP API查询代币余额，避免使用高频率的RPC调用
            Map<String, Object> rpcRequest = buildGetTokenBalanceRequest(walletAddress, tokenAddress);
            String responseBody = executeRpcRequestWithRetry(rpcRequest);
            
            if (responseBody != null) {
                return parseTokenBalanceResponse(responseBody);
            }
            
            return 0L;
        } catch (Exception e) {
            log.error("获取代币余额失败: wallet={}, token={}", walletAddress, tokenAddress, e);
            return 0L;
        }
    }

    @Override
    public boolean isValidWalletAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 验证Solana地址格式：32字节，Base58编码
            if (address.length() < 32 || address.length() > 44) {
                return false;
            }
            // 简单的Base58字符检查
            return address.matches("[1-9A-HJ-NP-Za-km-z]+");
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public String sendTransaction(String privateKey, String toAddress, String tokenAddress, Long amount) {
        try {
            log.info("发送交易: to={}, token={}, amount={}", toAddress, tokenAddress, amount);

            // TODO: 实现使用sava库的交易发送逻辑
            // 这里需要：
            // 1. 从私钥创建账户
            // 2. 构建交易指令
            // 3. 签名并发送交易
            
            // 暂时返回模拟的交易签名
            String mockSignature = "mock_tx_" + System.currentTimeMillis();
            log.info("模拟交易发送成功: signature={}", mockSignature);
            
            return mockSignature;
            
        } catch (Exception e) {
            log.error("发送交易失败", e);
            throw new RuntimeException("发送交易失败: " + e.getMessage());
        }
    }

    @Override
    public Long getSolBalance(String walletAddress) {
        try {
            log.debug("获取SOL余额: address={}", walletAddress);
            // TODO: 实现使用sava库的SOL余额查询
            return 0L;
        } catch (Exception e) {
            log.error("获取SOL余额失败: address={}", walletAddress, e);
            return 0L;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 执行RPC请求并处理频率限制和重试
     */
    private String executeRpcRequestWithRetry(Map<String, Object> rpcRequest) {
        for (int attempt = 1; attempt <= retryAttempts; attempt++) {
            try {
                // 频率控制
                rateLimitControl();
                
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.APPLICATION_JSON);
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(rpcRequest, headers);
                
                ResponseEntity<String> response = restTemplate.exchange(
                    solanaRpcUrl, HttpMethod.POST, entity, String.class);

                if (response.getStatusCode() == HttpStatus.OK) {
                    return response.getBody();
                } else {
                    log.warn("RPC请求失败，状态码: {}, 尝试次数: {}/{}", 
                        response.getStatusCode(), attempt, retryAttempts);
                }

            } catch (Exception e) {
                log.warn("RPC请求异常，尝试次数: {}/{}, 错误: {}", 
                    attempt, retryAttempts, e.getMessage());
                
                if (attempt < retryAttempts) {
                    try {
                        // 指数退避延迟
                        long delay = retryDelay * (long) Math.pow(2, attempt - 1);
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        log.error("RPC请求失败，已达到最大重试次数: {}", retryAttempts);
        return null;
    }

    /**
     * 请求频率控制
     */
    private void rateLimitControl() {
        long currentTime = System.currentTimeMillis();
        long lastTime = lastRequestTime.get();
        long timeSinceLastRequest = currentTime - lastTime;
        
        if (timeSinceLastRequest < requestInterval) {
            try {
                long sleepTime = requestInterval - timeSinceLastRequest;
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        lastRequestTime.set(System.currentTimeMillis());
    }

    private Map<String, Object> buildGetSignaturesRequest(String address, Integer limit) {
        Map<String, Object> request = new HashMap<>();
        request.put("jsonrpc", "2.0");
        request.put("id", 1);
        request.put("method", "getSignaturesForAddress");
        
        List<Object> params = new ArrayList<>();
        params.add(address);
        
        Map<String, Object> options = new HashMap<>();
        options.put("limit", Math.min(limit != null ? limit : 50, 1000));
        params.add(options);
        
        request.put("params", params);
        return request;
    }

    private Map<String, Object> buildGetTokenBalanceRequest(String walletAddress, String tokenAddress) {
        Map<String, Object> request = new HashMap<>();
        request.put("jsonrpc", "2.0");
        request.put("id", 1);
        request.put("method", "getTokenAccountsByOwner");
        
        List<Object> params = new ArrayList<>();
        params.add(walletAddress);
        
        Map<String, Object> filter = new HashMap<>();
        filter.put("mint", tokenAddress);
        params.add(filter);
        
        Map<String, Object> config = new HashMap<>();
        config.put("encoding", "jsonParsed");
        params.add(config);
        
        request.put("params", params);
        return request;
    }

    private List<TransactionDTO> parseTransactionResponse(String responseBody, String walletAddress) {
        List<TransactionDTO> transactions = new ArrayList<>();
        
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode result = root.get("result");
            
            if (result != null && result.isArray()) {
                for (JsonNode txNode : result) {
                    TransactionDTO transaction = parseTransactionNode(txNode, walletAddress);
                    if (transaction != null) {
                        transactions.add(transaction);
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("解析交易响应失败", e);
        }
        
        return transactions;
    }

    private TransactionDTO parseTransactionNode(JsonNode txNode, String walletAddress) {
        try {
            String signature = txNode.get("signature").asText();
            Long blockTime = txNode.has("blockTime") ? txNode.get("blockTime").asLong() : null;
            Long slot = txNode.has("slot") ? txNode.get("slot").asLong() : null;
            
            // TODO: 调用getTransaction获取详细交易信息来解析代币转账
            // 这里需要进一步解析交易内容来获取代币信息、金额等
            
            return TransactionDTO.builder()
                .id("tx_" + signature.substring(0, 8))
                .signature(signature)
                .blockTime(blockTime)
                .slot(slot)
                .timestamp(blockTime != null ? 
                    LocalDateTime.ofInstant(Instant.ofEpochSecond(blockTime), ZoneOffset.UTC) : null)
                .status("completed")
                .action("unknown") // 需要进一步解析确定是买入还是卖出
                .build();
                
        } catch (Exception e) {
            log.error("解析交易节点失败", e);
            return null;
        }
    }

    private Long parseTokenBalanceResponse(String responseBody) {
        try {
            JsonNode root = objectMapper.readTree(responseBody);
            JsonNode result = root.get("result");
            
            if (result != null && result.has("value") && result.get("value").isArray()) {
                JsonNode accounts = result.get("value");
                if (accounts.size() > 0) {
                    JsonNode account = accounts.get(0);
                    JsonNode accountInfo = account.get("account");
                    JsonNode data = accountInfo.get("data");
                    JsonNode parsed = data.get("parsed");
                    JsonNode info = parsed.get("info");
                    JsonNode tokenAmount = info.get("tokenAmount");
                    
                    return tokenAmount.get("amount").asLong();
                }
            }
            
        } catch (Exception e) {
            log.error("解析代币余额响应失败", e);
        }
        
        return 0L;
    }
}
