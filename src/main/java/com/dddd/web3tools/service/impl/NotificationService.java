package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.config.NotificationConfig;
import com.dddd.web3tools.constant.RedisKeyConstant;
import com.dddd.web3tools.entity.BlacklistUser;
import com.dddd.web3tools.entity.Keyword;
import com.dddd.web3tools.repository.BlacklistUserRepository;
import com.dddd.web3tools.repository.KeywordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.stream.Collectors;


@Slf4j
@Service
public class NotificationService {

    @Autowired
    private KeywordRepository keywordRepository;

    @Autowired
    private BlacklistUserRepository blacklistUserRepository;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private final JavaMailSender mailSender;
    private final NotificationConfig config;
    private static final long CACHE_TTL_SECONDS = 3600; // 1小时 = 60*60秒

    Logger logger = Logger.getLogger(NotificationService.class.getName());

    public NotificationService(JavaMailSender mailSender, NotificationConfig config) {
        this.mailSender = mailSender;
        this.config = config;
    }

    public void processNotify(List<String> texts) {
        logger.info("符合条件的推文: " + texts + " 已发送通知邮件");
        sendNotification(texts, "搜索关键字结果如下:");
    }

    /**
     * 检查是否应该保存推文
     * 粉丝数量大于等于50且文本中不包含指定的关键字  不包含是为了过滤广告
     * 同时文本中包含指定的关键字
     * followersCount >= 50 这个暂时不过滤了，怕把重要的信息过滤了
     *
     * @param text           推文文本
     * @param followersCount 粉丝数
     * @return 如果应该保存推文，则返回true，否则返回false
     */
    public boolean shouldSaveTweet(String text, int followersCount,String userid) {
            return followersCount >= 20 && !shouldDeleteTweet(text) && isTextMatched(text) && shouldRequiredTweet(text) && !filterNormalUser(userid);
    }

    public boolean isTextMatched(String text) {
        Set<String> keywords = getKeywordsByType(Keyword.TYPE_BASE, RedisKeyConstant.KEYWORDS_CACHE_KEY);
        return keywords.stream().anyMatch(keyword -> text.toLowerCase().contains(keyword.toLowerCase()));
    }

    public boolean shouldDeleteTweet(String text) {
        Set<String> deleteKeywords = getKeywordsByType(Keyword.TYPE_DELETE, RedisKeyConstant.DELETE_KEYWORDS_CACHE_KEY);// 这里添加你想过滤的关键字
        return deleteKeywords.stream().anyMatch(keyword -> text.toLowerCase().contains(keyword.toLowerCase()));
    }

    public boolean shouldRequiredTweet(String text) {
        Set<String> requiredKeywords = getKeywordsByType(Keyword.TYPE_REQUIRED, RedisKeyConstant.REQUIRED_KEYWORDS_CACHE_KEY);// 这里添加你想过滤的关键字
        return requiredKeywords.stream().anyMatch(keyword -> text.toLowerCase().contains(keyword.toLowerCase()));
    }

    public boolean filterNormalUser(String uerid) {
        Set<String> blacklistUserIds = getBlacklistUserIds();
        return blacklistUserIds.stream().anyMatch(keyword -> uerid.toLowerCase().contains(keyword.toLowerCase()));
    }


    // 通用关键词获取方法
    private Set<String> getKeywordsByType(String type, String cacheKey) {
        Set<String> keywords = redisTemplate.opsForSet().members(cacheKey);
        if (keywords == null || keywords.isEmpty()) {
            keywords = keywordRepository.findByType(type)
                    .stream()
                    .map(Keyword::getWord)
                    .collect(Collectors.toSet());

            if (!keywords.isEmpty()) {
                redisTemplate.opsForSet().add(cacheKey, keywords.toArray(new String[0]));
                redisTemplate.expire(cacheKey, CACHE_TTL_SECONDS, TimeUnit.SECONDS);
            }
        }
        return keywords;
    }

    // 新增缓存查询方法
    private Set<String> getBlacklistUserIds() {
        Set<String> userIds = redisTemplate.opsForSet().members(RedisKeyConstant.BLACKLIST_CACHE_KEY);
        if (userIds == null || userIds.isEmpty()) {
            userIds = blacklistUserRepository.findAll()
                .stream()
                .map(BlacklistUser::getUserId)
                .collect(Collectors.toSet());
            log.info("黑名单用户id: " + userIds + " 已缓存到redis，长度为: " + userIds.size() + " 个");
            if (!userIds.isEmpty()) {
                redisTemplate.opsForSet().add(RedisKeyConstant.BLACKLIST_CACHE_KEY, userIds.toArray(new String[0]));
                redisTemplate.expire(RedisKeyConstant.BLACKLIST_CACHE_KEY, 3600, TimeUnit.SECONDS);
            }
        }
        return userIds;
    }

    public void sendNotification(List<String> matchedTexts, String theme) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(config.getEmailFrom());
            message.setTo(config.getEmailTo());
            message.setSubject(theme);

            StringBuilder content = new StringBuilder("以下内容匹配了关键词:\n\n");
            if (matchedTexts.size() > 1) {
                for (String text : matchedTexts) {
                    content.append(text)
                            .append("\n\n++++++++++++++++新消息++++++++++++++++\n\n");
                }
                message.setText(content.toString());
            } else {
                message.setText(matchedTexts.get(0));
            }
            mailSender.send(message);
//            // 加推波波
//            message.setTo("<EMAIL>");
//            mailSender.send(message);
//            // 蔡总
//            message.setTo("<EMAIL>");
//            mailSender.send(message);
//            // 杜总
//            message.setTo("<EMAIL>");
//            mailSender.send(message);
//            // 俊总
//            message.setTo("<EMAIL>");
//            mailSender.send(message);
        } catch (MailException e) {
            logger.info("邮件发送失败: " + e.getMessage());
        }
    }
}