package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.dto.MarketDataDTO;
import com.dddd.web3tools.service.MarketDataService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 市场数据服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MarketDataServiceImpl implements MarketDataService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${market.coinbase.api-url:https://api.coinbase.com/v2}")
    private String coinbaseApiUrl;

    @Value("${market.binance.api-url:https://api.binance.com/api/v3}")
    private String binanceApiUrl;

    @Value("${market.cryptocompare.api-url:https://min-api.cryptocompare.com/data}")
    private String cryptoCompareApiUrl;

    private static final String PRICE_CACHE_PREFIX = "market:price:";
    private static final String CHANGE_CACHE_PREFIX = "market:change:";
    private static final long CACHE_EXPIRE_MINUTES = 5; // 缓存5分钟

    // 支持的代币符号映射
    private static final Map<String, String> SYMBOL_MAPPING = new HashMap<>();

    static {
        SYMBOL_MAPPING.put("ETH", "ETH");
        SYMBOL_MAPPING.put("BTC", "BTC");
        SYMBOL_MAPPING.put("SOL", "SOL");
        SYMBOL_MAPPING.put("USDT", "USDT");
        SYMBOL_MAPPING.put("USDC", "USDC");
        SYMBOL_MAPPING.put("BNB", "BNB");
        SYMBOL_MAPPING.put("ADA", "ADA");
        SYMBOL_MAPPING.put("DOT", "DOT");
        SYMBOL_MAPPING.put("MATIC", "MATIC");
        SYMBOL_MAPPING.put("AVAX", "AVAX");
    }

    @Override
    public MarketDataDTO getTokenPrices(List<String> symbols) {
        log.info("获取代币价格: symbols={}", symbols);

        Map<String, MarketDataDTO.TokenPrice> prices = new HashMap<>();

        for (String symbol : symbols) {
            String upperSymbol = symbol.toUpperCase();
            
            // 先从缓存获取
            MarketDataDTO.TokenPrice cachedPrice = getCachedPrice(upperSymbol);
            if (cachedPrice != null) {
                prices.put(upperSymbol, cachedPrice);
                continue;
            }

            // 缓存未命中，获取实时数据
            MarketDataDTO.TokenPrice tokenPrice = fetchTokenPrice(upperSymbol);
            if (tokenPrice != null) {
                prices.put(upperSymbol, tokenPrice);
                // 缓存价格数据
                cachePrice(upperSymbol, tokenPrice);
            }
        }

        return MarketDataDTO.builder()
                .prices(prices)
                .build();
    }

    @Override
    public BigDecimal getTokenPrice(String symbol) {
        String upperSymbol = symbol.toUpperCase();
        
        // 先从缓存获取
        String cacheKey = PRICE_CACHE_PREFIX + upperSymbol;
        Object cachedPrice = redisTemplate.opsForValue().get(cacheKey);
        if (cachedPrice != null) {
            return new BigDecimal(cachedPrice.toString());
        }

        // 缓存未命中，获取实时价格
        BigDecimal price = fetchRealTimePrice(upperSymbol);
        if (price != null) {
            // 缓存价格
            redisTemplate.opsForValue().set(cacheKey, price.toString(), CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        }

        return price != null ? price : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getToken24hChange(String symbol) {
        String upperSymbol = symbol.toUpperCase();
        
        // 先从缓存获取
        String cacheKey = CHANGE_CACHE_PREFIX + upperSymbol;
        Object cachedChange = redisTemplate.opsForValue().get(cacheKey);
        if (cachedChange != null) {
            return new BigDecimal(cachedChange.toString());
        }

        // 缓存未命中，获取实时涨跌幅
        BigDecimal change = fetchRealTimeChange(upperSymbol);
        if (change != null) {
            // 缓存涨跌幅
            redisTemplate.opsForValue().set(cacheKey, change.toString(), CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        }

        return change != null ? change : BigDecimal.ZERO;
    }

    @Override
    public void refreshPriceCache() {
        log.info("刷新价格缓存");
        
        List<String> symbols = Arrays.asList("ETH", "BTC", "SOL");
        for (String symbol : symbols) {
            try {
                // 删除旧缓存
                redisTemplate.delete(PRICE_CACHE_PREFIX + symbol);
                redisTemplate.delete(CHANGE_CACHE_PREFIX + symbol);
                
                // 重新获取并缓存
                getTokenPrice(symbol);
                getToken24hChange(symbol);
            } catch (Exception e) {
                log.error("刷新{}价格缓存失败: {}", symbol, e.getMessage());
            }
        }
    }

    /**
     * 从缓存获取价格数据
     */
    private MarketDataDTO.TokenPrice getCachedPrice(String symbol) {
        try {
            String priceKey = PRICE_CACHE_PREFIX + symbol;
            String changeKey = CHANGE_CACHE_PREFIX + symbol;
            
            Object cachedPrice = redisTemplate.opsForValue().get(priceKey);
            Object cachedChange = redisTemplate.opsForValue().get(changeKey);
            
            if (cachedPrice != null && cachedChange != null) {
                return MarketDataDTO.TokenPrice.builder()
                        .symbol(symbol)
                        .name(getTokenName(symbol))
                        .price(new BigDecimal(cachedPrice.toString()))
                        .change24h(new BigDecimal(cachedChange.toString()))
                        .volume24h(BigDecimal.ZERO) // 从缓存获取时暂不提供交易量
                        .marketCap(BigDecimal.ZERO) // 从缓存获取时暂不提供市值
                        .updatedAt(LocalDateTime.now())
                        .build();
            }
        } catch (Exception e) {
            log.error("获取缓存价格失败: symbol={}, error={}", symbol, e.getMessage());
        }
        return null;
    }

    /**
     * 缓存价格数据
     */
    private void cachePrice(String symbol, MarketDataDTO.TokenPrice tokenPrice) {
        try {
            String priceKey = PRICE_CACHE_PREFIX + symbol;
            String changeKey = CHANGE_CACHE_PREFIX + symbol;
            
            redisTemplate.opsForValue().set(priceKey, tokenPrice.getPrice().toString(), 
                    CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
            redisTemplate.opsForValue().set(changeKey, tokenPrice.getChange24h().toString(), 
                    CACHE_EXPIRE_MINUTES, TimeUnit.MINUTES);
        } catch (Exception e) {
            log.error("缓存价格失败: symbol={}, error={}", symbol, e.getMessage());
        }
    }

    /**
     * 获取代币价格（使用免费API）
     */
    private MarketDataDTO.TokenPrice fetchTokenPrice(String symbol) {
        try {
            String normalizedSymbol = SYMBOL_MAPPING.get(symbol.toUpperCase());
            if (normalizedSymbol == null) {
                log.warn("不支持的代币符号: {}", symbol);
                return null;
            }

            // 优先使用Binance API（免费且稳定）
            MarketDataDTO.TokenPrice price = fetchFromBinance(normalizedSymbol);
            if (price != null) {
                return price;
            }

            // 备用：使用CryptoCompare API
            price = fetchFromCryptoCompare(normalizedSymbol);
            if (price != null) {
                return price;
            }

            // 最后备用：使用Coinbase API
            return fetchFromCoinbase(normalizedSymbol);

        } catch (Exception e) {
            log.error("获取代币价格失败: symbol={}, error={}", symbol, e.getMessage());
        }
        return null;
    }

    /**
     * 从Binance API获取价格
     */
    private MarketDataDTO.TokenPrice fetchFromBinance(String symbol) {
        try {
            // Binance API需要交易对格式，如BTCUSDT
            String tradingPair = symbol + "USDT";
            if ("USDT".equals(symbol) || "USDC".equals(symbol)) {
                // 稳定币价格固定为1
                return MarketDataDTO.TokenPrice.builder()
                        .symbol(symbol)
                        .name(getTokenName(symbol))
                        .price(BigDecimal.ONE)
                        .change24h(BigDecimal.ZERO)
                        .volume24h(BigDecimal.ZERO)
                        .marketCap(BigDecimal.ZERO)
                        .updatedAt(LocalDateTime.now())
                        .build();
            }

            String url = String.format("%s/ticker/24hr?symbol=%s", binanceApiUrl, tradingPair);
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JsonNode rootNode = objectMapper.readTree(response.getBody());

                BigDecimal price = new BigDecimal(rootNode.get("lastPrice").asText());
                BigDecimal change24h = new BigDecimal(rootNode.get("priceChangePercent").asText());
                BigDecimal volume24h = new BigDecimal(rootNode.get("volume").asText());

                return MarketDataDTO.TokenPrice.builder()
                        .symbol(symbol)
                        .name(getTokenName(symbol))
                        .price(price)
                        .change24h(change24h)
                        .volume24h(volume24h)
                        .marketCap(BigDecimal.ZERO) // Binance不提供市值数据
                        .updatedAt(LocalDateTime.now())
                        .build();
            }
        } catch (Exception e) {
            log.debug("Binance API获取价格失败: symbol={}, error={}", symbol, e.getMessage());
        }
        return null;
    }

    /**
     * 从CryptoCompare API获取价格
     */
    private MarketDataDTO.TokenPrice fetchFromCryptoCompare(String symbol) {
        try {
            String url = String.format("%s/pricemultifull?fsyms=%s&tsyms=USD", cryptoCompareApiUrl, symbol);
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JsonNode rootNode = objectMapper.readTree(response.getBody());
                JsonNode dataNode = rootNode.path("RAW").path(symbol).path("USD");

                if (!dataNode.isMissingNode()) {
                    BigDecimal price = new BigDecimal(dataNode.get("PRICE").asText());
                    BigDecimal change24h = new BigDecimal(dataNode.get("CHANGEPCT24HOUR").asText());
                    BigDecimal volume24h = new BigDecimal(dataNode.get("VOLUME24HOUR").asText());
                    BigDecimal marketCap = dataNode.has("MKTCAP") ?
                            new BigDecimal(dataNode.get("MKTCAP").asText()) : BigDecimal.ZERO;

                    return MarketDataDTO.TokenPrice.builder()
                            .symbol(symbol)
                            .name(getTokenName(symbol))
                            .price(price)
                            .change24h(change24h)
                            .volume24h(volume24h)
                            .marketCap(marketCap)
                            .updatedAt(LocalDateTime.now())
                            .build();
                }
            }
        } catch (Exception e) {
            log.debug("CryptoCompare API获取价格失败: symbol={}, error={}", symbol, e.getMessage());
        }
        return null;
    }

    /**
     * 从Coinbase API获取价格
     */
    private MarketDataDTO.TokenPrice fetchFromCoinbase(String symbol) {
        try {
            String url = String.format("%s/exchange-rates?currency=%s", coinbaseApiUrl, symbol);
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JsonNode rootNode = objectMapper.readTree(response.getBody());
                JsonNode ratesNode = rootNode.path("data").path("rates");

                if (ratesNode.has("USD")) {
                    BigDecimal price = new BigDecimal(ratesNode.get("USD").asText());

                    return MarketDataDTO.TokenPrice.builder()
                            .symbol(symbol)
                            .name(getTokenName(symbol))
                            .price(price)
                            .change24h(BigDecimal.ZERO) // Coinbase基础API不提供24h变化
                            .volume24h(BigDecimal.ZERO)
                            .marketCap(BigDecimal.ZERO)
                            .updatedAt(LocalDateTime.now())
                            .build();
                }
            }
        } catch (Exception e) {
            log.debug("Coinbase API获取价格失败: symbol={}, error={}", symbol, e.getMessage());
        }
        return null;
    }

    /**
     * 获取实时价格
     */
    private BigDecimal fetchRealTimePrice(String symbol) {
        try {
            MarketDataDTO.TokenPrice tokenPrice = fetchTokenPrice(symbol);
            return tokenPrice != null ? tokenPrice.getPrice() : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取实时价格失败: symbol={}, error={}", symbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取实时涨跌幅
     */
    private BigDecimal fetchRealTimeChange(String symbol) {
        try {
            MarketDataDTO.TokenPrice tokenPrice = fetchTokenPrice(symbol);
            return tokenPrice != null ? tokenPrice.getChange24h() : BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取实时涨跌幅失败: symbol={}, error={}", symbol, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取代币名称
     */
    private String getTokenName(String symbol) {
        switch (symbol) {
            case "ETH": return "Ethereum";
            case "BTC": return "Bitcoin";
            case "SOL": return "Solana";
            default: return symbol;
        }
    }


}
