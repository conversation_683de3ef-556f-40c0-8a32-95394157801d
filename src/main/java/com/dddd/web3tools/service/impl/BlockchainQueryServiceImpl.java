package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.dto.UserWalletDTO;
import com.dddd.web3tools.service.BlockchainQueryService;
import com.dddd.web3tools.service.MarketDataService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameterName;
import org.web3j.protocol.http.HttpService;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 区块链查询服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BlockchainQueryServiceImpl implements BlockchainQueryService {

    private final MarketDataService marketDataService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${blockchain.ethereum.rpc-url:https://mainnet.infura.io/v3/your_project_id}")
    private String ethereumRpcUrl;

    @Value("${blockchain.bitcoin.api-url:https://blockstream.info/api}")
    private String bitcoinApiUrl;

    @Value("${blockchain.solana.rpc-url:https://api.mainnet-beta.solana.com}")
    private String solanaRpcUrl;

    // 地址格式验证正则表达式
    private static final Pattern ETH_ADDRESS_PATTERN = Pattern.compile("^0x[a-fA-F0-9]{40}$");
    private static final Pattern BTC_ADDRESS_PATTERN = Pattern.compile("^(bc1|[13])[a-zA-HJ-NP-Z0-9]{25,62}$");
    private static final Pattern SOL_ADDRESS_PATTERN = Pattern.compile("^[1-9A-HJ-NP-Za-km-z]{32,44}$");

    @Override
    public UserWalletDTO.WalletInfo getWalletBalance(String chain, String address) {
        log.info("获取钱包余额: chain={}, address={}", chain, address);

        if (!isValidAddress(chain, address)) {
            throw new IllegalArgumentException("无效的钱包地址格式");
        }

        UserWalletDTO.WalletInfo.WalletInfoBuilder builder = UserWalletDTO.WalletInfo.builder()
                .address(address);

        List<UserWalletDTO.TokenInfo> tokens = new ArrayList<>();
        BigDecimal totalUsdValue = BigDecimal.ZERO;

        try {
            switch (chain.toUpperCase()) {
                case "EVM":
                    BigDecimal ethBalance = getEthBalance(address);
                    List<UserWalletDTO.TokenInfo> ethTokens = getEthTokens(address);
                    
                    // 添加ETH主币
                    if (ethBalance.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal ethPrice = marketDataService.getTokenPrice("ETH");
                        BigDecimal ethUsdValue = ethBalance.multiply(ethPrice);
                        BigDecimal ethChange24h = marketDataService.getToken24hChange("ETH");
                        
                        tokens.add(UserWalletDTO.TokenInfo.builder()
                                .symbol("ETH")
                                .name("Ethereum")
                                .balance(ethBalance)
                                .usdValue(ethUsdValue)
                                .price(ethPrice)
                                .change24h(ethChange24h)
                                .build());
                        
                        totalUsdValue = totalUsdValue.add(ethUsdValue);
                    }
                    
                    // 添加ERC-20代币
                    tokens.addAll(ethTokens);
                    for (UserWalletDTO.TokenInfo token : ethTokens) {
                        totalUsdValue = totalUsdValue.add(token.getUsdValue());
                    }
                    
                    builder.balance(ethBalance);
                    break;

                case "BTC":
                    BigDecimal btcBalance = getBtcBalance(address);
                    BigDecimal btcPrice = marketDataService.getTokenPrice("BTC");
                    BigDecimal btcUsdValue = btcBalance.multiply(btcPrice);
                    BigDecimal btcChange24h = marketDataService.getToken24hChange("BTC");
                    
                    tokens.add(UserWalletDTO.TokenInfo.builder()
                            .symbol("BTC")
                            .name("Bitcoin")
                            .balance(btcBalance)
                            .usdValue(btcUsdValue)
                            .price(btcPrice)
                            .change24h(btcChange24h)
                            .build());
                    
                    builder.balance(btcBalance);
                    totalUsdValue = btcUsdValue;
                    break;

                case "SOLANA":
                    BigDecimal solBalance = getSolBalance(address);
                    List<UserWalletDTO.TokenInfo> solTokens = getSolTokens(address);
                    
                    // 添加SOL主币
                    if (solBalance.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal solPrice = marketDataService.getTokenPrice("SOL");
                        BigDecimal solUsdValue = solBalance.multiply(solPrice);
                        BigDecimal solChange24h = marketDataService.getToken24hChange("SOL");
                        
                        tokens.add(UserWalletDTO.TokenInfo.builder()
                                .symbol("SOL")
                                .name("Solana")
                                .balance(solBalance)
                                .usdValue(solUsdValue)
                                .price(solPrice)
                                .change24h(solChange24h)
                                .build());
                        
                        totalUsdValue = totalUsdValue.add(solUsdValue);
                    }
                    
                    // 添加SPL代币
                    tokens.addAll(solTokens);
                    for (UserWalletDTO.TokenInfo token : solTokens) {
                        totalUsdValue = totalUsdValue.add(token.getUsdValue());
                    }
                    
                    builder.balance(solBalance);
                    break;

                default:
                    throw new IllegalArgumentException("不支持的区块链类型: " + chain);
            }
        } catch (Exception e) {
            log.error("获取钱包余额失败: chain={}, address={}, error={}", chain, address, e.getMessage());
            // 返回空余额而不是抛出异常
            builder.balance(BigDecimal.ZERO);
        }

        return builder
                .tokens(tokens)
                .usdValue(totalUsdValue)
                .build();
    }

    @Override
    public boolean isValidAddress(String chain, String address) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }

        switch (chain.toUpperCase()) {
            case "EVM":
                return ETH_ADDRESS_PATTERN.matcher(address).matches();
            case "BTC":
                return BTC_ADDRESS_PATTERN.matcher(address).matches();
            case "SOLANA":
                return SOL_ADDRESS_PATTERN.matcher(address).matches();
            default:
                return false;
        }
    }

    @Override
    public BigDecimal getEthBalance(String address) {
        try {
            Web3j web3j = Web3j.build(new HttpService(ethereumRpcUrl));
            BigInteger balanceWei = web3j.ethGetBalance(address, DefaultBlockParameterName.LATEST)
                    .send().getBalance();
            
            // 将Wei转换为ETH (1 ETH = 10^18 Wei)
            return new BigDecimal(balanceWei).divide(new BigDecimal("1000000000000000000"));
        } catch (Exception e) {
            log.error("获取ETH余额失败: address={}, error={}", address, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    @Override
    public List<UserWalletDTO.TokenInfo> getEthTokens(String address) {
        // 获取ERC-20代币需要调用专门的API如Moralis、Alchemy等
        // 这里实现一个基础版本，获取常见代币余额
        log.info("获取ETH代币列表: address={}", address);

        List<UserWalletDTO.TokenInfo> tokens = new ArrayList<>();

        // 常见ERC-20代币合约地址
        Map<String, String> tokenContracts = new HashMap<>();
        tokenContracts.put("USDT", "******************************************");
        tokenContracts.put("USDC", "******************************************");

        for (Map.Entry<String, String> entry : tokenContracts.entrySet()) {
            try {
                BigDecimal balance = getERC20Balance(address, entry.getValue());
                if (balance.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal price = marketDataService.getTokenPrice(entry.getKey());
                    BigDecimal usdValue = balance.multiply(price);
                    BigDecimal change24h = marketDataService.getToken24hChange(entry.getKey());

                    tokens.add(UserWalletDTO.TokenInfo.builder()
                            .symbol(entry.getKey())
                            .name(getTokenName(entry.getKey()))
                            .balance(balance)
                            .usdValue(usdValue)
                            .price(price)
                            .change24h(change24h)
                            .build());
                }
            } catch (Exception e) {
                log.error("获取{}代币余额失败: {}", entry.getKey(), e.getMessage());
            }
        }

        return tokens;
    }

    /**
     * 获取ERC-20代币余额
     */
    private BigDecimal getERC20Balance(String address, String contractAddress) {
        try {
            Web3j web3j = Web3j.build(new HttpService(ethereumRpcUrl));

            // 构建balanceOf方法调用数据
            String methodSignature = "0x70a08231"; // balanceOf(address)
            String paddedAddress = "000000000000000000000000" + address.substring(2);
            String data = methodSignature + paddedAddress;

            // 这里需要实现具体的合约调用逻辑
            // 由于复杂性，暂时返回0
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取ERC-20代币余额失败: contract={}, error={}", contractAddress, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取代币名称
     */
    private String getTokenName(String symbol) {
        switch (symbol) {
            case "USDT": return "Tether USD";
            case "USDC": return "USD Coin";
            default: return symbol;
        }
    }

    @Override
    public BigDecimal getBtcBalance(String address) {
        try {
            String url = bitcoinApiUrl + "/address/" + address;
            log.info("获取BTC余额: address={}", address);

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JsonNode rootNode = objectMapper.readTree(response.getBody());

                // Blockstream API返回的余额单位是satoshi，需要转换为BTC
                long balanceSatoshi = rootNode.path("chain_stats").path("funded_txo_sum").asLong() -
                                     rootNode.path("chain_stats").path("spent_txo_sum").asLong();

                // 1 BTC = 100,000,000 satoshi
                return new BigDecimal(balanceSatoshi).divide(new BigDecimal("100000000"));
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取BTC余额失败: address={}, error={}", address, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getSolBalance(String address) {
        try {
            log.info("获取SOL余额: address={}", address);

            // 构建Solana RPC请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("id", 1);
            requestBody.put("method", "getBalance");
            requestBody.put("params", Arrays.asList(address));

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(solanaRpcUrl, HttpMethod.POST, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JsonNode rootNode = objectMapper.readTree(response.getBody());
                JsonNode resultNode = rootNode.path("result");

                if (resultNode.has("value")) {
                    long balanceLamports = resultNode.path("value").asLong();
                    // 1 SOL = 1,000,000,000 lamports
                    return new BigDecimal(balanceLamports).divide(new BigDecimal("1000000000"));
                }
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取SOL余额失败: address={}, error={}", address, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    @Override
    public List<UserWalletDTO.TokenInfo> getSolTokens(String address) {
        try {
            log.info("获取SOL代币列表: address={}", address);

            // 构建获取代币账户的RPC请求
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("jsonrpc", "2.0");
            requestBody.put("id", 1);
            requestBody.put("method", "getTokenAccountsByOwner");

            Map<String, Object> params = new HashMap<>();
            params.put("programId", "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA"); // SPL Token Program ID

            Map<String, Object> config = new HashMap<>();
            config.put("encoding", "jsonParsed");

            requestBody.put("params", Arrays.asList(address, params, config));

            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(solanaRpcUrl, HttpMethod.POST, entity, String.class);

            List<UserWalletDTO.TokenInfo> tokens = new ArrayList<>();

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                JsonNode rootNode = objectMapper.readTree(response.getBody());
                JsonNode resultNode = rootNode.path("result").path("value");

                if (resultNode.isArray()) {
                    for (JsonNode tokenAccount : resultNode) {
                        try {
                            JsonNode accountData = tokenAccount.path("account").path("data").path("parsed").path("info");
                            String mint = accountData.path("mint").asText();
                            String balanceStr = accountData.path("tokenAmount").path("uiAmountString").asText();

                            if (!balanceStr.isEmpty() && !balanceStr.equals("0")) {
                                BigDecimal balance = new BigDecimal(balanceStr);

                                // 这里可以根据mint地址获取代币信息
                                // 暂时使用简化处理
                                tokens.add(UserWalletDTO.TokenInfo.builder()
                                        .symbol("SPL-" + mint.substring(0, 8))
                                        .name("SPL Token")
                                        .balance(balance)
                                        .usdValue(BigDecimal.ZERO)
                                        .price(BigDecimal.ZERO)
                                        .change24h(BigDecimal.ZERO)
                                        .build());
                            }
                        } catch (Exception e) {
                            log.error("解析SPL代币失败: {}", e.getMessage());
                        }
                    }
                }
            }

            return tokens;
        } catch (Exception e) {
            log.error("获取SOL代币列表失败: address={}, error={}", address, e.getMessage());
            return new ArrayList<>();
        }
    }
}
