package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.dto.AuthDTO;
import com.dddd.web3tools.dto.CategoryDTO;
import com.dddd.web3tools.dto.PostDTO;
import com.dddd.web3tools.entity.Category;
import com.dddd.web3tools.entity.Post;
import com.dddd.web3tools.repository.CategoryRepository;
import com.dddd.web3tools.repository.PostRepository;
import com.dddd.web3tools.service.BlogAuthService;
import com.dddd.web3tools.service.BlogService;
import com.dddd.web3tools.vo.CategoryVO;
import com.dddd.web3tools.vo.PostVO;
import com.dddd.web3tools.vo.StatsVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 博客服务实现类
 */
@Service
public class BlogServiceImpl implements BlogService {

    private static final Logger log = LoggerFactory.getLogger(BlogServiceImpl.class);

    private final PostRepository postRepository;
    private final CategoryRepository categoryRepository;
    private final BlogAuthService authService;

    // 构造函数注入
    public BlogServiceImpl(PostRepository postRepository,
                          CategoryRepository categoryRepository,
                          BlogAuthService authService) {
        this.postRepository = postRepository;
        this.categoryRepository = categoryRepository;
        this.authService = authService;
    }
    
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    private static final String[] ALLOWED_EXTENSIONS = {".md", ".markdown", ".txt"};
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    // ========== 文章管理 ==========
    
    @Override
    public Page<PostVO> getPosts(int page, int limit, String category, String search) {
        Pageable pageable = PageRequest.of(page - 1, limit);
        Page<Post> posts;
        
        if (category != null && !category.trim().isEmpty() && search != null && !search.trim().isEmpty()) {
            // 按分类和关键词搜索
            posts = postRepository.findByCategoryAndTitleContainingOrContentContaining(
                    category.trim(), search.trim(), pageable);
        } else if (category != null && !category.trim().isEmpty()) {
            // 按分类筛选
            posts = postRepository.findByCategoryOrderByCreatedAtDesc(category.trim(), pageable);
        } else if (search != null && !search.trim().isEmpty()) {
            // 按关键词搜索
            posts = postRepository.findByTitleContainingOrContentContaining(search.trim(), pageable);
        } else {
            // 获取所有文章
            posts = postRepository.findAllByOrderByCreatedAtDesc(pageable);
        }
        
        return posts.map(PostVO::fromEntity);
    }
    
    @Override
    public Optional<PostVO> getPostById(Long id) {
        return postRepository.findById(id).map(PostVO::fromEntity);
    }
    
    @Override
    @Transactional
    public PostVO createPost(PostDTO postDTO) {
        Post post = new Post();
        post.setTitle(postDTO.getTitle().trim());
        post.setContent(postDTO.getContent().trim());
        post.setCategory(postDTO.getCategory().trim());

        Post savedPost = postRepository.save(post);
        log.info("创建文章成功，ID: {}, 标题: {}", savedPost.getId(), savedPost.getTitle());

        return PostVO.fromEntity(savedPost);
    }
    
    @Override
    @Transactional
    public PostVO updatePost(Long id, PostDTO postDTO) {
        Post post = postRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("文章不存在"));

        post.setTitle(postDTO.getTitle().trim());
        post.setContent(postDTO.getContent().trim());
        post.setCategory(postDTO.getCategory().trim());

        Post savedPost = postRepository.save(post);
        log.info("更新文章成功，ID: {}, 标题: {}", savedPost.getId(), savedPost.getTitle());

        return PostVO.fromEntity(savedPost);
    }
    
    @Override
    @Transactional
    public void deletePost(Long id) {
        if (!postRepository.existsById(id)) {
            throw new RuntimeException("文章不存在");
        }

        postRepository.deleteById(id);
        log.info("删除文章成功，ID: {}", id);
    }
    
    // ========== 分类管理 ==========
    
    @Override
    public List<CategoryVO> getAllCategories() {
        List<Category> categories = categoryRepository.findAllByOrderByCreatedAtAsc();
        return categories.stream()
                .map(category -> {
                    long postCount = postRepository.countByCategory(category.getId());
                    return CategoryVO.fromEntity(category, postCount);
                })
                .collect(Collectors.toList());
    }
    
    @Override
    public Optional<CategoryVO> getCategoryById(String id) {
        return categoryRepository.findById(id)
                .map(category -> {
                    long postCount = postRepository.countByCategory(category.getId());
                    return CategoryVO.fromEntity(category, postCount);
                });
    }
    
    @Override
    @Transactional
    public CategoryVO createCategory(CategoryDTO categoryDTO) {
        // 检查分类名称是否已存在
        if (categoryRepository.existsByName(categoryDTO.getName().trim())) {
            throw new RuntimeException("分类名称已存在");
        }

        // 生成分类ID（基于名称的简化版本）
        String categoryId = generateCategoryId(categoryDTO.getName().trim());

        // 检查ID是否已存在
        if (categoryRepository.existsById(categoryId)) {
            throw new RuntimeException("分类ID已存在，请使用不同的名称");
        }

        Category category = new Category();
        category.setId(categoryId);
        category.setName(categoryDTO.getName().trim());
        category.setIcon(categoryDTO.getIcon().trim());
        category.setColor(categoryDTO.getColor().trim());

        Category savedCategory = categoryRepository.save(category);
        log.info("创建分类成功，ID: {}, 名称: {}", savedCategory.getId(), savedCategory.getName());

        return CategoryVO.fromEntity(savedCategory, 0L);
    }
    
    @Override
    @Transactional
    public CategoryVO updateCategory(String id, CategoryDTO categoryDTO) {
        Category category = categoryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("分类不存在"));

        // 检查新名称是否与其他分类冲突
        Category existingCategory = categoryRepository.findByName(categoryDTO.getName().trim());
        if (existingCategory != null && !existingCategory.getId().equals(id)) {
            throw new RuntimeException("分类名称已存在");
        }

        category.setName(categoryDTO.getName().trim());
        category.setIcon(categoryDTO.getIcon().trim());
        category.setColor(categoryDTO.getColor().trim());

        Category savedCategory = categoryRepository.save(category);
        log.info("更新分类成功，ID: {}, 名称: {}", savedCategory.getId(), savedCategory.getName());

        long postCount = postRepository.countByCategory(savedCategory.getId());
        return CategoryVO.fromEntity(savedCategory, postCount);
    }
    
    @Override
    @Transactional
    public void deleteCategory(String id) {
        if (!categoryRepository.existsById(id)) {
            throw new RuntimeException("分类不存在");
        }

        // 将该分类下的文章移动到默认分类
        List<Post> posts = postRepository.findAll().stream()
                .filter(post -> id.equals(post.getCategory()))
                .collect(Collectors.toList());

        for (Post post : posts) {
            post.setCategory("default");
            postRepository.save(post);
        }

        categoryRepository.deleteById(id);
        log.info("删除分类成功，ID: {}，已将{}篇文章移动到默认分类", id, posts.size());
    }
    
    // ========== 文件上传 ==========
    
    @Override
    @Transactional
    public PostVO uploadMarkdownFile(MultipartFile file, String category) {
        // 验证文件
        validateFile(file);

        try {
            // 读取文件内容
            String content = new String(file.getBytes(), StandardCharsets.UTF_8);

            // 从文件名提取标题
            String title = extractTitleFromFilename(file.getOriginalFilename());

            // 创建文章
            Post post = new Post();
            post.setTitle(title);
            post.setContent(content);
            post.setCategory(category != null ? category.trim() : "default");

            Post savedPost = postRepository.save(post);
            log.info("上传Markdown文件成功，文件名: {}, 文章ID: {}", file.getOriginalFilename(), savedPost.getId());

            return PostVO.fromEntity(savedPost);

        } catch (IOException e) {
            log.error("读取文件失败: {}", e.getMessage());
            throw new RuntimeException("文件读取失败: " + e.getMessage());
        }
    }
    
    // ========== 认证 ==========
    
    @Override
    public Map<String, Object> verifyPassword(AuthDTO authDTO) {
        boolean valid = authService.verifyPassword(authDTO.getPassword());
        
        Map<String, Object> data = new HashMap<>();
        data.put("valid", valid);
        
        if (valid) {
            // 可以在这里生成临时token，目前简化处理
            data.put("token", "temp-token-" + System.currentTimeMillis());
        }
        
        return data;
    }
    
    // ========== 统计信息 ==========
    
    @Override
    public StatsVO getBlogStats() {
        StatsVO stats = new StatsVO();
        
        // 总文章数
        stats.setTotalPosts(postRepository.getTotalPostCount());
        
        // 总分类数
        stats.setTotalCategories(categoryRepository.getTotalCategoryCount());
        
        // 最近文章
        List<Post> recentPosts = postRepository.findTop5ByOrderByCreatedAtDesc();
        List<StatsVO.RecentPostVO> recentPostVOs = recentPosts.stream()
                .map(post -> {
                    StatsVO.RecentPostVO vo = new StatsVO.RecentPostVO();
                    vo.setId(post.getId());
                    vo.setTitle(post.getTitle());
                    vo.setCreatedAt(post.getCreatedAt().format(DATE_FORMATTER));
                    return vo;
                })
                .collect(Collectors.toList());
        stats.setRecentPosts(recentPostVOs);
        
        // 分类统计
        List<Category> categories = categoryRepository.findAllByOrderByCreatedAtAsc();
        List<StatsVO.CategoryStatsVO> categoryStats = categories.stream()
                .map(category -> {
                    StatsVO.CategoryStatsVO vo = new StatsVO.CategoryStatsVO();
                    vo.setCategoryId(category.getId());
                    vo.setCategoryName(category.getName());
                    vo.setPostCount(postRepository.countByCategory(category.getId()));
                    return vo;
                })
                .collect(Collectors.toList());
        stats.setCategoryStats(categoryStats);
        
        return stats;
    }
    
    // ========== 初始化 ==========
    
    @Override
    @Transactional
    public void initDefaultCategories() {
        if (categoryRepository.count() == 0) {
            Category[] defaultCategories = {
                new Category("default", "默认分类", "📝", "#6b7280"),
                new Category("tech", "技术分享", "💻", "#3b82f6"),
                new Category("life", "生活随笔", "🌟", "#10b981"),
                new Category("thinking", "思考感悟", "💭", "#8b5cf6"),
                new Category("reading", "读书笔记", "📚", "#f59e0b")
            };
            
            for (Category category : defaultCategories) {
                categoryRepository.save(category);
            }
            
            log.info("初始化默认分类完成");
        }
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }
        
        // 检查文件大小
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new RuntimeException("文件大小不能超过5MB");
        }
        
        // 检查文件扩展名
        String filename = file.getOriginalFilename();
        if (filename == null) {
            throw new RuntimeException("文件名不能为空");
        }
        
        boolean validExtension = false;
        for (String ext : ALLOWED_EXTENSIONS) {
            if (filename.toLowerCase().endsWith(ext)) {
                validExtension = true;
                break;
            }
        }
        
        if (!validExtension) {
            throw new RuntimeException("只支持.md、.markdown、.txt格式的文件");
        }
    }
    
    /**
     * 从文件名提取标题
     */
    private String extractTitleFromFilename(String filename) {
        if (filename == null) {
            return "未命名文章";
        }
        
        // 移除扩展名
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0) {
            filename = filename.substring(0, lastDotIndex);
        }
        
        // 替换下划线和连字符为空格
        filename = filename.replace("_", " ").replace("-", " ");
        
        // 首字母大写
        if (!filename.isEmpty()) {
            filename = filename.substring(0, 1).toUpperCase() + filename.substring(1);
        }
        
        return filename.trim().isEmpty() ? "未命名文章" : filename.trim();
    }
    
    /**
     * 生成分类ID
     */
    private String generateCategoryId(String name) {
        // 简单的ID生成策略：将中文转换为拼音首字母，英文转小写，移除特殊字符
        return name.toLowerCase()
                .replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "")
                .replaceAll("\\s+", "-");
    }
}
