package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.entity.Approval;
import com.dddd.web3tools.repository.ApprovalRepository;
import com.dddd.web3tools.service.ApprovalService;
import org.springframework.stereotype.Service;

@Service
public class ApprovalServiceImpl implements ApprovalService {

    private final ApprovalRepository approvalRepository;

    public ApprovalServiceImpl(ApprovalRepository approvalRepository) {
        this.approvalRepository = approvalRepository;
    }

    @Override
    public Approval saveApproval(Approval approval) {
        return approvalRepository.save(approval);
    }
}
