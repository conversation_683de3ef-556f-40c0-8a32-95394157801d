package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.entity.BlockchainData;
import com.dddd.web3tools.repository.BlockchainDataRepository;
import com.dddd.web3tools.service.BlockchainDataService;
import com.dddd.web3tools.vo.GasDataVO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.methods.response.EthBlock;
import org.web3j.protocol.core.methods.response.EthBlockNumber;
import org.web3j.protocol.core.methods.response.EthGasPrice;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class BlockchainDataServiceImpl implements BlockchainDataService {

    @Autowired
    private final Web3j web3j;
    private final RestTemplate restTemplate;
    private final BlockchainDataRepository repository;
    private final ObjectMapper objectMapper;

    @Value( "${blockchain.bitcoin.api-url}")
    private String bitcoinApiUrl;

    public BlockchainDataServiceImpl(Web3j web3j, RestTemplate restTemplate,
                                     BlockchainDataRepository repository) {
        this.web3j = web3j;
        this.restTemplate = restTemplate;
        this.repository = repository;
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public void collectEthereumData() {
        try {
            // 获取最新区块号
            EthBlockNumber blockNumber = web3j.ethBlockNumber().send();
            BigInteger latestBlockNumber = blockNumber.getBlockNumber();

            // 获取区块详细信息
            EthBlock ethBlock = web3j.ethGetBlockByNumber(
                    DefaultBlockParameter.valueOf(latestBlockNumber), true).send();

            if (ethBlock.getBlock() != null) {
                EthBlock.Block block = ethBlock.getBlock();

                // 获取Gas价格
                EthGasPrice gasPriceResponse  = web3j.ethGasPrice().send();
                BigInteger gasPriceInWei = gasPriceResponse.getGasPrice();
                BigInteger gasPriceInGwei = gasPriceInWei.divide(BigInteger.TEN.pow(9));
                // 创建数据对象
                BlockchainData data = new BlockchainData();
                data.setChainType("ETH");
                data.setBlockNumber(block.getNumber().longValue());
                data.setBlockHash(block.getHash());
                data.setGasPrice(gasPriceInGwei.toString());
                data.setGasLimit(block.getGasLimit().longValue());
                data.setGasUsed(block.getGasUsed().longValue());
                data.setTransactionCount(block.getTransactions().size());
                data.setBlockSize(block.getSize().longValue());
                data.setDifficulty(block.getDifficulty().toString());
                data.setTimestamp(block.getTimestamp().longValue());

                // 保存到数据库
                repository.save(data);
                log.info("成功保存以太坊区块数据: 区块号 {}, Gas价格 {} Gwei",
                        block.getNumber(), gasPriceInGwei);
            }

        } catch (Exception e) {
            log.error("获取以太坊数据失败", e);
        }
    }

    @Override
    public void collectBitcoinData() {
        try {
            // 获取最新区块信息
            String latestBlockUrl = bitcoinApiUrl + "/blocks/tip/hash";
            String latestBlockHash = restTemplate.getForObject(latestBlockUrl, String.class);

            if (latestBlockHash != null) {
                // 获取区块详细信息
                String blockInfoUrl = bitcoinApiUrl + "/block/" + latestBlockHash;
                String blockInfoJson = restTemplate.getForObject(blockInfoUrl, String.class);

                if (blockInfoJson != null) {
                    JsonNode blockInfo = objectMapper.readTree(blockInfoJson);

                    // 获取推荐手续费
                    String feeEstimateUrl = bitcoinApiUrl + "/fee-estimates";
                    String feeEstimateJson = restTemplate.getForObject(feeEstimateUrl, String.class);
                    JsonNode feeEstimate = objectMapper.readTree(feeEstimateJson);

                    // 创建数据对象
                    BlockchainData data = new BlockchainData();
                    data.setChainType("BTC");
                    data.setBlockNumber((long) blockInfo.get("height").asInt());
                    data.setBlockHash(blockInfo.get("id").asText());
                    // 比特币没有gas概念，用推荐手续费代替
                    data.setGasPrice(feeEstimate.get("1").asText()); // 1块确认的推荐费率
                    data.setTransactionCount(blockInfo.get("tx_count").asInt());
                    data.setBlockSize((long) blockInfo.get("size").asInt());
                    data.setDifficulty(blockInfo.get("difficulty").asText());
                    data.setTimestamp((long) blockInfo.get("timestamp").asInt());

                    // 保存到数据库
                    repository.save(data);
                    log.info("成功保存比特币区块数据: 区块号 {}, 推荐费率 {} sat/vB",
                            blockInfo.get("height").asInt(), feeEstimate.get("1").asText());
                }
            }

        } catch (Exception e) {
            log.error("获取比特币数据失败", e);
        }
    }

    @Override
    public List<GasDataVO> getGasData() {
        LocalDateTime startTime = LocalDateTime.now().minusMinutes(30);
        List<BlockchainData> gasData = repository.findAllDataInLast30Minutes(startTime);
        List<GasDataVO> gasDataVOList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
        gasData.forEach(data -> {
            gasDataVOList.add(new GasDataVO(data.getCreatedAt().format(formatter),data.getGasPrice(),data.getChainType()));
        });
        return gasDataVOList;
    }
}