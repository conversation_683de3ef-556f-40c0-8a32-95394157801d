package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.entity.ContractInteraction;
import com.dddd.web3tools.entity.SmartContract;
import com.dddd.web3tools.repository.ContractInteractionRepository;
import com.dddd.web3tools.repository.SmartContractRepository;
import com.dddd.web3tools.service.SmartContractDiscoveryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.methods.response.*;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 智能合约发现服务实现
 */
@Service
@Slf4j
public class SmartContractDiscoveryServiceImpl implements SmartContractDiscoveryService {

    @Autowired
    private Web3j web3j;
    
    @Autowired
    private SmartContractRepository contractRepository;
    
    @Autowired
    private ContractInteractionRepository interactionRepository;

    @Value("${ethereum.contract.discovery.enabled:true}")
    private boolean discoveryEnabled;

    @Value("${ethereum.contract.discovery.poll-interval:15000}")
    private long pollInterval;

    @Value("${ethereum.contract.discovery.max-retries:3}")
    private int maxRetries;

    @Value("${ethereum.contract.discovery.stats-update-interval:300000}")
    private long statsUpdateInterval;

    @Value("${ethereum.contract.discovery.cleanup-interval:3600000}")
    private long cleanupInterval;

    @Value("${ethereum.contract.discovery.batch-size:50}")
    private int batchSize;

    private final AtomicBoolean isMonitoring = new AtomicBoolean(false);
    private final AtomicReference<BigInteger> lastProcessedBlock = new AtomicReference<>();
    private final AtomicLong totalContractsDiscovered = new AtomicLong(0);
    private final AtomicLong processingErrors = new AtomicLong(0);
    private ScheduledExecutorService scheduler;
    private ScheduledExecutorService statsScheduler;
    private ScheduledExecutorService cleanupScheduler;
    private ExecutorService blockProcessorPool;
    private ExecutorService transactionProcessorPool;
    private String startTime;

    // 缓存已知合约地址，避免重复数据库查询
    // 使用LRU缓存限制内存使用
    private final Map<String, Boolean> knownContractAddresses = new ConcurrentHashMap<>();
    private volatile long lastCacheUpdate = 0;
    private static final long CACHE_REFRESH_INTERVAL = 300000; // 5分钟刷新缓存
    private static final int MAX_CACHE_SIZE = 10000; // 最大缓存10000个合约地址

    private static final BigInteger WEI_TO_ETH = new BigInteger("1000000000000000000");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // 已知的工厂合约和需要过滤的地址
    private static final Set<String> FACTORY_CONTRACTS = Set.of(
        "******************************************", // Uniswap V2 Factory
        "******************************************", // Uniswap V3 Factory
        "******************************************", // Sushiswap Factory
        "******************************************", // Balancer V2 Vault
        "******************************************"  // Balancer V2 Vault
    );

    @Override
    public void startMonitoring() {
        if (!discoveryEnabled) {
            log.warn("智能合约发现已被禁用");
            return;
        }

        if (isMonitoring.get()) {
            log.warn("合约发现已经在运行中");
            return;
        }

        log.info("🚀 开始启动智能合约发现服务...");
        
        // 测试Web3j连接
        if (!testConnection()) {
            log.error("Web3j连接测试失败，无法启动合约发现");
            return;
        }
        
        isMonitoring.set(true);
        startTime = DATE_FORMAT.format(new Date());
        
        // 初始化主调度器
        scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "contract-discovery-monitor");
            t.setDaemon(true);
            return t;
        });

        // 初始化统计更新调度器
        statsScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "contract-stats-updater");
            t.setDaemon(true);
            return t;
        });

        // 初始化清理调度器
        cleanupScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "contract-cleanup");
            t.setDaemon(true);
            return t;
        });

        // 初始化区块处理线程池
        blockProcessorPool = Executors.newFixedThreadPool(3, r -> {
            Thread t = new Thread(r, "block-processor");
            t.setDaemon(true);
            return t;
        });

        // 初始化交易处理线程池
        transactionProcessorPool = Executors.newFixedThreadPool(8, r -> {
            Thread t = new Thread(r, "tx-processor");
            t.setDaemon(true);
            return t;
        });

        // 初始化合约地址缓存
        refreshContractAddressCache();

        // 获取当前最新区块号作为起始点
        try {
            BigInteger latestBlockNumber = getCurrentBlockNumber();
            lastProcessedBlock.set(latestBlockNumber);
            log.info("✅ 合约发现起始区块号: {}", latestBlockNumber);
        } catch (Exception e) {
            log.error("获取起始区块号失败", e);
            lastProcessedBlock.set(BigInteger.ZERO);
        }

        // 启动主监控任务
        scheduler.scheduleWithFixedDelay(this::monitorNewBlocks, 0, pollInterval, TimeUnit.MILLISECONDS);
        
        // 启动统计更新任务
        statsScheduler.scheduleWithFixedDelay(this::updateAllContractStats, 
            statsUpdateInterval, statsUpdateInterval, TimeUnit.MILLISECONDS);
        
        // 启动清理任务
        cleanupScheduler.scheduleWithFixedDelay(this::cleanupOldInteractions, 
            cleanupInterval, cleanupInterval, TimeUnit.MILLISECONDS);
        
        log.info("✅ 智能合约发现服务已启动，轮询间隔: {}ms", pollInterval);
    }

    @Override
    public void stopMonitoring() {
        if (!isMonitoring.get()) {
            log.warn("合约发现未在运行");
            return;
        }

        log.info("正在停止智能合约发现服务...");
        isMonitoring.set(false);
        
        shutdownScheduler(scheduler, "主监控");
        shutdownScheduler(statsScheduler, "统计更新");
        shutdownScheduler(cleanupScheduler, "清理");

        // 关闭区块处理线程池
        if (blockProcessorPool != null && !blockProcessorPool.isShutdown()) {
            blockProcessorPool.shutdown();
            try {
                if (!blockProcessorPool.awaitTermination(10, TimeUnit.SECONDS)) {
                    blockProcessorPool.shutdownNow();
                }
                log.info("区块处理线程池已停止");
            } catch (InterruptedException e) {
                blockProcessorPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 关闭交易处理线程池
        if (transactionProcessorPool != null && !transactionProcessorPool.isShutdown()) {
            transactionProcessorPool.shutdown();
            try {
                if (!transactionProcessorPool.awaitTermination(10, TimeUnit.SECONDS)) {
                    transactionProcessorPool.shutdownNow();
                }
                log.info("交易处理线程池已停止");
            } catch (InterruptedException e) {
                transactionProcessorPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        log.info("智能合约发现服务已停止");
    }

    private void shutdownScheduler(ScheduledExecutorService scheduler, String name) {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
                log.info("{}调度器已停止", name);
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Override
    public boolean isMonitoring() {
        return isMonitoring.get();
    }

    /**
     * 监控新区块的核心方法 - 优化为异步处理
     */
    private void monitorNewBlocks() {
        if (!isMonitoring.get()) {
            return;
        }

        try {
            BigInteger currentBlockNumber = getCurrentBlockNumber();
            BigInteger lastProcessed = lastProcessedBlock.get();

            if (currentBlockNumber.compareTo(lastProcessed) > 0) {
                // 有新区块产生，计算需要处理的区块数量
                long blocksToProcess = currentBlockNumber.subtract(lastProcessed).longValue();

                log.info("🔄 发现 {} 个新区块，当前区块: {}, 上次处理: {}",
                    blocksToProcess, currentBlockNumber, lastProcessed);

                // 异步处理新区块，避免阻塞主监控线程
                BigInteger nextBlock = lastProcessed.add(BigInteger.ONE);

                while (nextBlock.compareTo(currentBlockNumber) <= 0 && isMonitoring.get()) {
                    final BigInteger blockToProcess = nextBlock;

                    // 异步提交区块处理任务
                    blockProcessorPool.submit(() -> {
                        try {
                            processBlockForContracts(blockToProcess);
                        } catch (Exception e) {
                            log.error("异步处理区块 {} 失败", blockToProcess, e);
                            processingErrors.incrementAndGet();
                        }
                    });

                    // 立即更新已处理的区块号，不等待处理完成
                    lastProcessedBlock.set(nextBlock);
                    nextBlock = nextBlock.add(BigInteger.ONE);
                }

                if (blocksToProcess > 3) {
                    log.warn("⚠️ 处理延迟较大，落后 {} 个区块，考虑优化处理速度", blocksToProcess);
                }
            }
        } catch (Exception e) {
            log.error("监控新区块时发生错误", e);
            processingErrors.incrementAndGet();
        }
    }

    @Override
    public void processBlockForContracts(BigInteger blockNumber) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("🔍 分析区块 {} 中的合约...", blockNumber);

            // 刷新缓存（如果需要）
            refreshContractAddressCacheIfNeeded();

            EthBlock ethBlock = web3j.ethGetBlockByNumber(
                DefaultBlockParameter.valueOf(blockNumber), true).send();

            if (ethBlock.getBlock() != null && ethBlock.getBlock().getTransactions() != null) {
                EthBlock.Block block = ethBlock.getBlock();
                Long blockTimestamp = block.getTimestamp().longValue();
                int transactionCount = block.getTransactions().size();

                log.info("📦 区块 {} 包含 {} 笔交易", blockNumber, transactionCount);

                // 快速预筛选交易
                List<Transaction> contractDeployments = new ArrayList<>();
                List<Transaction> contractInteractions = new ArrayList<>();

                for (EthBlock.TransactionResult txResult : block.getTransactions()) {
                    if (txResult instanceof EthBlock.TransactionObject) {
                        EthBlock.TransactionObject txObj = (EthBlock.TransactionObject) txResult;
                        Transaction tx = txObj.get();

                        if (tx.getTo() == null || tx.getTo().isEmpty()) {
                            // 合约部署
                            contractDeployments.add(tx);
                        } else if (knownContractAddresses.containsKey(tx.getTo().toLowerCase())) {
                            // 已知合约交互
                            contractInteractions.add(tx);
                        }
                    }
                }

                // 并行处理合约部署和交互
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                // 处理合约部署
                for (Transaction tx : contractDeployments) {
                    futures.add(CompletableFuture.runAsync(() -> {
                        try {
                            analyzeTransaction(tx, blockNumber, blockTimestamp);
                        } catch (Exception e) {
                            log.error("处理合约部署交易失败: {}", tx.getHash(), e);
                        }
                    }, transactionProcessorPool));
                }

                // 处理合约交互
                for (Transaction tx : contractInteractions) {
                    futures.add(CompletableFuture.runAsync(() -> {
                        try {
                            analyzeTransaction(tx, blockNumber, blockTimestamp);
                        } catch (Exception e) {
                            log.error("处理合约交互交易失败: {}", tx.getHash(), e);
                        }
                    }, transactionProcessorPool));
                }

                // 等待所有任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                long processingTime = System.currentTimeMillis() - startTime;
                log.info("📊 区块 {} 处理完成: 合约部署 {} 个, 合约交互 {} 笔, 耗时 {}ms",
                    blockNumber, contractDeployments.size(), contractInteractions.size(), processingTime);
            }
        } catch (Exception e) {
            log.error("处理区块 {} 时发生错误", blockNumber, e);
            processingErrors.incrementAndGet();
        }
    }

    @Override
    @Transactional
    public void analyzeTransaction(Transaction tx, BigInteger blockNumber, Long blockTimestamp) {
        try {
            // 检查是否为合约部署交易
            if (tx.getTo() == null || tx.getTo().isEmpty()) {
                handleContractDeployment(tx, blockNumber, blockTimestamp);
            } else {
                // 检查是否为合约交互
                handleContractInteraction(tx, blockNumber, blockTimestamp);
            }
        } catch (Exception e) {
            log.error("分析交易 {} 时发生错误", tx.getHash(), e);
        }
    }

    /**
     * 处理合约部署
     */
    private void handleContractDeployment(Transaction tx, BigInteger blockNumber, Long blockTimestamp) {
        try {
            // 获取交易收据以获取合约地址
            TransactionReceipt receipt = getTransactionReceipt(tx.getHash());
            if (receipt != null && receipt.getContractAddress() != null) {
                String contractAddress = receipt.getContractAddress();
                
                // 检查合约是否已存在（使用缓存）
                if (!knownContractAddresses.containsKey(contractAddress.toLowerCase())) {
                    // 检查是否应该过滤
                    if (!shouldFilterContract(contractAddress, tx.getFrom())) {
                        SmartContract contract = createSmartContract(tx, contractAddress, blockNumber, blockTimestamp);

                        // 初始化统计数据
                        initializeContractStats(contract);

                        contractRepository.save(contract);
                        totalContractsDiscovered.incrementAndGet();

                        // 立即更新缓存，检查大小限制
                        addToCache(contractAddress.toLowerCase());

                        log.info("🎯 发现新合约: {} 部署者: {} 区块: {}", contractAddress, tx.getFrom(), blockNumber);
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理合约部署失败: {}", tx.getHash(), e);
        }
    }

    /**
     * 处理合约交互
     */
    private void handleContractInteraction(Transaction tx, BigInteger blockNumber, Long blockTimestamp) {
        try {
            String contractAddress = tx.getTo();

            // 直接检查合约是否在我们的数据库中（已经通过缓存预筛选）
            Optional<SmartContract> contractOpt = contractRepository.findByContractAddress(contractAddress);
            if (contractOpt.isPresent() && !contractOpt.get().getIsFiltered()) {
                // 记录交互
                recordContractInteraction(tx, contractAddress, blockNumber, blockTimestamp);

                // 异步更新合约统计（提高响应速度）
                transactionProcessorPool.submit(() -> {
                    try {
                        updateContractStats(contractAddress);
                    } catch (Exception e) {
                        log.error("异步更新合约统计失败: {}", contractAddress, e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("处理合约交互失败: {}", tx.getHash(), e);
        }
    }

    /**
     * 测试Web3j连接
     */
    private boolean testConnection() {
        try {
            log.info("🔗 测试以太坊节点连接...");
            BigInteger blockNumber = web3j.ethBlockNumber().send().getBlockNumber();
            log.info("✅ 连接成功，当前区块号: {}", blockNumber);
            return true;
        } catch (Exception e) {
            log.error("❌ Web3j连接测试失败", e);
            return false;
        }
    }

    /**
     * 获取当前区块号
     */
    private BigInteger getCurrentBlockNumber() throws Exception {
        EthBlockNumber blockNumber = web3j.ethBlockNumber().send();
        return blockNumber.getBlockNumber();
    }

    /**
     * 刷新合约地址缓存 - 只缓存最近活跃的合约
     */
    private void refreshContractAddressCache() {
        try {
            log.info("🔄 刷新合约地址缓存...");

            // 只缓存最近30天有交互的合约，避免内存无限增长
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
            List<String> activeAddresses = contractRepository.findActiveContractAddresses(cutoffTime);

            knownContractAddresses.clear();
            for (String address : activeAddresses) {
                knownContractAddresses.put(address.toLowerCase(), true);
            }

            // 如果缓存超过限制，只保留最新的
            if (knownContractAddresses.size() > MAX_CACHE_SIZE) {
                log.warn("⚠️ 缓存大小超限 ({} > {})，清理旧数据", knownContractAddresses.size(), MAX_CACHE_SIZE);
                // 保留最新的MAX_CACHE_SIZE个
                List<String> sortedAddresses = activeAddresses.stream()
                    .limit(MAX_CACHE_SIZE)
                    .collect(Collectors.toList());
                knownContractAddresses.clear();
                for (String address : sortedAddresses) {
                    knownContractAddresses.put(address.toLowerCase(), true);
                }
            }

            lastCacheUpdate = System.currentTimeMillis();
            log.info("✅ 合约地址缓存已刷新，包含 {} 个活跃地址", knownContractAddresses.size());
        } catch (Exception e) {
            log.error("刷新合约地址缓存失败", e);
        }
    }

    /**
     * 如果需要则刷新合约地址缓存
     */
    private void refreshContractAddressCacheIfNeeded() {
        long now = System.currentTimeMillis();
        if (now - lastCacheUpdate > CACHE_REFRESH_INTERVAL) {
            refreshContractAddressCache();
        }
    }

    /**
     * 安全地添加地址到缓存，防止内存溢出
     */
    private void addToCache(String contractAddress) {
        // 如果缓存已满，移除一个旧的地址
        if (knownContractAddresses.size() >= MAX_CACHE_SIZE) {
            // 移除第一个元素（最旧的）
            String firstKey = knownContractAddresses.keySet().iterator().next();
            knownContractAddresses.remove(firstKey);
            log.debug("缓存已满，移除旧地址: {}", firstKey);
        }
        knownContractAddresses.put(contractAddress, true);
    }

    /**
     * 初始化新合约的统计数据
     */
    private void initializeContractStats(SmartContract contract) {
        try {
            LocalDateTime now = LocalDateTime.now();

            // 初始化所有统计字段为合理的默认值
            contract.setInteractionCount24h(0);
            contract.setUniqueUsers24h(0);
            contract.setTotalValue24h(BigDecimal.ZERO);
            contract.setAvgGasUsed24h(BigDecimal.ZERO);
            contract.setTotalGasUsed24h(0L);
            contract.setAvgTransactionValue24h(BigDecimal.ZERO);
            contract.setMaxTransactionValue24h(BigDecimal.ZERO);
            contract.setLastInteractionTime(null);
            contract.setStatsUpdatedAt(now);

            // 初始化评分字段
            contract.setActivityScore(BigDecimal.ZERO);
            contract.setValueScore(BigDecimal.ZERO);
            contract.setUserDiversityScore(BigDecimal.ZERO);
            contract.setGasEfficiencyScore(BigDecimal.ZERO);
            contract.setScore(BigDecimal.ZERO);

            log.debug("初始化合约统计数据: {}", contract.getContractAddress());
        } catch (Exception e) {
            log.error("初始化合约统计数据失败: {}", contract.getContractAddress(), e);
        }
    }

    /**
     * 获取交易收据
     */
    private TransactionReceipt getTransactionReceipt(String txHash) {
        for (int retry = 0; retry < maxRetries; retry++) {
            try {
                EthGetTransactionReceipt receiptResponse = web3j.ethGetTransactionReceipt(txHash).send();
                if (receiptResponse.getTransactionReceipt().isPresent()) {
                    return receiptResponse.getTransactionReceipt().get();
                }
            } catch (Exception e) {
                log.warn("获取交易收据失败，重试 {}/{}: {}", retry + 1, maxRetries, txHash);
                if (retry < maxRetries - 1) {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public boolean isContract(String address) {
        try {
            EthGetCode codeResponse = web3j.ethGetCode(address, DefaultBlockParameter.valueOf("latest")).send();
            String code = codeResponse.getCode();
            return code != null && !code.equals("0x") && code.length() > 2;
        } catch (Exception e) {
            log.debug("检查合约地址失败: {}", address);
            return false;
        }
    }

    @Override
    public boolean shouldFilterContract(String contractAddress, String deployerAddress) {
        // 检查是否为已知的工厂合约
        if (FACTORY_CONTRACTS.contains(deployerAddress.toLowerCase())) {
            log.debug("过滤工厂合约部署: {} 部署者: {}", contractAddress, deployerAddress);
            return true;
        }
        
        // 可以添加更多过滤规则
        // 例如：检查合约代码大小、特定的字节码模式等
        
        return false;
    }

    /**
     * 创建智能合约对象
     */
    private SmartContract createSmartContract(Transaction tx, String contractAddress, 
                                            BigInteger blockNumber, Long blockTimestamp) {
        SmartContract contract = new SmartContract();
        contract.setContractAddress(contractAddress);
        contract.setDeployTransactionHash(tx.getHash());
        contract.setDeployBlockNumber(blockNumber.longValue());
        contract.setDeployTimestamp(blockTimestamp);
        contract.setDeployerAddress(tx.getFrom());
        
        // 尝试检测是否为代币合约
        contract.setIsToken(detectIfToken(contractAddress));
        
        return contract;
    }

    /**
     * 检测是否为代币合约
     */
    private boolean detectIfToken(String contractAddress) {
        try {
            // 尝试调用标准ERC20方法
            // 这里简化处理，实际可以调用name()、symbol()、totalSupply()等方法
            return false; // 暂时返回false，后续可以完善
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 记录合约交互
     */
    private void recordContractInteraction(Transaction tx, String contractAddress, 
                                         BigInteger blockNumber, Long blockTimestamp) {
        try {
            // 检查交互是否已存在
            if (!interactionRepository.existsByTransactionHash(tx.getHash())) {
                ContractInteraction interaction = new ContractInteraction();
                interaction.setContractAddress(contractAddress);
                interaction.setTransactionHash(tx.getHash());
                interaction.setUserAddress(tx.getFrom());
                interaction.setBlockNumber(blockNumber.longValue());
                
                // 转换Wei到ETH
                BigDecimal valueEth = new BigDecimal(tx.getValue())
                    .divide(new BigDecimal(WEI_TO_ETH), 18, RoundingMode.HALF_UP);
                interaction.setTransactionValue(valueEth);
                
                interaction.setGasPrice(tx.getGasPrice().toString());
                
                // 提取方法签名（input的前4字节）
                if (tx.getInput() != null && tx.getInput().length() >= 10) {
                    interaction.setMethodSignature(tx.getInput().substring(0, 10));
                }
                
                interaction.setInteractionTime(LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochSecond(blockTimestamp), ZoneId.systemDefault()));
                
                interactionRepository.save(interaction);
            }
        } catch (Exception e) {
            log.error("记录合约交互失败: {}", tx.getHash(), e);
        }
    }

    @Override
    public MonitorStats getMonitorStats() {
        MonitorStats stats = new MonitorStats();
        stats.setTotalContractsDiscovered(totalContractsDiscovered.get());
        stats.setActiveContracts24h(contractRepository.countByIsFilteredFalse());
        stats.setFilteredContracts(contractRepository.count() - contractRepository.countByIsFilteredFalse());
        stats.setLastProcessedBlock(lastProcessedBlock.get());
        stats.setRunning(isMonitoring.get());
        stats.setStartTime(startTime);
        stats.setProcessingErrors(processingErrors.get());
        return stats;
    }

    @Override
    @Transactional
    public void updateContractStats(String contractAddress) {
        try {
            Optional<SmartContract> contractOpt = contractRepository.findByContractAddress(contractAddress);
            if (contractOpt.isPresent()) {
                SmartContract contract = contractOpt.get();

                LocalDateTime now = LocalDateTime.now();
                LocalDateTime yesterday = now.minusHours(24);

                // 统计24小时内的数据
                Long interactionCount = interactionRepository.countInteractionsByContractAndTimeRange(
                    contractAddress, yesterday, now);
                Long uniqueUsers = interactionRepository.countUniqueUsersByContractAndTimeRange(
                    contractAddress, yesterday, now);
                BigDecimal totalValue = interactionRepository.sumTransactionValueByContractAndTimeRange(
                    contractAddress, yesterday, now);
                Double avgGasUsed = interactionRepository.avgGasUsedByContractAndTimeRange(
                    contractAddress, yesterday, now);
                Long totalGasUsed = interactionRepository.sumGasUsedByContractAndTimeRange(
                    contractAddress, yesterday, now);
                BigDecimal avgTransactionValue = interactionRepository.avgTransactionValueByContractAndTimeRange(
                    contractAddress, yesterday, now);
                BigDecimal maxTransactionValue = interactionRepository.maxTransactionValueByContractAndTimeRange(
                    contractAddress, yesterday, now);
                LocalDateTime lastInteraction = interactionRepository.findLatestInteractionTime(contractAddress);

                // 更新统计数据
                contract.setInteractionCount24h(interactionCount.intValue());
                contract.setUniqueUsers24h(uniqueUsers.intValue());
                contract.setTotalValue24h(totalValue != null ? totalValue : BigDecimal.ZERO);
                contract.setAvgGasUsed24h(avgGasUsed != null ? BigDecimal.valueOf(avgGasUsed) : BigDecimal.ZERO);
                contract.setTotalGasUsed24h(totalGasUsed != null ? totalGasUsed : 0L);
                contract.setAvgTransactionValue24h(avgTransactionValue != null ? avgTransactionValue : BigDecimal.ZERO);
                contract.setMaxTransactionValue24h(maxTransactionValue != null ? maxTransactionValue : BigDecimal.ZERO);
                contract.setLastInteractionTime(lastInteraction);
                contract.setStatsUpdatedAt(now);

                // 计算评分
                calculateContractScore(contract);

                contractRepository.save(contract);

                log.debug("更新合约统计: {} 交互次数: {} 独立用户: {}",
                    contractAddress, interactionCount, uniqueUsers);
            }
        } catch (Exception e) {
            log.error("更新合约统计失败: {}", contractAddress, e);
        }
    }

    @Override
    public void updateAllContractStats() {
        try {
            log.info("🔄 开始更新所有合约统计数据...");

            LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(300); // 300分钟前
            List<SmartContract> contractsToUpdate = contractRepository.findContractsNeedingStatsUpdate(cutoffTime);

            log.info("需要更新统计的合约数量: {}", contractsToUpdate.size());

            for (SmartContract contract : contractsToUpdate) {
                updateContractStats(contract.getContractAddress());
            }

            log.info("✅ 合约统计数据更新完成");
        } catch (Exception e) {
            log.error("更新所有合约统计失败", e);
        }
    }

    @Override
    public void calculateContractScore(SmartContract contract) {
        try {
            // 活跃度评分 (0-100)
            BigDecimal activityScore = calculateActivityScore(contract);
            contract.setActivityScore(activityScore);

            // 价值评分 (0-100)
            BigDecimal valueScore = calculateValueScore(contract);
            contract.setValueScore(valueScore);

            // 用户多样性评分 (0-100)
            BigDecimal userDiversityScore = calculateUserDiversityScore(contract);
            contract.setUserDiversityScore(userDiversityScore);

            // Gas效率评分 (0-100)
            BigDecimal gasEfficiencyScore = calculateGasEfficiencyScore(contract);
            contract.setGasEfficiencyScore(gasEfficiencyScore);

            // 计算综合评分
            contract.calculateScore();

            log.debug("合约评分 {}: 总分={}, 活跃度={}, 价值={}, 用户多样性={}, Gas效率={}",
                contract.getContractAddress(), contract.getScore(),
                activityScore, valueScore, userDiversityScore, gasEfficiencyScore);
        } catch (Exception e) {
            log.error("计算合约评分失败: {}", contract.getContractAddress(), e);
        }
    }

    /**
     * 计算活跃度评分
     */
    private BigDecimal calculateActivityScore(SmartContract contract) {
        int interactions = contract.getInteractionCount24h();

        // 基于交互次数的评分
        if (interactions >= 1000) return BigDecimal.valueOf(100);
        if (interactions >= 500) return BigDecimal.valueOf(90);
        if (interactions >= 200) return BigDecimal.valueOf(80);
        if (interactions >= 100) return BigDecimal.valueOf(70);
        if (interactions >= 50) return BigDecimal.valueOf(60);
        if (interactions >= 20) return BigDecimal.valueOf(50);
        if (interactions >= 10) return BigDecimal.valueOf(40);
        if (interactions >= 5) return BigDecimal.valueOf(30);
        if (interactions >= 1) return BigDecimal.valueOf(20);

        return BigDecimal.ZERO;
    }

    /**
     * 计算价值评分
     */
    private BigDecimal calculateValueScore(SmartContract contract) {
        BigDecimal totalValue = contract.getTotalValue24h();

        // 基于24小时总交易价值的评分
        if (totalValue.compareTo(BigDecimal.valueOf(1000)) >= 0) return BigDecimal.valueOf(100);
        if (totalValue.compareTo(BigDecimal.valueOf(500)) >= 0) return BigDecimal.valueOf(90);
        if (totalValue.compareTo(BigDecimal.valueOf(100)) >= 0) return BigDecimal.valueOf(80);
        if (totalValue.compareTo(BigDecimal.valueOf(50)) >= 0) return BigDecimal.valueOf(70);
        if (totalValue.compareTo(BigDecimal.valueOf(10)) >= 0) return BigDecimal.valueOf(60);
        if (totalValue.compareTo(BigDecimal.valueOf(5)) >= 0) return BigDecimal.valueOf(50);
        if (totalValue.compareTo(BigDecimal.valueOf(1)) >= 0) return BigDecimal.valueOf(40);
        if (totalValue.compareTo(BigDecimal.valueOf(0.1)) >= 0) return BigDecimal.valueOf(30);
        if (totalValue.compareTo(BigDecimal.ZERO) > 0) return BigDecimal.valueOf(20);

        return BigDecimal.ZERO;
    }

    /**
     * 计算用户多样性评分
     */
    private BigDecimal calculateUserDiversityScore(SmartContract contract) {
        int uniqueUsers = contract.getUniqueUsers24h();
        int totalInteractions = contract.getInteractionCount24h();

        if (totalInteractions == 0) return BigDecimal.ZERO;

        // 计算用户多样性比例
        double diversityRatio = (double) uniqueUsers / totalInteractions;

        // 基于独立用户数和多样性比例的评分
        BigDecimal userScore = BigDecimal.ZERO;
        if (uniqueUsers >= 100) userScore = BigDecimal.valueOf(50);
        else if (uniqueUsers >= 50) userScore = BigDecimal.valueOf(40);
        else if (uniqueUsers >= 20) userScore = BigDecimal.valueOf(30);
        else if (uniqueUsers >= 10) userScore = BigDecimal.valueOf(20);
        else if (uniqueUsers >= 5) userScore = BigDecimal.valueOf(10);

        BigDecimal diversityScore = BigDecimal.valueOf(diversityRatio * 50);

        return userScore.add(diversityScore).min(BigDecimal.valueOf(100));
    }

    /**
     * 计算Gas效率评分
     */
    private BigDecimal calculateGasEfficiencyScore(SmartContract contract) {
        BigDecimal avgGasUsed = contract.getAvgGasUsed24h();

        if (avgGasUsed.compareTo(BigDecimal.ZERO) == 0) return BigDecimal.valueOf(50);

        // 基于平均Gas使用量的评分（Gas使用越少评分越高）
        if (avgGasUsed.compareTo(BigDecimal.valueOf(21000)) <= 0) return BigDecimal.valueOf(100);
        if (avgGasUsed.compareTo(BigDecimal.valueOf(50000)) <= 0) return BigDecimal.valueOf(90);
        if (avgGasUsed.compareTo(BigDecimal.valueOf(100000)) <= 0) return BigDecimal.valueOf(80);
        if (avgGasUsed.compareTo(BigDecimal.valueOf(200000)) <= 0) return BigDecimal.valueOf(70);
        if (avgGasUsed.compareTo(BigDecimal.valueOf(300000)) <= 0) return BigDecimal.valueOf(60);
        if (avgGasUsed.compareTo(BigDecimal.valueOf(500000)) <= 0) return BigDecimal.valueOf(50);
        if (avgGasUsed.compareTo(BigDecimal.valueOf(1000000)) <= 0) return BigDecimal.valueOf(40);

        return BigDecimal.valueOf(30);
    }

    @Override
    public List<SmartContract> getTopContracts(int limit) {
        return contractRepository.findHighScoreContracts(BigDecimal.valueOf(50))
            .stream().limit(limit).toList();
    }

    @Override
    public List<SmartContract> getRecentContracts(int hours, int limit) {
        LocalDateTime since = LocalDateTime.now().minusHours(hours);
        return contractRepository.findRecentContracts(since)
            .stream().limit(limit).toList();
    }

    @Override
    @Transactional
    public void cleanupOldInteractions() {
        try {
            log.info("🧹 开始清理旧的交互记录...");

            // 删除7天前的交互记录
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7);
            interactionRepository.deleteOldInteractions(cutoffTime);

            log.info("✅ 旧交互记录清理完成");
        } catch (Exception e) {
            log.error("清理旧交互记录失败", e);
        }
    }
}
