package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.dto.common.PaginationDTO;
import com.dddd.web3tools.dto.monitoring.*;
import com.dddd.web3tools.entity.ContractInteraction;
import com.dddd.web3tools.entity.SmartContract;
import com.dddd.web3tools.repository.ContractInteractionRepository;
import com.dddd.web3tools.repository.SmartContractRepository;
import com.dddd.web3tools.service.MonitoringService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 链上异常监控服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MonitoringServiceImpl implements MonitoringService {
    
    private final SmartContractRepository smartContractRepository;
    private final ContractInteractionRepository contractInteractionRepository;
    
    @Override
    public ContractMonitoringListDTO getContractMonitoringData(
            String timeRange, String category, String sortBy, String sortOrder, 
            int page, int limit) {
        
        // 创建排序条件
        Sort.Direction direction = "asc".equalsIgnoreCase(sortOrder) ? 
            Sort.Direction.ASC : Sort.Direction.DESC;
        
        Sort sort = createSort(sortBy, direction);
        Pageable pageable = PageRequest.of(page - 1, limit, sort);
        
        // 获取合约数据
        Page<SmartContract> contractPage;
        if (category != null && !category.isEmpty()) {
            // TODO: 添加category字段到SmartContract实体，暂时忽略category过滤
            contractPage = smartContractRepository.findByIsFilteredFalseOrderByScoreDesc(pageable);
        } else {
            contractPage = smartContractRepository.findByIsFilteredFalseOrderByScoreDesc(pageable);
        }
        
        // 转换为DTO
        List<ContractMonitoringDTO> contractDTOs = contractPage.getContent().stream()
            .map(this::convertToMonitoringDTO)
            .collect(Collectors.toList());
        
        // 创建分页信息
        PaginationDTO pagination = new PaginationDTO(
            page, limit, contractPage.getTotalElements(), contractPage.getTotalPages()
        );
        
        return new ContractMonitoringListDTO(contractDTOs, pagination);
    }
    
    @Override
    public ContractHistoryDTO getContractHistory(Long contractId, String timeRange, String interval) {
        
        SmartContract contract = smartContractRepository.findById(contractId)
            .orElseThrow(() -> new RuntimeException("Contract not found"));
        
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = calculateStartTime(endTime, timeRange);
        
        // 获取历史交互数据
        List<ContractInteraction> interactions = contractInteractionRepository
            .findByContractAddressAndInteractionTimeBetween(
                contract.getContractAddress(), startTime, endTime);
        
        // 根据interval分组数据
        Map<LocalDateTime, List<ContractInteraction>> groupedData = 
            groupInteractionsByInterval(interactions, interval);
        
        // 转换为DTO
        List<ContractHistoryDataDTO> historyData = groupedData.entrySet().stream()
            .map(entry -> {
                List<ContractInteraction> intervalData = entry.getValue();
                return new ContractHistoryDataDTO(
                    entry.getKey(),
                    entry.getKey().format(DateTimeFormatter.ofPattern("HH:mm")),
                    intervalData.size(),
                    intervalData.stream().mapToLong(ci -> ci.getGasUsed() != null ? ci.getGasUsed() : 0).sum(),
                    (int) intervalData.stream().map(ContractInteraction::getUserAddress).distinct().count(),
                    calculateAvgFeeRate(intervalData)
                );
            })
            .sorted(Comparator.comparing(ContractHistoryDataDTO::getTimestamp))
            .collect(Collectors.toList());
        
        return new ContractHistoryDTO(contractId, historyData);
    }
    
    @Override
    public SystemOverviewDTO getSystemOverview(String timeRange) {
        
        // 获取基础统计
        Object[] stats = smartContractRepository.getSystemStats();
        System.out.println("Stats result: " + (stats != null ? java.util.Arrays.toString(stats) : "null"));
        System.out.println("Stats length: " + (stats != null ? stats.length : 0));
        
        Long totalContracts = 0L;
        Long totalInteractions = 0L;
        
        if (stats != null && stats.length >= 2) {
            // 处理可能的嵌套数组情况
            Object firstStat = stats[0];
            Object secondStat = stats[1];
            
            System.out.println("First stat type: " + (firstStat != null ? firstStat.getClass().getName() : "null"));
            System.out.println("Second stat type: " + (secondStat != null ? secondStat.getClass().getName() : "null"));
            
            if (firstStat instanceof Number) {
                totalContracts = ((Number) firstStat).longValue();
            } else if (firstStat instanceof Object[] && ((Object[]) firstStat).length > 0) {
                Object nested = ((Object[]) firstStat)[0];
                if (nested instanceof Number) {
                    totalContracts = ((Number) nested).longValue();
                }
            }
            
            if (secondStat instanceof Number) {
                totalInteractions = ((Number) secondStat).longValue();
            } else if (secondStat instanceof Object[] && ((Object[]) secondStat).length > 0) {
                Object nested = ((Object[]) secondStat)[0];
                if (nested instanceof Number) {
                    totalInteractions = ((Number) nested).longValue();
                }
            }
        }
        
        // 获取异常合约数量
        Long anomalyCount = smartContractRepository.countHighAnomalyContracts(BigDecimal.valueOf(70));
        
        // 获取最大增长合约
        List<SmartContract> topGainers = smartContractRepository.findTopGainers(
            PageRequest.of(0, 1));
        SystemOverviewDTO.TopGainerDTO topGainer = null;
        if (!topGainers.isEmpty()) {
            SmartContract topContract = topGainers.get(0);
            topGainer = new SystemOverviewDTO.TopGainerDTO(
                topContract.getId(),
                topContract.getContractName(),
                topContract.getContractSymbol(),
                calculateChangePercent(topContract)
            );
        }
        
        // 模拟网络统计数据
        SystemOverviewDTO.NetworkStatsDTO networkStats = new SystemOverviewDTO.NetworkStatsDTO(
            BigDecimal.valueOf(45.2), // avgGasPrice
            BigDecimal.valueOf(78.5), // networkUtilization
            BigDecimal.valueOf(12.1)  // blockTime
        );
        
        return new SystemOverviewDTO(totalContracts, totalInteractions, anomalyCount, 
            topGainer, networkStats);
    }
    
    @Override
    public ContractDetailDTO getContractDetails(Long contractId) {
        
        SmartContract contract = smartContractRepository.findById(contractId)
            .orElseThrow(() -> new RuntimeException("Contract not found"));
        
        // 基础信息
        ContractDetailDTO.ContractInfoDTO contractInfo = new ContractDetailDTO.ContractInfoDTO(
            contract.getCreatedAt(),
            contract.getDeployerAddress(),
            true, // 假设已验证
            "verified"
        );
        
        // 实时指标
        ContractDetailDTO.RealTimeMetricsDTO realTimeMetrics = 
            new ContractDetailDTO.RealTimeMetricsDTO(
                contract.getInteractionCount24h(),
                calculateInteractions7d(contract),
                calculateInteractions30d(contract),
                calculateChangePercent(contract),
                contract.getTotalGasUsed24h(),
                contract.getUniqueUsers24h(),
                contract.getAvgTransactionValue24h(),
                contract.getTotalValue24h(),
                calculateAnomalyScore(contract),
                contract.getLastInteractionTime(),
                determineTrend(contract),
                determineRiskLevel(contract)
            );
        
        // 费用分布（模拟数据）
        List<ContractDetailDTO.FeeDistributionDTO> feeDistribution = Arrays.asList(
            new ContractDetailDTO.FeeDistributionDTO(
                BigDecimal.valueOf(10.0), BigDecimal.valueOf(20.0), 1250L, BigDecimal.valueOf(25.5)
            ),
            new ContractDetailDTO.FeeDistributionDTO(
                BigDecimal.valueOf(20.0), BigDecimal.valueOf(50.0), 2000L, BigDecimal.valueOf(40.8)
            )
        );
        
        // 社交链接（模拟数据）
        Map<String, String> socialLinks = new HashMap<>();
        socialLinks.put("twitter", "@" + contract.getContractSymbol());
        socialLinks.put("discord", "https://discord.gg/" + contract.getContractSymbol().toLowerCase());
        
        return new ContractDetailDTO(
            contract.getId(),
            contract.getContractAddress(),
            contract.getContractName(),
            contract.getContractSymbol(),
            determineCategory(contract),
            "智能合约详细描述", // 模拟描述
            "https://example.com", // 模拟网站
            socialLinks,
            contractInfo,
            realTimeMetrics,
            feeDistribution
        );
    }
    
    @Override
    public ContractSearchDTO searchContracts(String query, String category, int limit) {
        
        Pageable pageable = PageRequest.of(0, limit);
        List<SmartContract> contracts = smartContractRepository.searchContracts(query, pageable);
        
        List<ContractSearchDTO.ContractSearchResultDTO> results = contracts.stream()
            .map(contract -> new ContractSearchDTO.ContractSearchResultDTO(
                contract.getId(),
                contract.getContractAddress(),
                contract.getContractName(),
                contract.getContractSymbol(),
                determineCategory(contract),
                calculateAnomalyScore(contract),
                contract.getInteractionCount24h(),
                calculateChangePercent(contract)
            ))
            .collect(Collectors.toList());
        
        return new ContractSearchDTO(results, (long) results.size());
    }
    
    // 辅助方法
    
    private ContractMonitoringDTO convertToMonitoringDTO(SmartContract contract) {
        return new ContractMonitoringDTO(
            contract.getId(),
            contract.getContractAddress(),
            contract.getContractName(),
            contract.getContractSymbol(),
            determineCategory(contract),
            contract.getInteractionCount24h(),
            calculateInteractions7d(contract),
            calculateInteractions30d(contract),
            calculateChangePercent(contract),
            contract.getTotalGasUsed24h(),
            contract.getUniqueUsers24h(),
            contract.getAvgTransactionValue24h(),
            calculateAnomalyScore(contract),
            contract.getLastInteractionTime(),
            determineTrend(contract),
            determineRiskLevel(contract)
        );
    }
    
    private Sort createSort(String sortBy, Sort.Direction direction) {
        switch (sortBy.toLowerCase()) {
            case "interactions":
                return Sort.by(direction, "interactionCount24h");
            case "change":
                return Sort.by(direction, "interactionCount24h"); // 简化实现
            case "anomaly":
                return Sort.by(direction, "score");
            case "users":
                return Sort.by(direction, "uniqueUsers24h");
            default:
                return Sort.by(direction, "interactionCount24h");
        }
    }
    
    private LocalDateTime calculateStartTime(LocalDateTime endTime, String timeRange) {
        switch (timeRange.toLowerCase()) {
            case "1h":
                return endTime.minusHours(1);
            case "24h":
                return endTime.minusDays(1);
            case "7d":
                return endTime.minusDays(7);
            case "30d":
                return endTime.minusDays(30);
            default:
                return endTime.minusDays(1);
        }
    }
    
    private Map<LocalDateTime, List<ContractInteraction>> groupInteractionsByInterval(
            List<ContractInteraction> interactions, String interval) {
        
        // 简化实现，按小时分组
        return interactions.stream()
            .collect(Collectors.groupingBy(
                interaction -> interaction.getInteractionTime().withMinute(0).withSecond(0).withNano(0)
            ));
    }
    
    private BigDecimal calculateAvgFeeRate(List<ContractInteraction> interactions) {
        if (interactions.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        // 简化计算，使用模拟数据
        return BigDecimal.valueOf(25.3);
    }
    
    private Integer calculateInteractions7d(SmartContract contract) {
        // 简化实现，使用24h数据乘以估算系数
        if (contract.getInteractionCount24h() == null) {
            return 0;
        }
        return contract.getInteractionCount24h() * 5;
    }
    
    private Integer calculateInteractions30d(SmartContract contract) {
        // 简化实现，使用24h数据乘以估算系数
        if (contract.getInteractionCount24h() == null) {
            return 0;
        }
        return contract.getInteractionCount24h() * 20;
    }
    
    private BigDecimal calculateChangePercent(SmartContract contract) {
        // 简化实现，基于评分计算变化百分比
        if (contract.getScore() == null) {
            return BigDecimal.ZERO;
        }
        return contract.getScore().multiply(BigDecimal.valueOf(2));
    }
    
    private Integer calculateAnomalyScore(SmartContract contract) {
        // 将评分转换为异常分数
        if (contract.getScore() == null) {
            return 0;
        }
        return contract.getScore().multiply(BigDecimal.valueOf(10)).intValue();
    }
    
    private String determineTrend(SmartContract contract) {
        BigDecimal score = contract.getScore();
        if (score == null) {
            return "stable";
        }
        if (score.compareTo(BigDecimal.valueOf(60)) > 0) {
            return "up";
        } else if (score.compareTo(BigDecimal.valueOf(40)) < 0) {
            return "down";
        }
        return "stable";
    }
    
    private String determineRiskLevel(SmartContract contract) {
        BigDecimal score = contract.getScore();
        if (score == null) {
            return "low";
        }
        if (score.compareTo(BigDecimal.valueOf(70)) > 0) {
            return "high";
        } else if (score.compareTo(BigDecimal.valueOf(50)) > 0) {
            return "medium";
        }
        return "low";
    }
    
    private String determineCategory(SmartContract contract) {
        // 基于合约特征判断类别，简化实现
        if (contract.getIsToken()) {
            return "Token";
        }
        // 可以根据合约地址、名称等进一步判断
        return "DeFi";
    }
}