package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.dto.*;
import com.dddd.web3tools.entity.*;
import com.dddd.web3tools.repository.*;
import com.dddd.web3tools.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 钱包管理服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WalletManagementServiceImpl implements WalletManagementService {

    private final UserRepository userRepository;
    private final UserWalletRepository userWalletRepository;
    private final WalletTokenRepository walletTokenRepository;
    private final WalletHistoryRepository walletHistoryRepository;
    private final BlockchainQueryService blockchainQueryService;
    private final MarketDataService marketDataService;
    private final WalletBalanceCacheService walletBalanceCacheService;

    // ==================== 钱包管理接口 ====================

    @Override
    public UserWalletDTO.WalletsResponse getUserWalletsByAddress(String address) {
        log.info("通过钱包地址获取用户所有钱包: address={}", address);

        List<UserWallet> wallets = userWalletRepository.findAllWalletsByAddress(address);

        if (wallets.isEmpty()) {
            log.warn("未找到地址对应的钱包: address={}", address);
            return UserWalletDTO.WalletsResponse.builder()
                    .wallets(new HashMap<>())
                    .build();
        }

        Map<String, List<UserWalletDTO.WalletInfo>> walletsByChain = wallets.stream()
                .collect(Collectors.groupingBy(
                        UserWallet::getChain,
                        Collectors.mapping(this::convertToWalletInfoWithCache, Collectors.toList())
                ));

        return UserWalletDTO.WalletsResponse.builder()
                .wallets(walletsByChain)
                .build();
    }

    @Override
    @Transactional
    public UserWalletDTO.WalletInfo addWalletByAddress(String ownerAddress, UserWalletDTO.AddWalletRequest request) {
        log.info("通过钱包地址添加钱包: ownerAddress={}, chain={}, address={}",
                ownerAddress, request.getChain(), request.getAddress());

        // 通过ownerAddress查找或创建用户
        User user = getUserByAddress(ownerAddress);

        // 验证地址格式
        if (!blockchainQueryService.isValidAddress(request.getChain(), request.getAddress())) {
            throw new IllegalArgumentException("无效的钱包地址格式");
        }

        // 检查是否已存在
        if (userWalletRepository.existsByUserAddressAndWalletAddress(ownerAddress, request.getAddress())) {
            throw new IllegalArgumentException("钱包地址已存在");
        }

        // 创建钱包
        UserWallet wallet = UserWallet.builder()
                .user(user)
                .chain(request.getChain())
                .address(request.getAddress())
                .name(request.getName())
                .notes(request.getNotes())
                .build();

        wallet = userWalletRepository.save(wallet);

        // 立即获取余额信息（第一次添加时）
        try {
            log.info("第一次添加钱包，立即查询余额: chain={}, address={}",
                    request.getChain(), request.getAddress());

            UserWalletDTO.WalletInfo balanceInfo = blockchainQueryService.getWalletBalance(
                    request.getChain(), request.getAddress());

            if (balanceInfo != null) {
                wallet.setBalance(balanceInfo.getBalance());
                wallet.setUsdValue(balanceInfo.getUsdValue());
                wallet.setUpdatedAt(LocalDateTime.now());
                wallet = userWalletRepository.save(wallet);

                // 保存代币信息
                if (balanceInfo.getTokens() != null && !balanceInfo.getTokens().isEmpty()) {
                    saveWalletTokens(wallet, balanceInfo.getTokens());
                }
            }
        } catch (Exception e) {
            log.warn("获取钱包余额失败，将在后台任务中重试: {}", e.getMessage());
        }

        return convertToWalletInfo(wallet);
    }

    @Override
    @Transactional
    public UserWalletDTO.WalletInfo updateWalletByAddress(String ownerAddress, String walletAddress, UserWalletDTO.UpdateWalletRequest request) {
        log.info("通过地址更新钱包: ownerAddress={}, walletAddress={}", ownerAddress, walletAddress);

        // 通过ownerAddress找到用户，然后找到对应的钱包
        User user = getUserByAddress(ownerAddress);
        UserWallet wallet = userWalletRepository.findByUserAddressAndWalletAddress(ownerAddress, walletAddress)
                .orElseThrow(() -> new RuntimeException("钱包不存在"));

        // 更新钱包信息
        if (request.getName() != null) {
            wallet.setName(request.getName());
        }
        if (request.getNotes() != null) {
            wallet.setNotes(request.getNotes());
        }

        wallet = userWalletRepository.save(wallet);
        return convertToWalletInfo(wallet);
    }

    @Override
    @Transactional
    public void deleteWalletByAddress(String ownerAddress, String walletAddress) {
        log.info("通过地址删除钱包: ownerAddress={}, walletAddress={}", ownerAddress, walletAddress);

        User user = getUserByAddress(ownerAddress);
        UserWallet wallet = userWalletRepository.findByUserAddressAndWalletAddress(ownerAddress, walletAddress)
                .orElseThrow(() -> new RuntimeException("钱包不存在"));

        // 删除缓存
        walletBalanceCacheService.removeCachedWalletBalance(wallet.getChain(), wallet.getAddress());

        // 删除相关的代币记录
        walletTokenRepository.deleteByWalletId(wallet.getId());

        // 删除钱包
        userWalletRepository.delete(wallet);
    }

    @Override
    @Transactional
    public UserWalletDTO.BatchDeleteResponse batchDeleteWalletsByAddress(String ownerAddress, UserWalletDTO.BatchDeleteRequest request) {
        log.info("通过地址批量删除钱包: ownerAddress={}, count={}", ownerAddress, request.getWalletIds().size());

        User user = getUserByAddress(ownerAddress);
        int deletedCount = 0;
        List<String> failedIds = new ArrayList<>();

        for (String walletId : request.getWalletIds()) {
            try {
                // 通过walletId找到钱包，验证权限后删除
                UserWallet wallet = userWalletRepository.findById(Long.parseLong(walletId))
                        .orElseThrow(() -> new RuntimeException("钱包不存在"));

                if (!wallet.getUser().getId().equals(user.getId())) {
                    throw new RuntimeException("无权限删除此钱包");
                }

                // 删除缓存
                walletBalanceCacheService.removeCachedWalletBalance(wallet.getChain(), wallet.getAddress());

                // 删除相关的代币记录
                walletTokenRepository.deleteByWalletId(wallet.getId());

                // 删除钱包
                userWalletRepository.delete(wallet);
                deletedCount++;
            } catch (Exception e) {
                log.error("删除钱包失败: walletId={}, error={}", walletId, e.getMessage());
                failedIds.add(walletId);
            }
        }

        return UserWalletDTO.BatchDeleteResponse.builder()
                .deletedCount(deletedCount)
                .failedIds(failedIds)
                .build();
    }

    @Override
    @Transactional
    public UserWalletDTO.RefreshResponse refreshWalletBalanceByAddress(String ownerAddress, String walletAddress) {
        log.info("通过地址刷新钱包余额: ownerAddress={}, walletAddress={}", ownerAddress, walletAddress);

        User user = getUserByAddress(ownerAddress);
        UserWallet wallet = userWalletRepository.findByUserAddressAndWalletAddress(ownerAddress, walletAddress)
                .orElseThrow(() -> new RuntimeException("钱包不存在"));

        // 优先从缓存获取余额信息
        UserWalletDTO.WalletInfo cachedInfo = walletBalanceCacheService.getCachedWalletBalance(
                wallet.getChain(), wallet.getAddress());

        List<UserWalletDTO.TokenInfo> tokens;
        BigDecimal balance = wallet.getBalance();
        BigDecimal usdValue = wallet.getUsdValue();

        if (cachedInfo != null) {
            // 使用缓存的数据
            balance = cachedInfo.getBalance();
            usdValue = cachedInfo.getUsdValue();
            tokens = cachedInfo.getTokens();
        } else {
            // 从数据库获取代币信息
            tokens = walletTokenRepository.findByWalletIdOrderByUsdValueDesc(wallet.getId())
                    .stream()
                    .map(this::convertToTokenInfo)
                    .collect(Collectors.toList());
        }

        return UserWalletDTO.RefreshResponse.builder()
                .id(wallet.getId().toString())
                .balance(balance)
                .usdValue(usdValue)
                .tokens(tokens)
                .updatedAt(wallet.getUpdatedAt())
                .build();
    }

    @Override
    @Transactional
    public UserWalletDTO.RefreshAllResponse refreshAllWalletsByAddress(String ownerAddress) {
        log.info("通过地址刷新所有钱包: ownerAddress={}", ownerAddress);

        User user = getUserByAddress(ownerAddress);
        List<UserWallet> wallets = userWalletRepository.findAllWalletsByAddress(ownerAddress);

        BigDecimal totalValue = BigDecimal.ZERO;
        int refreshedCount = 0;

        for (UserWallet wallet : wallets) {
            try {
                // 优先从缓存获取余额信息
                UserWalletDTO.WalletInfo cachedInfo = walletBalanceCacheService.getCachedWalletBalance(
                        wallet.getChain(), wallet.getAddress());

                BigDecimal walletValue;
                if (cachedInfo != null) {
                    walletValue = cachedInfo.getUsdValue();
                } else {
                    walletValue = wallet.getUsdValue();
                }

                if (walletValue != null) {
                    totalValue = totalValue.add(walletValue);
                }
                refreshedCount++;
            } catch (Exception e) {
                log.error("获取钱包余额失败: walletId={}, error={}", wallet.getId(), e.getMessage());
            }
        }

        return UserWalletDTO.RefreshAllResponse.builder()
                .refreshedCount(refreshedCount)
                .totalValue(totalValue)
                .updatedAt(LocalDateTime.now())
                .build();
    }

    // ==================== 统计数据接口 ====================
    @Override
    public WalletOverviewDTO getWalletOverviewByAddress(String ownerAddress) {
        log.info("通过地址获取资产总览: ownerAddress={}", ownerAddress);

        User user = getUserByAddress(ownerAddress);
        List<UserWallet> wallets = userWalletRepository.findAllWalletsByAddress(ownerAddress);

        BigDecimal totalValue = userWalletRepository.sumUsdValueByUserAddress(ownerAddress);
        long totalWallets = userWalletRepository.countByUserAddress(ownerAddress);

        // 按链分组统计
        Map<String, WalletOverviewDTO.ChainDistribution> chainStats = new HashMap<>();

        for (UserWallet wallet : wallets) {
            String chain = wallet.getChain();
            WalletOverviewDTO.ChainDistribution distribution = chainStats.computeIfAbsent(chain,
                    k -> WalletOverviewDTO.ChainDistribution.builder()
                            .chain(chain)
                            .name(getChainName(chain))
                            .value(BigDecimal.ZERO)
                            .walletCount(0)
                            .change24h(getChainChange24h(chain))
                            .build());

            BigDecimal walletValue = wallet.getUsdValue() != null ? wallet.getUsdValue() : BigDecimal.ZERO;
            distribution.setValue(distribution.getValue().add(walletValue));
            distribution.setWalletCount(distribution.getWalletCount() + 1);
        }

        // 计算百分比
        List<WalletOverviewDTO.ChainDistribution> chainDistribution = chainStats.values().stream()
                .peek(dist -> {
                    if (totalValue.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal percentage = dist.getValue()
                                .divide(totalValue, 4, RoundingMode.HALF_UP)
                                .multiply(new BigDecimal("100"));
                        dist.setPercentage(percentage);
                    } else {
                        dist.setPercentage(BigDecimal.ZERO);
                    }
                })
                .collect(Collectors.toList());

        return WalletOverviewDTO.builder()
                .totalValue(totalValue)
                .totalWallets((int) totalWallets)
                .chainDistribution(chainDistribution)
                .build();
    }

    @Override
    public WalletTrendDTO getWalletTrends(String ownerAddress, String timeRange) {
        log.info("获取资产趋势: ownerAddress={}, timeRange={}", ownerAddress, timeRange);

        User user = getUserByAddress(ownerAddress);

        LocalDate endDate = LocalDate.now();
        LocalDate startDate;
        String historyType = "DAILY";

        // 根据时间范围确定查询参数
        switch (timeRange.toLowerCase()) {
            case "7d":
                startDate = endDate.minusDays(7);
                break;
            case "30d":
                startDate = endDate.minusDays(30);
                break;
            case "1y":
                startDate = endDate.minusYears(1);
                historyType = "MONTHLY";
                break;
            case "all":
                startDate = endDate.minusYears(10); // 假设最多查询10年
                historyType = "MONTHLY";
                break;
            default:
                startDate = endDate.minusDays(7);
                timeRange = "7d";
        }

        List<WalletHistory> historyList = walletHistoryRepository.findByUserAddressAndTypeAndDateBetweenOrderByDateAsc(
                ownerAddress, historyType, startDate, endDate);

        // 如果没有历史数据，生成当前数据点
        if (historyList.isEmpty()) {
            BigDecimal currentValue = userWalletRepository.sumUsdValueByUserAddress(ownerAddress);
            WalletTrendDTO.DataPoint currentPoint = WalletTrendDTO.DataPoint.builder()
                    .date(endDate.format(DateTimeFormatter.ofPattern("MM/dd")))
                    .total(currentValue)
                    .timestamp(endDate.atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC) * 1000)
                    .build();

            return WalletTrendDTO.builder()
                    .timeRange(timeRange)
                    .dataPoints(Arrays.asList(currentPoint))
                    .build();
        }

        List<WalletTrendDTO.DataPoint> dataPoints = historyList.stream()
                .map(history -> WalletTrendDTO.DataPoint.builder()
                        .date(history.getDate().format(DateTimeFormatter.ofPattern("MM/dd")))
                        .total(history.getTotalValue())
                        .timestamp(history.getDate().atStartOfDay().toEpochSecond(java.time.ZoneOffset.UTC) * 1000)
                        .build())
                .collect(Collectors.toList());

        return WalletTrendDTO.builder()
                .timeRange(timeRange)
                .dataPoints(dataPoints)
                .build();
    }

    // ==================== 市场数据接口 ====================

    @Override
    public MarketDataDTO getTokenPrices(String symbols) {
        log.info("获取代币价格: symbols={}", symbols);

        List<String> symbolList = Arrays.asList(symbols.split(","));
        return marketDataService.getTokenPrices(symbolList);
    }

    // ==================== 私有方法 ====================

    /**
     * 转换为钱包信息DTO（使用缓存）
     */
    private UserWalletDTO.WalletInfo convertToWalletInfoWithCache(UserWallet wallet) {
        // 优先从缓存获取余额信息
        UserWalletDTO.WalletInfo cachedInfo = walletBalanceCacheService.getCachedWalletBalance(
                wallet.getChain(), wallet.getAddress());

        if (cachedInfo != null) {
            // 使用缓存的余额和代币信息，但保留数据库中的其他信息
            return UserWalletDTO.WalletInfo.builder()
                    .id(wallet.getId().toString())
                    .address(wallet.getAddress())
                    .name(wallet.getName())
                    .notes(wallet.getNotes())
                    .balance(cachedInfo.getBalance())
                    .usdValue(cachedInfo.getUsdValue())
                    .tokens(cachedInfo.getTokens())
                    .createdAt(wallet.getCreatedAt())
                    .updatedAt(wallet.getUpdatedAt())
                    .build();
        }

        // 缓存未命中，从数据库获取
        return convertToWalletInfo(wallet);
    }

    /**
     * 转换为钱包信息DTO（从数据库）
     */
    private UserWalletDTO.WalletInfo convertToWalletInfo(UserWallet wallet) {
        List<UserWalletDTO.TokenInfo> tokens = walletTokenRepository.findByWalletIdOrderByUsdValueDesc(wallet.getId())
                .stream()
                .map(this::convertToTokenInfo)
                .collect(Collectors.toList());

        return UserWalletDTO.WalletInfo.builder()
                .id(wallet.getId().toString())
                .address(wallet.getAddress())
                .name(wallet.getName())
                .notes(wallet.getNotes())
                .balance(wallet.getBalance())
                .usdValue(wallet.getUsdValue())
                .tokens(tokens)
                .createdAt(wallet.getCreatedAt())
                .updatedAt(wallet.getUpdatedAt())
                .build();
    }

    /**
     * 转换为代币信息DTO
     */
    private UserWalletDTO.TokenInfo convertToTokenInfo(WalletToken token) {
        return UserWalletDTO.TokenInfo.builder()
                .symbol(token.getSymbol())
                .name(token.getName())
                .balance(token.getBalance())
                .usdValue(token.getUsdValue())
                .price(token.getPrice())
                .change24h(token.getChange24h())
                .build();
    }

    /**
     * 保存钱包代币信息
     */
    private void saveWalletTokens(UserWallet wallet, List<UserWalletDTO.TokenInfo> tokens) {
        if (tokens == null || tokens.isEmpty()) {
            return;
        }

        List<WalletToken> walletTokens = tokens.stream()
                .map(tokenInfo -> WalletToken.builder()
                        .wallet(wallet)
                        .symbol(tokenInfo.getSymbol())
                        .name(tokenInfo.getName())
                        .balance(tokenInfo.getBalance())
                        .usdValue(tokenInfo.getUsdValue())
                        .price(tokenInfo.getPrice())
                        .change24h(tokenInfo.getChange24h())
                        .build())
                .collect(Collectors.toList());

        walletTokenRepository.saveAll(walletTokens);
    }

    /**
     * 获取链名称
     */
    private String getChainName(String chain) {
        switch (chain.toUpperCase()) {
            case "EVM": return "Ethereum";
            case "BTC": return "Bitcoin";
            case "SOLANA": return "Solana";
            default: return chain;
        }
    }

    /**
     * 获取链的24小时涨跌幅
     */
    private BigDecimal getChainChange24h(String chain) {
        switch (chain.toUpperCase()) {
            case "EVM": return marketDataService.getToken24hChange("ETH");
            case "BTC": return marketDataService.getToken24hChange("BTC");
            case "SOLANA": return marketDataService.getToken24hChange("SOL");
            default: return BigDecimal.ZERO;
        }
    }

    // ==================== 基于地址的方法实现 ====================

    /**
     * 根据地址获取用户
     */
    private User getUserByAddress(String address) {
        return userRepository.findByAddress(address)
                .orElseGet(() -> {
                    // 如果用户不存在，创建一个新用户
                    User newUser = User.builder()
                            .address(address)
                            .walletType("unknown")
                            .build();
                    return userRepository.save(newUser);
                });
    }
}
