package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.constant.RedisKeyConstant;
import com.dddd.web3tools.entity.Tweet;
import com.dddd.web3tools.repository.TweetRepository;
import com.dddd.web3tools.service.TweetCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 推特缓存服务实现类
 */
@Slf4j
@Service
public class TweetCacheServiceImpl implements TweetCacheService {

    @Autowired
    private TweetRepository tweetRepository;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private boolean redisAvailable = false;

    @PostConstruct
    public void init() {
        try {
            if (redisTemplate != null) {
                // 测试Redis连接
                redisTemplate.opsForValue().get("test");
                redisAvailable = true;
                log.info("推特缓存服务初始化完成，Redis可用");
                
                // 初始化推特ID缓存
                initializeTweetIdsCache();
            } else {
                log.warn("Redis不可用，推特缓存服务将直接查询数据库");
            }
        } catch (Exception e) {
            log.warn("Redis连接失败，推特缓存服务将直接查询数据库: {}", e.getMessage());
            redisAvailable = false;
        }
    }

    @Override
    public boolean existsTweetId(String tweetId) {
        if (!redisAvailable) {
            // Redis不可用时直接查询数据库
            return tweetRepository.existsById(tweetId);
        }

        try {
            // 先检查Redis缓存
            Boolean exists = redisTemplate.opsForSet().isMember(RedisKeyConstant.TWEET_IDS_CACHE_KEY, tweetId);
            if (exists != null && exists) {
                log.debug("从Redis缓存中找到推特ID: {}", tweetId);
                return true;
            }

            // Redis中没有，检查缓存是否已初始化
            Long cacheSize = redisTemplate.opsForSet().size(RedisKeyConstant.TWEET_IDS_CACHE_KEY);
            if (cacheSize == null || cacheSize == 0) {
                log.info("Redis缓存为空，重新初始化推特ID缓存");
                initializeTweetIdsCache();
                
                // 重新检查
                exists = redisTemplate.opsForSet().isMember(RedisKeyConstant.TWEET_IDS_CACHE_KEY, tweetId);
                if (exists != null && exists) {
                    log.debug("初始化缓存后从Redis中找到推特ID: {}", tweetId);
                    return true;
                }
            }

            // Redis中确实没有，返回false
            log.debug("推特ID不存在于缓存中: {}", tweetId);
            return false;

        } catch (Exception e) {
            log.error("检查推特ID缓存失败，回退到数据库查询: tweetId={}, error={}", tweetId, e.getMessage());
            return tweetRepository.existsById(tweetId);
        }
    }

    @Override
    @Transactional
    public int batchSaveTweets(List<Tweet> tweets) {
        if (tweets == null || tweets.isEmpty()) {
            return 0;
        }

        try {
            log.info("开始批量保存推特，数量: {}", tweets.size());
            
            // 批量保存到数据库
            List<Tweet> savedTweets = tweetRepository.saveAll(tweets);
            
            // 将新保存的推特ID添加到Redis缓存
            if (redisAvailable) {
                addTweetIdsToCache(savedTweets.stream()
                        .map(Tweet::getId)
                        .collect(Collectors.toList()));
            }
            
            log.info("批量保存推特完成，实际保存数量: {}", savedTweets.size());
            return savedTweets.size();
            
        } catch (Exception e) {
            log.error("批量保存推特失败", e);
            throw new RuntimeException("批量保存推特失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void addTweetIdToCache(String tweetId) {
        if (!redisAvailable) {
            return;
        }

        try {
            redisTemplate.opsForSet().add(RedisKeyConstant.TWEET_IDS_CACHE_KEY, tweetId);
            // 设置过期时间为7天
            redisTemplate.expire(RedisKeyConstant.TWEET_IDS_CACHE_KEY, 
                    RedisKeyConstant.TWEET_IDS_CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.debug("推特ID已添加到缓存: {}", tweetId);
        } catch (Exception e) {
            log.error("添加推特ID到缓存失败: tweetId={}, error={}", tweetId, e.getMessage());
        }
    }

    /**
     * 批量添加推特ID到缓存
     */
    private void addTweetIdsToCache(List<String> tweetIds) {
        if (!redisAvailable || tweetIds.isEmpty()) {
            return;
        }

        try {
            String[] idsArray = tweetIds.toArray(new String[0]);
            redisTemplate.opsForSet().add(RedisKeyConstant.TWEET_IDS_CACHE_KEY, (Object[]) idsArray);
            // 设置过期时间为7天
            redisTemplate.expire(RedisKeyConstant.TWEET_IDS_CACHE_KEY, 
                    RedisKeyConstant.TWEET_IDS_CACHE_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.debug("批量添加推特ID到缓存，数量: {}", tweetIds.size());
        } catch (Exception e) {
            log.error("批量添加推特ID到缓存失败: count={}, error={}", tweetIds.size(), e.getMessage());
        }
    }

    @Override
    public void initializeTweetIdsCache() {
        if (!redisAvailable) {
            return;
        }

        try {
            log.info("开始初始化推特ID缓存...");
            
            // 获取最近7天的推特ID
            LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);
            List<String> recentTweetIds = tweetRepository.findRecentTweetIds(sevenDaysAgo);

            if (!recentTweetIds.isEmpty()) {
                // 清空现有缓存
                redisTemplate.delete(RedisKeyConstant.TWEET_IDS_CACHE_KEY);

                // 批量添加到缓存
                addTweetIdsToCache(recentTweetIds);
                

                log.info("推特ID缓存初始化完成，加载了{}条最近7天的推特ID", recentTweetIds.size());
            } else {
                log.info("没有找到最近7天的推特数据");
            }
            
        } catch (Exception e) {
            log.error("初始化推特ID缓存失败", e);
        }
    }

    @Override
    public void cleanExpiredTweetIdsCache() {
        if (!redisAvailable) {
            return;
        }

        try {
            // Redis会自动清理过期的key，这里可以手动重新初始化缓存
            log.info("清理过期推特ID缓存并重新初始化");
            initializeTweetIdsCache();
        } catch (Exception e) {
            log.error("清理过期推特ID缓存失败", e);
        }
    }

    @Override
    public long getCachedTweetIdsCount() {
        if (!redisAvailable) {
            return 0;
        }

        try {
            Long count = redisTemplate.opsForSet().size(RedisKeyConstant.TWEET_IDS_CACHE_KEY);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("获取缓存推特ID数量失败", e);
            return 0;
        }
    }
}
