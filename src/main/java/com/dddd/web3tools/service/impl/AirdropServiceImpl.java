package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.dto.ProjectDTO;
import com.dddd.web3tools.dto.ProjectStatsDTO;
import com.dddd.web3tools.dto.WalletDTO;
import com.dddd.web3tools.entity.Project;
import com.dddd.web3tools.entity.TableColumn;
import com.dddd.web3tools.entity.Wallet;
import com.dddd.web3tools.repository.ProjectRepository;
import com.dddd.web3tools.repository.TableColumnRepository;
import com.dddd.web3tools.repository.WalletRepository;
import com.dddd.web3tools.service.AirdropService;
import com.dddd.web3tools.util.WalletValidator;
import com.dddd.web3tools.vo.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 空投项目管理服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class AirdropServiceImpl implements AirdropService {

    private final ProjectRepository projectRepository;
    private final WalletRepository walletRepository;
    private final TableColumnRepository tableColumnRepository;
    private final WalletValidator walletValidator;

    @Value("${airdrop.admin.address:******************************************}")
    private String adminAddress;

    // ==================== 项目管理接口实现 ====================

    @Override
    @Transactional(readOnly = true)
    public PageResponse<ProjectDTO> getProjects(ProjectDTO.QueryRequest request) {
        log.info("获取项目列表，用户地址: {}, 页码: {}, 每页数量: {}", 
                request.getUserAddress(), request.getPage(), request.getLimit());

        Pageable pageable = PageRequest.of(
                request.getPage() - 1, 
                request.getLimit(), 
                Sort.by(Sort.Direction.DESC, "createdAt")
        );

        Page<Project> projectPage;
        Project.ProjectStatus status = request.getStatus() != null ? 
                Project.ProjectStatus.valueOf(request.getStatus()) : null;

        if (request.getChain() != null && status != null) {
            projectPage = projectRepository.findByCreatedByAndStatusAndChain(
                    request.getUserAddress(), status, request.getChain(), pageable);
        } else if (request.getChain() != null) {
            projectPage = projectRepository.findByCreatedByAndChain(
                    request.getUserAddress(), request.getChain(), pageable);
        } else if (status != null) {
            projectPage = projectRepository.findByCreatedByAndStatus(
                    request.getUserAddress(), status, pageable);
        } else {
            projectPage = projectRepository.findByCreatedBy(request.getUserAddress(), pageable);
        }

        List<ProjectDTO> projectDTOs = projectPage.getContent().stream()
                .map(this::convertToProjectDTO)
                .collect(Collectors.toList());

        return PageResponse.<ProjectDTO>builder()
                .content(projectDTOs)
                .page(request.getPage())
                .size(request.getLimit())
                .totalElements(projectPage.getTotalElements())
                .totalPages(projectPage.getTotalPages())
                .build();
    }

    @Override
    public ProjectDTO createProject(ProjectDTO.CreateRequest request) {
        log.info("创建项目: {}, 创建者: {}", request.getName(), request.getCreatedBy());

        // 验证项目名称是否已存在
        if (projectRepository.existsByCreatedByAndNameIgnoreCase(request.getCreatedBy(), request.getName())) {
            throw new IllegalArgumentException("项目名称已存在");
        }

        // 验证日期
        if (request.getEndDate().isBefore(request.getStartDate())) {
            throw new IllegalArgumentException("结束日期不能早于开始日期");
        }

        Project project = Project.builder()
                .name(request.getName())
                .description(request.getDescription())
                .chain(request.getChain())
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .status(Project.ProjectStatus.valueOf(request.getStatus()))
                .createdBy(request.getCreatedBy())
                .build();

        Project savedProject = projectRepository.save(project);
        
        // 创建默认表格列配置
        createDefaultTableColumns(savedProject.getId());

        log.info("项目创建成功，ID: {}", savedProject.getId());
        return convertToProjectDTO(savedProject);
    }

    @Override
    public ProjectDTO updateProject(Long projectId, ProjectDTO.UpdateRequest request) {
        log.info("更新项目: {}", projectId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new IllegalArgumentException("项目不存在"));

        // 更新字段
        if (request.getName() != null) {
            project.setName(request.getName());
        }
        if (request.getDescription() != null) {
            project.setDescription(request.getDescription());
        }
        if (request.getChain() != null) {
            project.setChain(request.getChain());
        }
        if (request.getStartDate() != null) {
            project.setStartDate(request.getStartDate());
        }
        if (request.getEndDate() != null) {
            project.setEndDate(request.getEndDate());
        }
        if (request.getStatus() != null) {
            project.setStatus(Project.ProjectStatus.valueOf(request.getStatus()));
        }

        // 验证日期
        if (project.getEndDate().isBefore(project.getStartDate())) {
            throw new IllegalArgumentException("结束日期不能早于开始日期");
        }

        Project updatedProject = projectRepository.save(project);
        log.info("项目更新成功，ID: {}", projectId);
        return convertToProjectDTO(updatedProject);
    }

    @Override
    public void deleteProject(Long projectId) {
        log.info("删除项目: {}", projectId);

        if (!projectRepository.existsById(projectId)) {
            throw new IllegalArgumentException("项目不存在");
        }

        projectRepository.deleteById(projectId);
        log.info("项目删除成功，ID: {}", projectId);
    }

    @Override
    @Transactional(readOnly = true)
    public ProjectDTO getProjectById(Long projectId) {
        log.info("获取项目详情: {}", projectId);

        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new IllegalArgumentException("项目不存在"));

        return convertToProjectDTO(project);
    }

    // ==================== 钱包管理接口实现 ====================

    @Override
    public WalletDTO addWalletToProject(Long projectId, WalletDTO.AddRequest request) {
        log.info("添加钱包到项目: {}, 钱包地址: {}", projectId, request.getAddress());

        // 验证项目是否存在
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new IllegalArgumentException("项目不存在"));

        // 验证钱包地址格式
        if (!walletValidator.isValidAddress(request.getAddress())) {
            throw new IllegalArgumentException("无效的钱包地址格式");
        }

        // 检查钱包是否已存在于项目中
        if (walletRepository.existsByProject_IdAndAddress(projectId, request.getAddress())) {
            throw new IllegalArgumentException("钱包已存在于项目中");
        }

        Wallet wallet = Wallet.builder()
                .project(project)
                .address(request.getAddress())
                .name(request.getName())
                .notes(request.getNotes())
                .addedBy(request.getAddedBy())
                .build();

        Wallet savedWallet = walletRepository.save(wallet);

        // 更新项目钱包数量
        updateProjectWalletCount(projectId);

        log.info("钱包添加成功，ID: {}", savedWallet.getId());
        return convertToWalletDTO(savedWallet);
    }

    @Override
    public WalletDTO.BatchImportResponse batchImportWallets(Long projectId, WalletDTO.BatchImportRequest request) {
        log.info("批量导入钱包到项目: {}, 数量: {}", projectId, request.getWallets().size());

        // 验证项目是否存在
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new IllegalArgumentException("项目不存在"));

        List<WalletDTO> successWallets = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        int imported = 0;
        int failed = 0;

        for (WalletDTO.AddRequest walletRequest : request.getWallets()) {
            try {
                // 验证钱包地址格式
                if (!walletValidator.isValidAddress(walletRequest.getAddress())) {
                    errors.add("无效的钱包地址格式: " + walletRequest.getAddress());
                    failed++;
                    continue;
                }

                // 检查钱包是否已存在于项目中
                if (walletRepository.existsByProject_IdAndAddress(projectId, walletRequest.getAddress())) {
                    errors.add("钱包已存在于项目中: " + walletRequest.getAddress());
                    failed++;
                    continue;
                }

                Wallet wallet = Wallet.builder()
                        .project(project)
                        .address(walletRequest.getAddress())
                        .name(walletRequest.getName())
                        .notes(walletRequest.getNotes())
                        .addedBy(walletRequest.getAddedBy())
                        .build();

                Wallet savedWallet = walletRepository.save(wallet);
                successWallets.add(convertToWalletDTO(savedWallet));
                imported++;

            } catch (Exception e) {
                log.error("导入钱包失败: {}", walletRequest.getAddress(), e);
                errors.add("导入失败: " + walletRequest.getAddress() + " - " + e.getMessage());
                failed++;
            }
        }

        // 更新项目钱包数量
        updateProjectWalletCount(projectId);

        log.info("批量导入完成，成功: {}, 失败: {}", imported, failed);
        return WalletDTO.BatchImportResponse.builder()
                .wallets(successWallets)
                .imported(imported)
                .failed(failed)
                .errors(errors)
                .build();
    }

    // ==================== 辅助方法 ====================

    private ProjectDTO convertToProjectDTO(Project project) {
        return ProjectDTO.fromEntity(project);
    }

    private WalletDTO convertToWalletDTO(Wallet wallet) {
        return WalletDTO.fromEntity(wallet);
    }

    private void updateProjectWalletCount(Long projectId) {
        long walletCount = walletRepository.countByProject_Id(projectId);
        projectRepository.findById(projectId).ifPresent(project -> {
            project.setWalletCount((int) walletCount);
            projectRepository.save(project);
        });
    }

    private void createDefaultTableColumns(Long projectId) {
        // 获取默认列配置模板
        List<TableColumn> defaultColumns = List.of(
                createTableColumn(projectId, "name", "钱包名称", "150px", true, true, 1),
                createTableColumn(projectId, "address", "地址", "200px", false, true, 2),
                createTableColumn(projectId, "balance", "余额", "100px", false, true, 3),
                createTableColumn(projectId, "lastActivity", "最后活动", "120px", false, true, 4),
                createTableColumn(projectId, "notes", "备注", "200px", true, true, 5),
                createTableColumn(projectId, "addedBy", "添加者", "150px", false, true, 6),
                createTableColumn(projectId, "createdAt", "创建时间", "150px", false, true, 7)
        );

        tableColumnRepository.saveAll(defaultColumns);
    }

    private TableColumn createTableColumn(Long projectId, String key, String label, String width,
                                        boolean editable, boolean visible, int sortOrder) {
        return TableColumn.builder()
                .project(Project.builder().id(projectId).build())
                .columnKey(key)
                .label(label)
                .width(width)
                .editable(editable)
                .visible(visible)
                .sortOrder(sortOrder)
                .build();
    }

    // ==================== 钱包管理接口实现（续） ====================

    @Override
    public WalletDTO updateWallet(Long projectId, Long walletId, WalletDTO.UpdateRequest request) {
        log.info("更新钱包信息: 项目ID={}, 钱包ID={}", projectId, walletId);

        Wallet wallet = walletRepository.findByIdAndProject_Id(walletId, projectId)
                .orElseThrow(() -> new IllegalArgumentException("钱包不存在或不属于该项目"));

        // 更新字段
        if (request.getName() != null) {
            wallet.setName(request.getName());
        }
        if (request.getNotes() != null) {
            wallet.setNotes(request.getNotes());
        }

        Wallet updatedWallet = walletRepository.save(wallet);
        log.info("钱包更新成功，ID: {}", walletId);
        return convertToWalletDTO(updatedWallet);
    }

    @Override
    public void deleteWallet(Long projectId, Long walletId) {
        log.info("删除钱包: 项目ID={}, 钱包ID={}", projectId, walletId);

        Wallet wallet = walletRepository.findByIdAndProject_Id(walletId, projectId)
                .orElseThrow(() -> new IllegalArgumentException("钱包不存在或不属于该项目"));

        walletRepository.delete(wallet);

        // 更新项目钱包数量
        updateProjectWalletCount(projectId);

        log.info("钱包删除成功，ID: {}", walletId);
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<WalletDTO> getProjectWallets(Long projectId, String addedBy, Integer page, Integer limit) {
        log.info("获取项目钱包列表: 项目ID={}, 添加者={}, 页码={}, 每页数量={}", projectId, addedBy, page, limit);

        // 验证项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new IllegalArgumentException("项目不存在");
        }

        Pageable pageable = PageRequest.of(
                page - 1,
                limit,
                Sort.by(Sort.Direction.DESC, "createdAt")
        );

        // 根据项目ID和添加者查询钱包
        Page<Wallet> walletPage = walletRepository.findByProject_IdAndAddedBy(projectId, addedBy, pageable);

        List<WalletDTO> walletDTOs = walletPage.getContent().stream()
                .map(this::convertToWalletDTO)
                .collect(Collectors.toList());

        return PageResponse.<WalletDTO>builder()
                .content(walletDTOs)
                .page(page)
                .size(limit)
                .totalElements(walletPage.getTotalElements())
                .totalPages(walletPage.getTotalPages())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public PageResponse<WalletDTO> getAllProjectWallets(Long projectId, Integer page, Integer limit) {
        log.info("获取项目所有钱包列表: 项目ID={}, 页码={}, 每页数量={}", projectId, page, limit);

        // 验证项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new IllegalArgumentException("项目不存在");
        }

        Pageable pageable = PageRequest.of(
                page - 1,
                limit,
                Sort.by(Sort.Direction.DESC, "createdAt")
        );

        // 查询项目中的所有钱包（不按添加者过滤）
        Page<Wallet> walletPage = walletRepository.findByProject_Id(projectId, pageable);

        List<WalletDTO> walletDTOs = walletPage.getContent().stream()
                .map(this::convertToWalletDTO)
                .collect(Collectors.toList());

        return PageResponse.<WalletDTO>builder()
                .content(walletDTOs)
                .page(page)
                .size(limit)
                .totalElements(walletPage.getTotalElements())
                .totalPages(walletPage.getTotalPages())
                .build();
    }

    // ==================== 数据统计接口实现 ====================

    @Override
    @Transactional(readOnly = true)
    public ProjectStatsDTO getProjectStats(Long projectId) {
        log.info("获取项目统计信息: 项目ID={}", projectId);

        // 验证项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new IllegalArgumentException("项目不存在");
        }

        // 获取基础统计数据
        long walletCount = walletRepository.countByProject_Id(projectId);
        BigDecimal totalValue = walletRepository.sumBalanceByProjectId(projectId);
        if (totalValue == null) {
            totalValue = BigDecimal.ZERO;
        }

        // 获取活跃度统计
        LocalDate now = LocalDate.now();
        Object[] activityData = walletRepository.getActivityStats(
                projectId,
                now.minusDays(7),
                now.minusDays(30),
                now.minusDays(90)
        );

        ProjectStatsDTO.ActivityStats activityStats = ProjectStatsDTO.ActivityStats.builder()
                .last7Days(activityData[0] != null ? ((Number) activityData[0]).intValue() : 0)
                .last30Days(activityData[1] != null ? ((Number) activityData[1]).intValue() : 0)
                .last90Days(activityData[2] != null ? ((Number) activityData[2]).intValue() : 0)
                .build();

        // 获取有余额和无余额的钱包数量
        long activeWallets = walletRepository.countWalletsWithBalance(projectId);
        long inactiveWallets = walletRepository.countWalletsWithoutBalance(projectId);

        // 计算平均余额
        BigDecimal averageBalance = BigDecimal.ZERO;
        if (walletCount > 0) {
            averageBalance = totalValue.divide(BigDecimal.valueOf(walletCount), 2, RoundingMode.HALF_UP);
        }

        // 获取顶级钱包
        List<Wallet> topWallets = walletRepository.findTop10ByProject_IdOrderByBalanceDesc(projectId);
        final BigDecimal finalTotalValue = totalValue; // 创建final变量
        List<ProjectStatsDTO.TopWallet> topWalletList = topWallets.stream()
                .map(wallet -> {
                    String percentage = "0";
                    if (finalTotalValue.compareTo(BigDecimal.ZERO) > 0) {
                        percentage = wallet.getBalance()
                                .divide(finalTotalValue, 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100))
                                .setScale(1, RoundingMode.HALF_UP)
                                .toString();
                    }
                    return ProjectStatsDTO.TopWallet.builder()
                            .address(wallet.getAddress())
                            .balance(wallet.getBalance().toString())
                            .percentage(percentage)
                            .build();
                })
                .collect(Collectors.toList());

        return ProjectStatsDTO.builder()
                .projectId(projectId)
                .walletCount((int) walletCount)
                .totalValue(ProjectStatsDTO.TotalValue.builder()
                        .usd(totalValue.toString())
                        .nativeToken(totalValue.toString())
                        .build())
                .activeWallets((int) activeWallets)
                .inactiveWallets((int) inactiveWallets)
                .averageBalance(averageBalance.toString())
                .topWallets(topWalletList)
                .activityStats(activityStats)
                .updatedAt(LocalDateTime.now())
                .build();
    }

    // ==================== 工具接口实现 ====================

    @Override
    @Transactional(readOnly = true)
    public WalletDTO.ValidationResponse validateWalletAddress(WalletDTO.ValidationRequest request) {
        log.info("验证钱包地址: 地址={}, 链={}", request.getAddress(), request.getChain());

        boolean isValid = walletValidator.isValidAddress(request.getAddress());
        String checksumAddress = isValid ? walletValidator.toChecksumAddress(request.getAddress()) : request.getAddress();

        return WalletDTO.ValidationResponse.builder()
                .address(request.getAddress())
                .isValid(isValid)
                .format(request.getChain())
                .checksumAddress(checksumAddress)
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public Resource exportProjectData(Long projectId, String format) {
        log.info("导出项目数据: 项目ID={}, 格式={}", projectId, format);

        // 验证项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new IllegalArgumentException("项目不存在");
        }

        // 这里应该实现具体的导出逻辑
        // 暂时抛出未实现异常
        throw new UnsupportedOperationException("导出功能暂未实现");
    }

    @Override
    @Transactional(readOnly = true)
    public List<ProjectStatsDTO.TableColumnDTO> getTableColumns(Long projectId) {
        log.info("获取表格列配置: 项目ID={}", projectId);

        // 验证项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new IllegalArgumentException("项目不存在");
        }

        List<TableColumn> columns = tableColumnRepository.findByProject_IdOrderBySortOrder(projectId);

        return columns.stream()
                .map(column -> ProjectStatsDTO.TableColumnDTO.builder()
                        .key(column.getColumnKey())
                        .label(column.getLabel())
                        .width(column.getWidth())
                        .editable(column.getEditable())
                        .visible(column.getVisible())
                        .sortOrder(column.getSortOrder())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<ProjectStatsDTO.TableColumnDTO> updateTableColumns(Long projectId, List<ProjectStatsDTO.TableColumnDTO> columns) {
        log.info("更新表格列配置: 项目ID={}, 列数量={}", projectId, columns.size());

        // 验证项目是否存在
        if (!projectRepository.existsById(projectId)) {
            throw new IllegalArgumentException("项目不存在");
        }

        // 删除现有配置
        tableColumnRepository.deleteByProject_Id(projectId);

        // 创建新配置
        List<TableColumn> newColumns = columns.stream()
                .map(dto -> TableColumn.builder()
                        .project(Project.builder().id(projectId).build())
                        .columnKey(dto.getKey())
                        .label(dto.getLabel())
                        .width(dto.getWidth())
                        .editable(dto.getEditable())
                        .visible(dto.getVisible())
                        .sortOrder(dto.getSortOrder())
                        .build())
                .collect(Collectors.toList());

        List<TableColumn> savedColumns = tableColumnRepository.saveAll(newColumns);

        return savedColumns.stream()
                .map(column -> ProjectStatsDTO.TableColumnDTO.builder()
                        .key(column.getColumnKey())
                        .label(column.getLabel())
                        .width(column.getWidth())
                        .editable(column.getEditable())
                        .visible(column.getVisible())
                        .sortOrder(column.getSortOrder())
                        .build())
                .collect(Collectors.toList());
    }

    // ==================== 区块链数据同步接口实现 ====================

    @Override
    public void syncWalletBalance(Long projectId, String walletAddress) {
        log.info("同步钱包余额: 项目ID={}, 钱包地址={}", projectId, walletAddress);

        Wallet wallet = walletRepository.findByProject_IdAndAddress(projectId, walletAddress)
                .orElseThrow(() -> new IllegalArgumentException("钱包不存在于该项目中"));

        // 这里应该调用区块链API获取最新余额
        // 暂时使用模拟数据
        BigDecimal newBalance = new BigDecimal("1.5");
        wallet.setBalance(newBalance);
        wallet.setLastActivity(LocalDate.now());

        walletRepository.save(wallet);

        // 更新项目总价值
        updateProjectTotalValue(projectId);

        log.info("钱包余额同步完成: {}", walletAddress);
    }

    @Override
    public void syncAllWalletBalances(Long projectId) {
        log.info("批量同步项目钱包余额: 项目ID={}", projectId);

        List<Wallet> wallets = walletRepository.findByProject_Id(projectId);

        for (Wallet wallet : wallets) {
            try {
                // 这里应该调用区块链API获取最新余额
                // 暂时使用模拟数据
                BigDecimal newBalance = new BigDecimal("1.5");
                wallet.setBalance(newBalance);
                wallet.setLastActivity(LocalDate.now());

                walletRepository.save(wallet);

                // 添加延迟避免API限制
                Thread.sleep(100);

            } catch (Exception e) {
                log.error("同步钱包余额失败: {}", wallet.getAddress(), e);
            }
        }

        // 更新项目总价值
        updateProjectTotalValue(projectId);

        log.info("批量同步完成: 项目ID={}", projectId);
    }

    @Override
    public void updateWalletLastActivity(String walletAddress, String chain) {
        log.info("更新钱包最后活动时间: 地址={}, 链={}", walletAddress, chain);

        List<Wallet> wallets = walletRepository.findByAddress(walletAddress);

        for (Wallet wallet : wallets) {
            wallet.setLastActivity(LocalDate.now());
            walletRepository.save(wallet);
        }

        log.info("钱包活动时间更新完成: {}", walletAddress);
    }

    // ==================== 权限验证接口实现 ====================

    @Override
    @Transactional(readOnly = true)
    public boolean hasProjectAccess(Long projectId, String userAddress) {
        log.debug("验证项目访问权限: 项目ID={}, 用户地址={}", projectId, userAddress);

        // 管理员有所有权限
        if (isAdmin(userAddress)) {
            return true;
        }

        // 检查是否是项目创建者
        return projectRepository.findByIdAndCreatedBy(projectId, userAddress).isPresent();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasProjectEditAccess(Long projectId, String userAddress) {
        log.debug("验证项目编辑权限: 项目ID={}, 用户地址={}", projectId, userAddress);

        // 管理员有所有权限
        if (isAdmin(userAddress)) {
            return true;
        }

        // 检查是否是项目创建者
        return projectRepository.findByIdAndCreatedBy(projectId, userAddress).isPresent();
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isAdmin(String userAddress) {
        return adminAddress.equalsIgnoreCase(userAddress);
    }

    // ==================== 私有辅助方法 ====================

    private void updateProjectTotalValue(Long projectId) {
        BigDecimal totalValue = walletRepository.sumBalanceByProjectId(projectId);
        if (totalValue == null) {
            totalValue = BigDecimal.ZERO;
        }
        final BigDecimal finalTotalValue = totalValue;
        projectRepository.findById(projectId).ifPresent(project -> {
            project.setTotalValue(finalTotalValue);
            projectRepository.save(project);
        });
    }
}
