package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.entity.Tweet;
import com.dddd.web3tools.entity.TwitterUser;
import com.dddd.web3tools.repository.TweetRepository;
import com.dddd.web3tools.repository.TwitterUserRepository;
import com.dddd.web3tools.service.TwitterUserService;
import com.dddd.web3tools.util.TwitterAPIUtil;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Locale;

@Service
@Slf4j
public class TwitterUserServiceImpl implements TwitterUserService {

    @Autowired
    private TwitterUserRepository twitterUserRepository;

    @Autowired
    private TweetRepository tweetRepository;

    @Autowired
    private TwitterAPIUtil twitterAPIUtil;

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss Z yyyy", Locale.ENGLISH);

    public void importTwitterUsersAndTweets(String listId){

        JsonNode usersResponse = null;
        try {
            usersResponse = twitterAPIUtil.getTwitterListUsers(listId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        processUsers(usersResponse);
    }

    private void processUsers(JsonNode rootNode) {
        if (rootNode.has("members")) {
            for (JsonNode userNode : rootNode.get("members")) {
                try {
                    TwitterUser user = saveTwitterUser(userNode);
                    if (user != null) {
                        processUserTweets(user.getScreenName());
                    }
                } catch (Exception e) {
                    log.error("处理用户失败: {}", userNode, e);
                }
            }
        }
    }

    private TwitterUser saveTwitterUser(JsonNode userNode) {
        String userId = userNode.get("user_id").asText();
        if (!twitterUserRepository.existsByUserId(userId)) {
            TwitterUser user = new TwitterUser();
            user.setUserId(userId);
            user.setScreenName(userNode.get("screen_name").asText());
            user.setTwitterName(userNode.get("name").asText());
            user.setFollowers(userNode.get("followers_count").asInt());
            user.setDescription(userNode.get("description").asText());
            user.setCreatedAt(userNode.get("created_at").asText());
            user.setWebsite(userNode.get("website").asText());
            user.setType(1);
            return twitterUserRepository.save(user);
        }
        return null;
    }

    private void processUserTweets(String username) {
        try {
            JsonNode tweetsResponse = twitterAPIUtil.getUserTweets(username);
            saveTweets(tweetsResponse);
        } catch (Exception e) {
            log.error("获取用户{}的推文失败", username, e);
        }
    }

    private void saveTweets(JsonNode rootNode) {
        if (rootNode.has("timeline")) {
            for (JsonNode tweetNode : rootNode.get("timeline")) {
                try {
                    String tweetId = tweetNode.get("tweet_id").asText();
                    if (!tweetRepository.existsById(tweetId)) {
                        Tweet tweet = new Tweet();
                        tweet.setId(tweetId);
                        tweet.setContent(tweetNode.get("text").asText());
                        tweet.setAuthor(tweetNode.get("author").get("name").asText());
                        tweet.setScreenName(tweetNode.get("author").get("screen_name").asText());
                        tweet.setCreatedAt(
                                ZonedDateTime.parse(tweetNode.get("created_at").asText(), formatter)
                                        .withZoneSameInstant(ZoneId.of("Asia/Shanghai"))
                                        .toLocalDateTime()
                        );
                        tweet.setType(1);
                        tweetRepository.save(tweet);
                    }
                } catch (Exception e) {
                    log.error("保存推文失败", e);
                }
            }
        }
    }

    public TwitterUser updateUser(TwitterUser updateRequest) {
        return twitterUserRepository.findById(updateRequest.getId())
            .map(user -> {
                if (updateRequest.getRemark() != null) {
                    user.setRemark(updateRequest.getRemark());
                }

                return twitterUserRepository.save(user);
            })
            .orElseThrow(() -> new RuntimeException("用户ID不存在: " + updateRequest.getId()));
    }

    public List<Tweet> getTweetsByScreenName(String screenName) {
        return tweetRepository.findByScreenNameAndTypeOrderByCreatedAtDesc(screenName, 1);
    }

    public Page<TwitterUser> searchUsers(String twitterName, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        if (twitterName != null && !twitterName.isEmpty()) {
            return twitterUserRepository.findBytwitterNameContaining(twitterName, pageable);
        }
        return twitterUserRepository.findAll(pageable);
    }

    public void importUserAndTweetsByUsername(String username) throws Exception {
        // 通过username获取用户信息
        JsonNode userResponse = twitterAPIUtil.getUserByUsername(username);

        // 处理用户信息
        TwitterUser user = saveTwitterUserInUserInfo(userResponse,username);
        if (user != null) {
            // 获取并保存用户推文
            JsonNode tweetsResponse = twitterAPIUtil.getUserTweets(username);
            saveTweets(tweetsResponse);
        }
    }

    private TwitterUser saveTwitterUserInUserInfo(JsonNode userNode,String username) {
        String userId = userNode.get("rest_id").asText();
        if (!twitterUserRepository.existsByUserId(userId)) {
            TwitterUser user = new TwitterUser();
            user.setUserId(userId);
            user.setScreenName(username);
            user.setTwitterName(userNode.get("name").asText());
            user.setFollowers(userNode.get("sub_count").asInt());
            user.setDescription(userNode.get("desc").asText());
            user.setCreatedAt(userNode.get("created_at").asText());
            user.setType(1);
            return twitterUserRepository.save(user);
        }
        return null;
    }

}
