package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.dto.*;
import com.dddd.web3tools.entity.FollowTask;
import com.dddd.web3tools.entity.SmartWallet;
import com.dddd.web3tools.repository.FollowTaskRepository;
import com.dddd.web3tools.repository.SmartWalletRepository;
import com.dddd.web3tools.service.SmartWalletTrackerService;
import com.dddd.web3tools.service.SolanaBlockchainService;
import com.dddd.web3tools.util.CryptoUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 聪明钱包追踪服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SmartWalletTrackerServiceImpl implements SmartWalletTrackerService {

    private final SmartWalletRepository smartWalletRepository;
    private final FollowTaskRepository followTaskRepository;
    private final SolanaBlockchainService solanaBlockchainService;
    private final CryptoUtil cryptoUtil;
    private final ObjectMapper objectMapper;

    @Override
    public SmartWalletDTO.ListResponse getSmartWallets(SmartWalletDTO.QueryRequest request) {
        log.info("获取聪明钱包列表: {}", request);

        try {
            // 构建排序
            Sort sort = buildSort(request.getSortBy(), request.getOrder());
            Pageable pageable = PageRequest.of(request.getPage() - 1, request.getLimit(), sort);

            // 构建查询条件
            String tagsFilter = request.getTags() != null && !request.getTags().isEmpty() 
                ? String.join(",", request.getTags()) : null;

            Page<SmartWallet> walletPage = smartWalletRepository.findByStatusAndTags(
                request.getStatus(), tagsFilter, pageable);

            List<SmartWalletDTO> walletDTOs = walletPage.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());

            return SmartWalletDTO.ListResponse.builder()
                .wallets(walletDTOs)
                .total(walletPage.getTotalElements())
                .page(request.getPage())
                .totalPages(walletPage.getTotalPages())
                .build();

        } catch (Exception e) {
            log.error("获取聪明钱包列表失败", e);
            throw new RuntimeException("获取聪明钱包列表失败: " + e.getMessage());
        }
    }

    @Override
    public SmartWalletDTO getSmartWalletDetail(String walletId) {
        log.info("获取聪明钱包详情: {}", walletId);

        try {
            SmartWallet wallet = smartWalletRepository.findById(Long.valueOf(walletId))
                .orElseThrow(() -> new RuntimeException("指定的钱包不存在"));

            SmartWalletDTO dto = convertToDTO(wallet);
            
            // 添加详细统计信息
            SmartWalletDTO.Stats stats = SmartWalletDTO.Stats.builder()
                .totalTrades(wallet.getTotalTrades())
                .profitableTrades(wallet.getProfitableTrades())
                .totalVolume(wallet.getTotalVolume())
                .avgProfit(wallet.getAvgProfit())
                .maxProfit(wallet.getMaxProfit())
                .maxLoss(wallet.getMaxLoss())
                .bestWinStreak(wallet.getBestWinStreak())
                .currentStreak(wallet.getCurrentStreak())
                .build();
            
            dto.setStats(stats);
            return dto;

        } catch (Exception e) {
            log.error("获取聪明钱包详情失败: walletId={}", walletId, e);
            throw new RuntimeException("获取聪明钱包详情失败: " + e.getMessage());
        }
    }

    @Override
    public TransactionDTO.ListResponse getWalletTransactions(String walletId, 
                                                           TransactionDTO.QueryRequest request) {
        log.info("获取钱包交易记录: walletId={}, request={}", walletId, request);

        try {
            SmartWallet wallet = smartWalletRepository.findById(Long.valueOf(walletId))
                .orElseThrow(() -> new RuntimeException("指定的钱包不存在"));

            // 从链上查询交易记录
            List<TransactionDTO> transactions = queryTransactionsFromBlockchain(wallet.getAddress(), request);
            
            // 计算汇总信息
            TransactionDTO.Summary summary = calculateTransactionSummary(transactions);

            // 分页处理
            int start = (request.getPage() - 1) * request.getLimit();
            int end = Math.min(start + request.getLimit(), transactions.size());
            List<TransactionDTO> pagedTransactions = transactions.subList(start, end);

            return TransactionDTO.ListResponse.builder()
                .transactions(pagedTransactions)
                .total((long) transactions.size())
                .page(request.getPage())
                .totalPages((int) Math.ceil((double) transactions.size() / request.getLimit()))
                .summary(summary)
                .build();

        } catch (Exception e) {
            log.error("获取钱包交易记录失败: walletId={}", walletId, e);
            throw new RuntimeException("获取钱包交易记录失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FollowTaskDTO createFollowTask(FollowTaskDTO.CreateRequest request) {
        log.info("创建跟单任务: {}", request);

        try {
            // 验证聪明钱包是否存在
            SmartWallet smartWallet = smartWalletRepository.findById(Long.valueOf(request.getSmartWalletId()))
                .orElseThrow(() -> new RuntimeException("指定的聪明钱包不存在"));

            // 检查是否已经存在活跃的跟单任务
            boolean exists = followTaskRepository.existsByUserWalletAndSmartWalletIdAndStatusIn(
                request.getWallet(), 
                Long.valueOf(request.getSmartWalletId()),
                Arrays.asList("active", "paused")
            );
            
            if (exists) {
                throw new RuntimeException("该聪明钱包已存在活跃的跟单任务");
            }

            // 生成任务ID
            String taskId = "task_" + System.currentTimeMillis();

            // 转换配置为JSON
            String configJson;
            try {
                configJson = objectMapper.writeValueAsString(request.getConfig());
            } catch (JsonProcessingException e) {
                throw new RuntimeException("跟单配置格式无效", e);
            }

            // 加密私钥
            String encryptedPrivateKey = cryptoUtil.encryptPrivateKey(request.getWalletPrivateKey());

            // 创建跟单任务
            FollowTask followTask = FollowTask.builder()
                .taskId(taskId)
                .userWallet(request.getWallet())
                .smartWalletId(Long.valueOf(request.getSmartWalletId()))
                .walletPrivateKey(encryptedPrivateKey)
                .config(configJson)
                .status("active")
                .totalProfit(0L)
                .totalLoss(0L)
                .netProfit(0L)
                .profitPercent(BigDecimal.ZERO)
                .followCount(0)
                .successRate(BigDecimal.ZERO)
                .totalInvested(0L)
                .build();

            followTask = followTaskRepository.save(followTask);

            return FollowTaskDTO.builder()
                .taskId(followTask.getTaskId())
                .smartWalletId(request.getSmartWalletId())
                .smartWalletAddress(smartWallet.getAddress())
                .smartWalletNickname(smartWallet.getNickname())
                .status(followTask.getStatus())
                .createdAt(followTask.getCreatedAt())
                .config(request.getConfig())
                .build();

        } catch (Exception e) {
            log.error("创建跟单任务失败", e);
            throw new RuntimeException("创建跟单任务失败: " + e.getMessage());
        }
    }

    @Override
    public FollowTaskDTO.ListResponse getFollowTasks(FollowTaskDTO.QueryRequest request) {
        log.info("获取跟单任务列表: {}", request);

        try {
            Pageable pageable = PageRequest.of(request.getPage() - 1, request.getLimit(), 
                Sort.by(Sort.Direction.DESC, "createdAt"));

            Page<FollowTask> taskPage = followTaskRepository.findByUserWalletAndStatus(
                request.getWallet(), request.getStatus(), pageable);

            List<FollowTaskDTO> taskDTOs = taskPage.getContent().stream()
                .map(this::convertToFollowTaskDTO)
                .collect(Collectors.toList());

            return FollowTaskDTO.ListResponse.builder()
                .tasks(taskDTOs)
                .total(taskPage.getTotalElements())
                .page(request.getPage())
                .totalPages(taskPage.getTotalPages())
                .build();

        } catch (Exception e) {
            log.error("获取跟单任务列表失败", e);
            throw new RuntimeException("获取跟单任务列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public FollowTaskDTO updateFollowTaskStatus(FollowTaskDTO.StatusUpdateRequest request) {
        log.info("更新跟单任务状态: {}", request);

        try {
            FollowTask task = followTaskRepository.findByUserWalletAndTaskId(
                request.getWallet(), request.getTaskId())
                .orElseThrow(() -> new RuntimeException("指定的跟单任务不存在"));

            task.setStatus(request.getStatus());
            task = followTaskRepository.save(task);

            return FollowTaskDTO.builder()
                .taskId(task.getTaskId())
                .status(task.getStatus())
                .updatedAt(task.getUpdatedAt())
                .build();

        } catch (Exception e) {
            log.error("更新跟单任务状态失败: taskId={}", request.getTaskId(), e);
            throw new RuntimeException("更新跟单任务状态失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void deleteFollowTask(FollowTaskDTO.DeleteRequest request) {
        log.info("删除跟单任务: {}", request);

        try {
            FollowTask task = followTaskRepository.findByUserWalletAndTaskId(
                request.getWallet(), request.getTaskId())
                .orElseThrow(() -> new RuntimeException("指定的跟单任务不存在"));

            followTaskRepository.delete(task);

        } catch (Exception e) {
            log.error("删除跟单任务失败: taskId={}", request.getTaskId(), e);
            throw new RuntimeException("删除跟单任务失败: " + e.getMessage());
        }
    }

    @Override
    public TransactionDTO.TaskDetailResponse getFollowTaskDetail(String wallet, String taskId) {
        log.info("获取跟单任务详情: wallet={}, taskId={}", wallet, taskId);

        try {
            FollowTask task = followTaskRepository.findByUserWalletAndTaskId(wallet, taskId)
                .orElseThrow(() -> new RuntimeException("指定的跟单任务不存在"));

            FollowTaskDTO taskDTO = convertToFollowTaskDTO(task);
            
            // 查询跟单交易记录
            List<TransactionDTO.FollowTransaction> transactions = queryFollowTransactionsFromBlockchain(task);

            return TransactionDTO.TaskDetailResponse.builder()
                .task(taskDTO)
                .transactions(transactions)
                .build();

        } catch (Exception e) {
            log.error("获取跟单任务详情失败: taskId={}", taskId, e);
            throw new RuntimeException("获取跟单任务详情失败: " + e.getMessage());
        }
    }

    @Override
    public PlatformStatsDTO getPlatformStats() {
        log.info("获取平台统计数据");

        try {
            Long totalWallets = smartWalletRepository.countTotalWallets();
            Long activeWallets = smartWalletRepository.countActiveWallets();
            Long totalFollowers = smartWalletRepository.getTotalFollowers();
            Long totalFollowTasks = followTaskRepository.countTotalTasks();
            Long activeFollowTasks = followTaskRepository.countActiveTasks();
            Double avgWinRate = smartWalletRepository.getAverageWinRate();
            Long totalProfit = smartWalletRepository.getTotalProfit();
            Double avgProfitPercent = smartWalletRepository.getAverageProfitPercent();

            // 获取表现最佳的钱包
            List<SmartWallet> topWallets = smartWalletRepository.findTopPerformers(
                PageRequest.of(0, 3));
            List<PlatformStatsDTO.TopPerformer> topPerformers = topWallets.stream()
                .map(wallet -> PlatformStatsDTO.TopPerformer.builder()
                    .walletId(wallet.getId().toString())
                    .nickname(wallet.getNickname())
                    .winRate(wallet.getWinRate())
                    .profitPercent(wallet.getProfitPercent())
                    .build())
                .collect(Collectors.toList());

            return PlatformStatsDTO.builder()
                .totalWallets(totalWallets != null ? totalWallets.intValue() : 0)
                .activeWallets(activeWallets != null ? activeWallets.intValue() : 0)
                .totalFollowers(totalFollowers != null ? totalFollowers.intValue() : 0)
                .totalFollowTasks(totalFollowTasks != null ? totalFollowTasks.intValue() : 0)
                .activeFollowTasks(activeFollowTasks != null ? activeFollowTasks.intValue() : 0)
                .avgWinRate(avgWinRate != null ? BigDecimal.valueOf(avgWinRate) : BigDecimal.ZERO)
                .totalProfit(totalProfit != null ? totalProfit : 0L)
                .avgProfitPercent(avgProfitPercent != null ? BigDecimal.valueOf(avgProfitPercent) : BigDecimal.ZERO)
                .topPerformers(topPerformers)
                .build();

        } catch (Exception e) {
            log.error("获取平台统计数据失败", e);
            throw new RuntimeException("获取平台统计数据失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    private Sort buildSort(String sortBy, String order) {
        Sort.Direction direction = "asc".equalsIgnoreCase(order) ?
            Sort.Direction.ASC : Sort.Direction.DESC;

        return switch (sortBy) {
            case "winRate" -> Sort.by(direction, "winRate");
            case "totalProfit" -> Sort.by(direction, "totalProfit");
            case "followers" -> Sort.by(direction, "followers");
            default -> Sort.by(direction, "profitPercent");
        };
    }

    private SmartWalletDTO convertToDTO(SmartWallet wallet) {
        List<String> tags = parseTagsFromJson(wallet.getTags());

        return SmartWalletDTO.builder()
            .id(wallet.getId().toString())
            .address(wallet.getAddress())
            .nickname(wallet.getNickname())
            .winRate(wallet.getWinRate())
            .totalProfit(wallet.getTotalProfit())
            .profitPercent(wallet.getProfitPercent())
            .avgHoldTime(formatHoldTime(wallet.getAvgHoldTime()))
            .recentTrades(wallet.getRecentTrades())
            .followers(wallet.getFollowers())
            .tags(tags)
            .status(wallet.getStatus())
            .lastActive(wallet.getLastActive())
            .createdAt(wallet.getCreatedAt())
            .updatedAt(wallet.getUpdatedAt())
            .build();
    }

    private FollowTaskDTO convertToFollowTaskDTO(FollowTask task) {
        SmartWallet smartWallet = task.getSmartWallet();

        FollowTaskDTO.Config config = parseConfigFromJson(task.getConfig());

        FollowTaskDTO.Stats stats = FollowTaskDTO.Stats.builder()
            .totalProfit(task.getTotalProfit())
            .totalLoss(task.getTotalLoss())
            .netProfit(task.getNetProfit())
            .profitPercent(task.getProfitPercent())
            .followCount(task.getFollowCount())
            .successRate(task.getSuccessRate())
            .totalInvested(task.getTotalInvested())
            .build();

        return FollowTaskDTO.builder()
            .taskId(task.getTaskId())
            .smartWalletId(task.getSmartWalletId().toString())
            .smartWalletAddress(smartWallet != null ? smartWallet.getAddress() : null)
            .smartWalletNickname(smartWallet != null ? smartWallet.getNickname() : null)
            .status(task.getStatus())
            .createdAt(task.getCreatedAt())
            .updatedAt(task.getUpdatedAt())
            .config(config)
            .stats(stats)
            .build();
    }

    private List<String> parseTagsFromJson(String tagsJson) {
        if (tagsJson == null || tagsJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(tagsJson, new TypeReference<List<String>>() {});
        } catch (JsonProcessingException e) {
            log.warn("解析标签JSON失败: {}", tagsJson, e);
            return new ArrayList<>();
        }
    }

    private FollowTaskDTO.Config parseConfigFromJson(String configJson) {
        if (configJson == null || configJson.trim().isEmpty()) {
            return new FollowTaskDTO.Config();
        }
        try {
            return objectMapper.readValue(configJson, FollowTaskDTO.Config.class);
        } catch (JsonProcessingException e) {
            log.warn("解析配置JSON失败: {}", configJson, e);
            return new FollowTaskDTO.Config();
        }
    }

    private String formatHoldTime(Integer avgHoldTimeHours) {
        if (avgHoldTimeHours == null) {
            return "未知";
        }
        if (avgHoldTimeHours < 24) {
            return avgHoldTimeHours + "小时";
        } else {
            double days = avgHoldTimeHours / 24.0;
            return String.format("%.1f天", days);
        }
    }

    /**
     * 从区块链查询交易记录
     */
    private List<TransactionDTO> queryTransactionsFromBlockchain(String walletAddress,
                                                               TransactionDTO.QueryRequest request) {
        try {
            return solanaBlockchainService.getWalletTransactions(walletAddress, request);
        } catch (Exception e) {
            log.error("从区块链查询交易记录失败: address={}", walletAddress, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询跟单交易记录
     */
    private List<TransactionDTO.FollowTransaction> queryFollowTransactionsFromBlockchain(FollowTask task) {
        // TODO: 实现实际的跟单交易查询逻辑
        // 暂时返回空列表
        return new ArrayList<>();
    }

    private TransactionDTO.Summary calculateTransactionSummary(List<TransactionDTO> transactions) {
        long totalProfit = transactions.stream()
            .filter(tx -> tx.getProfit() != null && tx.getProfit() > 0)
            .mapToLong(TransactionDTO::getProfit)
            .sum();

        long totalLoss = transactions.stream()
            .filter(tx -> tx.getProfit() != null && tx.getProfit() < 0)
            .mapToLong(TransactionDTO::getProfit)
            .sum();

        long netProfit = totalProfit + totalLoss;

        BigDecimal profitPercent = BigDecimal.ZERO;
        if (totalProfit > 0) {
            profitPercent = BigDecimal.valueOf(netProfit)
                .divide(BigDecimal.valueOf(totalProfit), 2, BigDecimal.ROUND_HALF_UP)
                .multiply(BigDecimal.valueOf(100));
        }

        return TransactionDTO.Summary.builder()
            .totalProfit(totalProfit)
            .totalLoss(totalLoss)
            .netProfit(netProfit)
            .profitPercent(profitPercent)
            .build();
    }
}
