package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.dto.UserWalletDTO;
import com.dddd.web3tools.entity.UserWallet;
import com.dddd.web3tools.service.BlockchainQueryService;
import com.dddd.web3tools.service.WalletBalanceCacheService;
import com.dddd.web3tools.config.WalletCacheConfig;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 钱包余额缓存服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WalletBalanceCacheServiceImpl implements WalletBalanceCacheService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final BlockchainQueryService blockchainQueryService;
    private final ObjectMapper objectMapper;
    private final WalletCacheConfig cacheConfig;

    private static final String CACHE_PREFIX = "wallet:balance:";

    @Override
    public UserWalletDTO.WalletInfo getCachedWalletBalance(String chain, String address) {
        try {
            String cacheKey = buildCacheKey(chain, address);
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            
            if (cachedData != null) {
                log.debug("从缓存获取钱包余额: chain={}, address={}", chain, address);
                return objectMapper.readValue(cachedData.toString(), UserWalletDTO.WalletInfo.class);
            }
        } catch (Exception e) {
            log.error("获取缓存钱包余额失败: chain={}, address={}, error={}", chain, address, e.getMessage());
        }
        return null;
    }

    @Override
    public void cacheWalletBalance(String chain, String address, UserWalletDTO.WalletInfo walletInfo) {
        try {
            String cacheKey = buildCacheKey(chain, address);
            String jsonData = objectMapper.writeValueAsString(walletInfo);
            
            redisTemplate.opsForValue().set(cacheKey, jsonData, cacheConfig.getBalanceExpireMinutes(), TimeUnit.MINUTES);
            log.debug("缓存钱包余额: chain={}, address={}", chain, address);
        } catch (JsonProcessingException e) {
            log.error("缓存钱包余额失败: chain={}, address={}, error={}", chain, address, e.getMessage());
        }
    }

    @Override
    public void removeCachedWalletBalance(String chain, String address) {
        try {
            String cacheKey = buildCacheKey(chain, address);
            redisTemplate.delete(cacheKey);
            log.debug("删除钱包余额缓存: chain={}, address={}", chain, address);
        } catch (Exception e) {
            log.error("删除钱包余额缓存失败: chain={}, address={}, error={}", chain, address, e.getMessage());
        }
    }

    @Override
    public UserWalletDTO.WalletInfo getWalletBalance(String chain, String address) {
        // 先从缓存获取
        UserWalletDTO.WalletInfo cachedBalance = getCachedWalletBalance(chain, address);
        if (cachedBalance != null) {
            return cachedBalance;
        }

        // 缓存未命中，查询区块链
        log.info("缓存未命中，查询区块链余额: chain={}, address={}", chain, address);
        UserWalletDTO.WalletInfo walletInfo = blockchainQueryService.getWalletBalance(chain, address);
        
        // 缓存查询结果
        if (walletInfo != null) {
            cacheWalletBalance(chain, address, walletInfo);
        }
        
        return walletInfo;
    }

    @Override
    public void refreshWalletBalancesCache(List<UserWallet> wallets) {
        log.info("开始批量刷新钱包余额缓存，数量: {}", wallets.size());
        
        for (UserWallet wallet : wallets) {
            try {
                // 删除旧缓存
                removeCachedWalletBalance(wallet.getChain(), wallet.getAddress());
                
                // 查询最新余额并缓存
                UserWalletDTO.WalletInfo walletInfo = blockchainQueryService.getWalletBalance(
                        wallet.getChain(), wallet.getAddress());
                
                if (walletInfo != null) {
                    cacheWalletBalance(wallet.getChain(), wallet.getAddress(), walletInfo);
                }
                
                // 避免请求过于频繁
                Thread.sleep(cacheConfig.getRequestIntervalMs());
                
            } catch (Exception e) {
                log.error("刷新钱包余额缓存失败: chain={}, address={}, error={}", 
                        wallet.getChain(), wallet.getAddress(), e.getMessage());
            }
        }
        
        log.info("批量刷新钱包余额缓存完成");
    }

    @Override
    public void cleanExpiredCache() {
        // Redis会自动清理过期的key，这里可以添加额外的清理逻辑
        log.debug("清理过期缓存");
    }

    /**
     * 构建缓存key
     */
    private String buildCacheKey(String chain, String address) {
        return CACHE_PREFIX + chain.toLowerCase() + ":" + address.toLowerCase();
    }
}
