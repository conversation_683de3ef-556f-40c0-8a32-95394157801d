package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.entity.Tweet;
import com.dddd.web3tools.repository.TweetRepository;
import com.dddd.web3tools.service.TwitterService;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TwitterServiceImpl implements TwitterService {

    private final TweetRepository tweetRepository;

    public TwitterServiceImpl(TweetRepository tweetRepository) {
        this.tweetRepository = tweetRepository;
    }

    @Override
    public List<Tweet> getRecentTweets() {
        Pageable topTwenty = PageRequest.of(0, 20, Sort.by("createdAt").descending());
        return tweetRepository.findByType(0, topTwenty).getContent();
    }

    @Override
    public Tweet updateTweetState(String id, Integer state) {
        return tweetRepository.findById(id)
                .map(tweet -> {
                    tweet.setState(state);
                    return tweetRepository.save(tweet);
                })
                .orElseThrow(() -> new RuntimeException("Tweet not found with id: " + id));
    }
}