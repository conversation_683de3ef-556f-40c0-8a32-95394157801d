package com.dddd.web3tools.service.impl;

import com.dddd.web3tools.constant.RedisKeyConstant;
import com.dddd.web3tools.entity.Keyword;
import com.dddd.web3tools.repository.KeywordRepository;
import jakarta.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class KeywordService {

    @Autowired
    private KeywordRepository keywordRepository;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    // 添加关键词并更新缓存
    @Transactional
    public Keyword addKeyword(String word) {
        Keyword keyword = new Keyword();
        keyword.setWord(word);
        keyword = keywordRepository.save(keyword);
        redisTemplate.opsForSet().add(RedisKeyConstant.KEYWORDS_CACHE_KEY, word);
        return keyword;
    }

    // 删除关键词并更新缓存
    @Transactional
    public void deleteKeyword(Long id) {
        keywordRepository.findById(id).ifPresent(keyword -> {
            keywordRepository.delete(keyword);
            redisTemplate.opsForSet().remove(RedisKeyConstant.KEYWORDS_CACHE_KEY, keyword.getWord());
        });
    }

    public Set<String> getKewordByBaseType() {
        Set<String> keywords = redisTemplate.opsForSet().members(RedisKeyConstant.KEYWORDS_CACHE_KEY);
        if (keywords == null || keywords.isEmpty()) {
            keywords = keywordRepository.findByType(Keyword.TYPE_BASE)
                    .stream()
                    .map(Keyword::getWord)
                    .collect(Collectors.toSet());
            if (!keywords.isEmpty()) {
                redisTemplate.opsForSet().add(RedisKeyConstant.KEYWORDS_CACHE_KEY,
                        keywords.toArray(new String[0]));
                redisTemplate.expire(RedisKeyConstant.KEYWORDS_CACHE_KEY, 3600, TimeUnit.SECONDS);
            }
        }
        return keywords;
    }

    // 获取所有关键词(优先从缓存获取)
    public Set<String> getAllKeywords() {
        Set<String> keywords = redisTemplate.opsForSet().members(RedisKeyConstant.KEYWORDS_CACHE_KEY);
        if (keywords == null || keywords.isEmpty()) {
            keywords = keywordRepository.findAll()
                    .stream()
                    .map(Keyword::getWord)
                    .collect(Collectors.toSet());
            if (!keywords.isEmpty()) {
                redisTemplate.opsForSet().add(RedisKeyConstant.KEYWORDS_CACHE_KEY,
                        keywords.toArray(new String[0]));
            }
        }
        return keywords;
    }

    // 获取所属类别的关键词(优先从缓存获取)
    public Set<String> getKeywordsByType(String type,String redisKey) {
        Set<String> keywords = redisTemplate.opsForSet().members(redisKey);
        if (keywords == null || keywords.isEmpty()) {
            keywords = keywordRepository.findByType(type)
                    .stream()
                    .map(Keyword::getWord)
                    .collect(Collectors.toSet());
            if (!keywords.isEmpty()) {
                redisTemplate.opsForSet().add(redisKey,
                        keywords.toArray(new String[0]));
            }
        }
        return keywords;
    }
}