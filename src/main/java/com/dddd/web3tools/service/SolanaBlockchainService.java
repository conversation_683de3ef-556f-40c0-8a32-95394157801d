package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.TransactionDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * Solana区块链服务接口
 */
public interface SolanaBlockchainService {

    /**
     * 查询钱包的交易记录
     */
    List<TransactionDTO> getWalletTransactions(String walletAddress, TransactionDTO.QueryRequest request);

    /**
     * 获取代币价格
     */
    BigDecimal getTokenPrice(String tokenAddress);

    /**
     * 获取代币余额
     */
    Long getTokenBalance(String walletAddress, String tokenAddress);

    /**
     * 验证钱包地址格式
     */
    boolean isValidWalletAddress(String address);

    /**
     * 发送交易（跟单）
     */
    String sendTransaction(String privateKey, String toAddress, String tokenAddress, Long amount);

    /**
     * 获取SOL余额
     */
    Long getSolBalance(String walletAddress);
}
