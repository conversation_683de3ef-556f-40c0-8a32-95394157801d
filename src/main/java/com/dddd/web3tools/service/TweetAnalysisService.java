package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.TweetAnalysisResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.request.ChatRequest;
import dev.langchain4j.model.chat.request.ResponseFormat;
import dev.langchain4j.model.chat.request.ResponseFormatType;
import dev.langchain4j.model.chat.request.json.JsonObjectSchema;
import dev.langchain4j.model.chat.request.json.JsonSchema;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 推文分析服务
 * 使用Gemini AI分析推文内容，判断是否包含铸造/mint相关内容
 */
@Service
@Slf4j
public class TweetAnalysisService {

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private GoogleAiGeminiChatModel googleAiGeminiChatModel;



    // URL匹配正则表达式
    private static final Pattern URL_PATTERN = Pattern.compile(
        "https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+",
        Pattern.CASE_INSENSITIVE
    );

    // mint相关关键词
    private static final List<String> MINT_KEYWORDS = Arrays.asList(
        "mint", "minting", "铸造", "铸币", "mint now", "mint live", "mint soon",
        "free mint", "public mint", "whitelist mint", "presale mint",
        "mint price", "mint cost", "mint fee", "mint开始", "开始mint",
        "正在mint", "mint中", "mint活动", "mint项目", "nft mint", "token mint",
            "新东西","新项目","新协议","速度冲"
    );

    // 垃圾内容关键词
    private static final List<String> SPAM_KEYWORDS = Arrays.asList(
        "follow me", "关注我", "点赞", "转发", "retweet", "like and retweet",
        "giveaway", "抽奖", "follow for follow",
        "f4f", "互关", "互粉", "求关注", "求粉", "广告", "推广", "promotion"
    );

    /**
     * 分析推文内容
     */
    public TweetAnalysisResult analyzeTweet(String tweetContent) {
        log.info("=== 开始分析推文 ===");
        log.info("推文内容: {}", tweetContent);

        try {
            // 基础检查
            boolean containsUrl = containsUrl(tweetContent);
            List<String> extractedUrls = extractUrls(tweetContent);
            boolean containsMintKeywords = containsMintKeywords(tweetContent);
            List<String> foundMintKeywords = findMintKeywords(tweetContent);
            boolean isSpam = isSpamContent(tweetContent);

            log.info("基础检查结果:");
            log.info("- 包含URL: {}", containsUrl);
            log.info("- 提取的URLs: {}", extractedUrls);
            log.info("- 包含mint关键词: {}", containsMintKeywords);
            log.info("- 发现的mint关键词: {}", foundMintKeywords);
            log.info("- 是否为垃圾内容: {}", isSpam);

            // 如果既没有URL也没有mint关键词，直接返回不符合条件
            // 修改逻辑：只要有URL或有mint关键词中的任何一个条件满足就可以通过基础检查
            if (!containsUrl && !containsMintKeywords) {
                String reason = "既不包含网址链接也不包含mint相关关键词";
                log.info("基础检查未通过，原因: {}", reason);

                return TweetAnalysisResult.builder()
                    .containsMintContent(containsMintKeywords)
                    .containsUrl(containsUrl)
                    .isSpam(isSpam)
                    .shouldNotify(false)
                    .confidence(90)
                    .extractedUrls(extractedUrls)
                    .mintKeywords(foundMintKeywords)
                    .analysisDetails("基础检查未通过")
                    .reason(reason)
                    .originalContent(tweetContent)
                    .build();
            }

            // 使用AI进行深度分析
            log.info("基础检查通过，开始AI深度分析...");
            ChatResponse aiAnalysis = performAIAnalysis(tweetContent);
            TweetAnalysisResult aiResult = parseAIAnalysis(aiAnalysis, tweetContent);

            // 综合判断 - 修改逻辑：只要有URL或有mint关键词就可以，不需要同时满足
            boolean shouldNotify = aiResult.isShouldNotify() && !isSpam && (containsUrl || containsMintKeywords);

            log.info("=== 最终分析结果 ===");
            log.info("AI建议推送: {}", aiResult.isShouldNotify());
            log.info("不是垃圾内容: {}", !isSpam);
            log.info("包含URL: {}", containsUrl);
            log.info("包含mint关键词: {}", containsMintKeywords);
            log.info("基础检查通过(URL或关键词): {}", (containsUrl || containsMintKeywords));
            log.info("最终是否推送: {}", shouldNotify);

            TweetAnalysisResult finalResult = TweetAnalysisResult.builder()
                .containsMintContent(containsMintKeywords)
                .containsUrl(containsUrl)
                .isSpam(isSpam)
                .shouldNotify(shouldNotify)
                .confidence(aiResult.getConfidence())
                .extractedUrls(extractedUrls)
                .mintKeywords(foundMintKeywords)
                .analysisDetails(aiResult.getAnalysisDetails())
                .reason(shouldNotify ? "符合mint推送条件" : aiResult.getReason())
                .originalContent(tweetContent)
                .build();

            log.info("=== 分析完成 ===");
            return finalResult;

        } catch (Exception e) {
            log.error("分析推文时发生错误", e);
            return TweetAnalysisResult.builder()
                .containsMintContent(false)
                .containsUrl(false)
                .isSpam(false)
                .shouldNotify(false)
                .confidence(0)
                .extractedUrls(new ArrayList<>())
                .mintKeywords(new ArrayList<>())
                .analysisDetails("分析过程中发生错误: " + e.getMessage())
                .reason("系统错误")
                .originalContent(tweetContent)
                .build();
        }
    }

    /**
     * 检查是否包含URL
     */
    private boolean containsUrl(String content) {
        return URL_PATTERN.matcher(content).find();
    }

    /**
     * 提取所有URL
     */
    private List<String> extractUrls(String content) {
        List<String> urls = new ArrayList<>();
        Matcher matcher = URL_PATTERN.matcher(content);
        while (matcher.find()) {
            urls.add(matcher.group());
        }
        return urls;
    }

    /**
     * 检查是否包含mint关键词
     */
    private boolean containsMintKeywords(String content) {
        String lowerContent = content.toLowerCase();
        return MINT_KEYWORDS.stream().anyMatch(keyword -> 
            lowerContent.contains(keyword.toLowerCase()));
    }

    /**
     * 查找包含的mint关键词
     */
    private List<String> findMintKeywords(String content) {
        List<String> foundKeywords = new ArrayList<>();
        String lowerContent = content.toLowerCase();
        for (String keyword : MINT_KEYWORDS) {
            if (lowerContent.contains(keyword.toLowerCase())) {
                foundKeywords.add(keyword);
            }
        }
        return foundKeywords;
    }

    /**
     * 检查是否为垃圾内容
     */
    private boolean isSpamContent(String content) {
        String lowerContent = content.toLowerCase();
        return SPAM_KEYWORDS.stream().anyMatch(keyword -> 
            lowerContent.contains(keyword.toLowerCase()));
    }

    /**
     * 使用AI进行深度分析
     */
    private ChatResponse performAIAnalysis(String tweetContent) {
        String prompt = String.format(
            "请分析以下推文内容，判断是否是关于NFT或代币铸造(mint)的重要信息：\n\n" +
            "推文内容：%s\n\n" +
            "判断标准：\n" +
            "1. 是否正在进行NFT或代币铸造活动\n" +
            "2. 是否包含有效的铸造链接或官方网站\n" +
            "3. 是否为垃圾信息、广告或无关内容\n" +
            "4. 信息的时效性和重要性\n",
//            "5. 如果前面\n",
            tweetContent
        );

        try {
            log.info("发送AI分析请求，推文内容: {}", tweetContent.substring(0, Math.min(1000, tweetContent.length())));

            ResponseFormat responseFormat = ResponseFormat.builder()
                    .type(ResponseFormatType.JSON)
                    .jsonSchema(JsonSchema.builder() // see [1] below
                            .rootElement(JsonObjectSchema.builder()
                                    .addBooleanProperty("isMintRelated")
                                    .addBooleanProperty("isImportant")
                                    .addIntegerProperty("confidence")
                                    .addStringProperty("reason")
                                    .addStringProperty("details")
//                                    .addProperty("reason", JsonArraySchema.builder()
//                                            .items(new JsonStringSchema())
//                                            .build())
//                                    .addProperty("details", JsonArraySchema.builder()
//                                            .items(new JsonStringSchema())
//                                            .build())
                                    .build())
                            .build())
                    .build();
            ChatRequest chatRequest = ChatRequest.builder()
                    .messages(UserMessage.from(prompt))
                    .responseFormat(responseFormat)
                    .build();
            ChatResponse response = googleAiGeminiChatModel.chat(chatRequest);
            log.info("收到Gemini AI分析结果: {}  content: {}", response,response.aiMessage().text());
            return response;
        } catch (Exception e) {
            log.error("AI分析失败", e);
            return ChatResponse.builder()
                    .aiMessage(AiMessage.aiMessage("AI分析失败: " + e.getMessage()))
                   .build();
        }
    }

    /**
     * 解析AI分析结果
     */
    private TweetAnalysisResult parseAIAnalysis(ChatResponse aiResponse, String originalContent) {
        try {
            log.info("开始解析AI响应: {}", aiResponse);

            // 尝试提取JSON部分
            String jsonPart = extractJsonFromResponse(aiResponse.aiMessage().text());
            log.info("提取的JSON部分: {}", jsonPart);

            JsonNode jsonNode = objectMapper.readTree(jsonPart);

            boolean isMintRelated = jsonNode.path("isMintRelated").asBoolean(false);
            boolean isImportant = jsonNode.path("isImportant").asBoolean(false);
            int confidence = jsonNode.path("confidence").asInt(0);
            String reason = jsonNode.path("reason").asText("未知原因");
            String details = jsonNode.path("details").asText("无详细信息");

            boolean shouldNotify = isMintRelated && isImportant && confidence >= 70;

            log.info("AI分析结果解析完成 - isMintRelated: {}, isImportant: {}, confidence: {}, shouldNotify: {}",
                    isMintRelated, isImportant, confidence, shouldNotify);
            log.info("分析原因: {}", reason);
            log.info("详细分析: {}", details);

            return TweetAnalysisResult.builder()
                .shouldNotify(shouldNotify)
                .confidence(confidence)
                .analysisDetails(details)
                .reason(reason)
                .originalContent(originalContent)
                .build();

        } catch (Exception e) {
            log.error("解析AI响应失败: {}", aiResponse, e);
            return TweetAnalysisResult.builder()
                .shouldNotify(false)
                .confidence(0)
                .analysisDetails("AI响应解析失败")
                .reason("解析错误: " + e.getMessage())
                .originalContent(originalContent)
                .build();
        }
    }

    /**
     * 从响应中提取JSON部分
     */
    private String extractJsonFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            log.warn("AI响应为空");
            return getDefaultJsonResponse("AI响应为空");
        }

        // 清理响应内容，移除可能的markdown标记
        String cleanResponse = response.trim();
        if (cleanResponse.startsWith("```json")) {
            cleanResponse = cleanResponse.substring(7);
        }
        if (cleanResponse.endsWith("```")) {
            cleanResponse = cleanResponse.substring(0, cleanResponse.length() - 3);
        }
        cleanResponse = cleanResponse.trim();

        // 查找JSON开始和结束位置
        int startIndex = cleanResponse.indexOf("{");
        int endIndex = findMatchingBrace(cleanResponse, startIndex);

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            String jsonPart = cleanResponse.substring(startIndex, endIndex + 1);

            // 验证JSON格式
            try {
                objectMapper.readTree(jsonPart);
                return jsonPart;
            } catch (Exception e) {
                log.warn("提取的JSON格式无效: {}", jsonPart);
                return getDefaultJsonResponse("JSON格式无效");
            }
        }

        log.warn("无法从响应中提取有效JSON: {}", response);
        return getDefaultJsonResponse("无法解析AI响应");
    }

    /**
     * 查找匹配的右大括号
     */
    private int findMatchingBrace(String text, int startIndex) {
        if (startIndex == -1 || startIndex >= text.length()) {
            return -1;
        }

        int braceCount = 0;
        for (int i = startIndex; i < text.length(); i++) {
            char c = text.charAt(i);
            if (c == '{') {
                braceCount++;
            } else if (c == '}') {
                braceCount--;
                if (braceCount == 0) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * 获取默认JSON响应
     */
    private String getDefaultJsonResponse(String reason) {
        return String.format(
            "{\"isMintRelated\": false, \"isImportant\": false, \"confidence\": 0, \"reason\": \"%s\", \"details\": \"响应格式错误\"}",
            reason.replace("\"", "\\\"")
        );
    }
}
