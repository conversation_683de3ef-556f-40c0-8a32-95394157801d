package com.dddd.web3tools.service;

import com.dddd.web3tools.config.TwitterApiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Twitter API Key池管理服务
 * 负责管理多个API Key的轮换和使用计数
 */
@Slf4j
@Service
public class TwitterApiKeyPoolService {

    private static final String REDIS_KEY_PREFIX = "twitter:api:key:";
    private static final String CURRENT_KEY_INDEX = "twitter:api:current_index";
    private static final String FAILED_KEYS_PREFIX = "twitter:api:failed:";

    @Autowired
    private TwitterApiConfig twitterApiConfig;

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    private final AtomicInteger currentKeyIndex = new AtomicInteger(0);
    private boolean redisAvailable = false;

    // 内存中记录失败的密钥（当Redis不可用时使用）
    private final Set<String> failedKeysInMemory = new HashSet<>();

    @PostConstruct
    public void init() {
        try {
            if (redisTemplate != null) {
                // 测试Redis连接
                redisTemplate.opsForValue().get("test");
                redisAvailable = true;

                // 初始化当前key索引
                Object currentIndex = redisTemplate.opsForValue().get(CURRENT_KEY_INDEX);
                if (currentIndex != null) {
                    currentKeyIndex.set((Integer) currentIndex);
                }
                log.info("Twitter API Key池服务初始化完成，Redis可用，当前key索引: {}", currentKeyIndex.get());
            } else {
                log.warn("Redis不可用，Twitter API Key池将使用内存模式");
            }
        } catch (Exception e) {
            log.warn("Redis连接失败，Twitter API Key池将使用内存模式: {}", e.getMessage());
            redisAvailable = false;
        }
    }
    
    /**
     * 获取当前可用的API Key
     * @return 当前API Key
     */
    public String getCurrentApiKey() {
        if (!twitterApiConfig.getPool().isEnabled() ||
            twitterApiConfig.getPool().getKeys() == null ||
            twitterApiConfig.getPool().getKeys().isEmpty()) {
            log.warn("API Key池未启用或未配置，返回空");
            return null;
        }

        List<String> keys = twitterApiConfig.getPool().getKeys();
        String currentKey = findAvailableKey(keys);

        if (currentKey == null) {
            log.error("所有API Key都不可用，请检查配置或等待重置");
            return null;
        }

        return currentKey;
    }

    /**
     * 查找可用的API Key（跳过已耗尽和失败的key）
     * @param keys 所有密钥列表
     * @return 可用的密钥，如果都不可用则返回null
     */
    private String findAvailableKey(List<String> keys) {
        int startIndex = currentKeyIndex.get() % keys.size();

        // 从当前索引开始查找可用的key
        for (int i = 0; i < keys.size(); i++) {
            int index = (startIndex + i) % keys.size();
            String key = keys.get(index);

            // 检查key是否可用（未耗尽且未失败）
            if (!isKeyExhausted(key) && !isKeyFailed(key)) {
                // 更新当前索引
                currentKeyIndex.set(index);
                if (redisAvailable && redisTemplate != null) {
                    try {
                        redisTemplate.opsForValue().set(CURRENT_KEY_INDEX, index);
                    } catch (Exception e) {
                        log.error("保存当前key索引到Redis失败: {}", e.getMessage());
                    }
                }
                return key;
            }
        }

        return null; // 所有key都不可用
    }
    
    /**
     * 记录API Key使用次数
     * @param apiKey 使用的API Key
     */
    public void recordKeyUsage(String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            return;
        }

        if (!redisAvailable || redisTemplate == null) {
            log.debug("Redis不可用，跳过API Key使用计数");
            return;
        }

        try {
            String redisKey = REDIS_KEY_PREFIX + apiKey;
            Long currentCount = redisTemplate.opsForValue().increment(redisKey);

            // 设置过期时间（根据配置的重置间隔）
            if (currentCount == 1) {
                Duration expiration = Duration.ofHours(twitterApiConfig.getPool().getResetIntervalHours());
                redisTemplate.expire(redisKey, expiration);
            }

            log.debug("API Key {} 使用次数: {}", maskApiKey(apiKey), currentCount);

            // 检查是否需要切换key
            if (currentCount >= twitterApiConfig.getPool().getMaxRequestsPerKey()) {
                log.warn("API Key {} 已达到使用限制 {}，将在下次请求时切换",
                        maskApiKey(apiKey), twitterApiConfig.getPool().getMaxRequestsPerKey());
            }
        } catch (Exception e) {
            log.error("记录API Key使用次数失败: {}", e.getMessage());
        }
    }
    
    /**
     * 检查API Key是否已耗尽
     * @param apiKey API Key
     * @return 是否已耗尽
     */
    private boolean isKeyExhausted(String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            return true;
        }

        if (!redisAvailable || redisTemplate == null) {
            return false; // Redis不可用时，不限制使用
        }

        try {
            String redisKey = REDIS_KEY_PREFIX + apiKey;
            Object count = redisTemplate.opsForValue().get(redisKey);

            if (count == null) {
                return false;
            }

            int currentCount = (Integer) count;
            return currentCount >= twitterApiConfig.getPool().getMaxRequestsPerKey();
        } catch (Exception e) {
            log.error("检查API Key使用状态失败: {}", e.getMessage());
            return false; // 出错时不限制使用
        }
    }

    /**
     * 检查API Key是否失败（短时间内不可用）
     * @param apiKey API Key
     * @return 是否失败
     */
    private boolean isKeyFailed(String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            return true;
        }

        if (!redisAvailable || redisTemplate == null) {
            // Redis不可用时，使用内存记录
            return failedKeysInMemory.contains(apiKey);
        }

        try {
            String redisKey = FAILED_KEYS_PREFIX + apiKey;
            return redisTemplate.hasKey(redisKey);
        } catch (Exception e) {
            log.error("检查API Key失败状态失败: {}", e.getMessage());
            return false; // 出错时不限制使用
        }
    }

    /**
     * 标记API Key为失败状态（短时间内不使用）
     * @param apiKey 失败的API Key
     * @param failureReason 失败原因
     */
    public void markKeyAsFailed(String apiKey, String failureReason) {
        if (apiKey == null || apiKey.isEmpty()) {
            return;
        }

        log.warn("标记API Key {} 为失败状态，原因: {}", maskApiKey(apiKey), failureReason);

        if (!redisAvailable || redisTemplate == null) {
            // Redis不可用时，使用内存记录
            failedKeysInMemory.add(apiKey);
            return;
        }

        try {
            String redisKey = FAILED_KEYS_PREFIX + apiKey;
            // 标记失败，5分钟后自动恢复
            redisTemplate.opsForValue().set(redisKey, failureReason, Duration.ofMinutes(5));
            log.info("API Key {} 将在5分钟后自动恢复可用状态", maskApiKey(apiKey));
        } catch (Exception e) {
            log.error("标记API Key失败状态失败: {}", e.getMessage());
            // 降级到内存记录
            failedKeysInMemory.add(apiKey);
        }
    }
    
    /**
     * 切换到下一个API Key
     */
    private synchronized void switchToNextKey() {
        List<String> keys = twitterApiConfig.getPool().getKeys();
        if (keys == null || keys.isEmpty()) {
            return;
        }

        int nextIndex = (currentKeyIndex.get() + 1) % keys.size();
        currentKeyIndex.set(nextIndex);

        // 保存到Redis（如果可用）
        if (redisAvailable && redisTemplate != null) {
            try {
                redisTemplate.opsForValue().set(CURRENT_KEY_INDEX, nextIndex);
            } catch (Exception e) {
                log.error("保存当前key索引到Redis失败: {}", e.getMessage());
            }
        }

        String newKey = keys.get(nextIndex);
        log.info("切换到新的API Key: {}", maskApiKey(newKey));
    }

    /**
     * 获取下一个可用的API Key（用于错误重试）
     * @param currentFailedKey 当前失败的密钥
     * @return 下一个可用的密钥，如果没有则返回null
     */
    public String getNextAvailableKey(String currentFailedKey) {
        if (!twitterApiConfig.getPool().isEnabled() ||
            twitterApiConfig.getPool().getKeys() == null ||
            twitterApiConfig.getPool().getKeys().isEmpty()) {
            return null;
        }

        // 标记当前密钥为失败
        if (currentFailedKey != null) {
            markKeyAsFailed(currentFailedKey, "API调用失败");
        }

        // 查找下一个可用的密钥
        List<String> keys = twitterApiConfig.getPool().getKeys();
        return findAvailableKey(keys);
    }
    
    /**
     * 获取API Key使用统计
     * @param apiKey API Key
     * @return 使用次数
     */
    public int getKeyUsageCount(String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            return 0;
        }

        if (!redisAvailable || redisTemplate == null) {
            return 0;
        }

        try {
            String redisKey = REDIS_KEY_PREFIX + apiKey;
            Object count = redisTemplate.opsForValue().get(redisKey);
            return count != null ? (Integer) count : 0;
        } catch (Exception e) {
            log.error("获取API Key使用统计失败: {}", e.getMessage());
            return 0;
        }
    }
    
    /**
     * 获取所有API Key的使用统计
     * @return 统计信息
     */
    public String getPoolStatus() {
        if (!twitterApiConfig.getPool().isEnabled() || 
            twitterApiConfig.getPool().getKeys() == null || 
            twitterApiConfig.getPool().getKeys().isEmpty()) {
            return "API Key池未启用或未配置";
        }
        
        StringBuilder status = new StringBuilder();
        status.append("Twitter API Key池状态:\n");
        
        List<String> keys = twitterApiConfig.getPool().getKeys();
        int currentIndex = currentKeyIndex.get() % keys.size();
        
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            int usage = getKeyUsageCount(key);
            String marker = (i == currentIndex) ? " [当前]" : "";
            
            status.append(String.format("Key %d: %s - 使用次数: %d/%d%s\n", 
                    i + 1, 
                    maskApiKey(key), 
                    usage, 
                    twitterApiConfig.getPool().getMaxRequestsPerKey(),
                    marker));
        }
        
        return status.toString();
    }
    
    /**
     * 重置所有API Key的使用计数
     */
    public void resetAllKeyUsage() {
        if (twitterApiConfig.getPool().getKeys() == null) {
            return;
        }

        if (!redisAvailable || redisTemplate == null) {
            log.warn("Redis不可用，无法重置API Key使用计数");
            // 清空内存中的失败记录
            failedKeysInMemory.clear();
            return;
        }

        try {
            for (String key : twitterApiConfig.getPool().getKeys()) {
                // 重置使用计数
                String usageKey = REDIS_KEY_PREFIX + key;
                redisTemplate.delete(usageKey);

                // 重置失败状态
                String failedKey = FAILED_KEYS_PREFIX + key;
                redisTemplate.delete(failedKey);
            }
            // 清空内存中的失败记录
            failedKeysInMemory.clear();
            log.info("已重置所有API Key的使用计数和失败状态");
        } catch (Exception e) {
            log.error("重置API Key使用计数失败: {}", e.getMessage());
        }
    }

    /**
     * 清除指定API Key的失败状态
     * @param apiKey 要清除失败状态的API Key
     */
    public void clearKeyFailureStatus(String apiKey) {
        if (apiKey == null || apiKey.isEmpty()) {
            return;
        }

        log.info("清除API Key {} 的失败状态", maskApiKey(apiKey));

        // 从内存中移除
        failedKeysInMemory.remove(apiKey);

        if (!redisAvailable || redisTemplate == null) {
            return;
        }

        try {
            String redisKey = FAILED_KEYS_PREFIX + apiKey;
            redisTemplate.delete(redisKey);
        } catch (Exception e) {
            log.error("清除API Key失败状态失败: {}", e.getMessage());
        }
    }
    
    /**
     * 掩码API Key用于日志输出
     * @param apiKey 原始API Key
     * @return 掩码后的API Key
     */
    public String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "****";
        }
        return apiKey.substring(0, 4) + "****" + apiKey.substring(apiKey.length() - 4);
    }
}
