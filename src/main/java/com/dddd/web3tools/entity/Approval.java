package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "approval")
@Data
public class Approval {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(columnDefinition = "varchar(256)")
    private String content;

    @Column(name = "review_status")
    private Boolean reviewStatus;

    @Column(name = "audit_comment")
    private String auditComment;

    private LocalDateTime reviewedAt; // 审核时间

    /**
     * 类型   1，大项目追踪
     */
    @Column(length = 2)
    private Integer type;
}
