package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * 表格列配置实体类
 */
@Entity
@Table(name = "table_columns",
    uniqueConstraints = {
        @UniqueConstraint(name = "unique_project_column", columnNames = {"project_id", "column_key"})
    },
    indexes = {
        @Index(name = "idx_project_id", columnList = "project_id"),
        @Index(name = "idx_sort_order", columnList = "sortOrder")
    }
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TableColumn {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", foreignKey = @ForeignKey(name = "fk_table_column_project"))
    private Project project;

    @Column(name = "column_key", nullable = false, length = 50)
    private String columnKey;

    @Column(name = "label", nullable = false, length = 100)
    private String label;

    @Column(name = "width", length = 20)
    private String width;

    @Column(name = "editable")
    @Builder.Default
    private Boolean editable = false;

    @Column(name = "visible")
    @Builder.Default
    private Boolean visible = true;

    @Column(name = "sort_order")
    @Builder.Default
    private Integer sortOrder = 0;

    @Column(name = "created_at", nullable = false, updatable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at")
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 获取项目ID
     */
    public Long getProjectId() {
        return project != null ? project.getId() : null;
    }

    /**
     * 设置项目ID
     */
    public void setProjectId(Long projectId) {
        if (projectId != null) {
            this.project = Project.builder().id(projectId).build();
        }
    }
}
