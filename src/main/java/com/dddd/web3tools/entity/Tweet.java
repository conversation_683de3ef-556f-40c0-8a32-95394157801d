package com.dddd.web3tools.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 推特推文实体类
 */
@Entity
@Table(name = "tweets")
@Data
public class Tweet {
    @Id
    private String id;

    @Column(columnDefinition = "TEXT")
    private String content;

    @Column(columnDefinition = "varchar(64)")
    private String author;

    @Column(length = 11)
    private Integer followers;

    @Column(columnDefinition = "varchar(32)")
    private String tweetTime;

    @Column(columnDefinition = "TEXT")
    private String aiAnalysis;

    @Column(columnDefinition = "varchar(64)")
    private String screenName;

    /**
     * 1: 项目追踪用的
     */
    @Column(length = 2)
    private Integer type =0;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 0: 正常  1: 拉黑  2:微信已推送
     */
    @Column(length = 2)
    private Integer state =0;

}