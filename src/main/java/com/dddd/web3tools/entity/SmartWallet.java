package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 聪明钱包实体
 */
@Entity
@Table(name = "smart_wallets")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SmartWallet {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 钱包地址
     */
    @Column(name = "address", nullable = false, unique = true, length = 64)
    private String address;

    /**
     * 钱包昵称
     */
    @Column(name = "nickname", length = 100)
    private String nickname;

    /**
     * 胜率百分比
     */
    @Column(name = "win_rate", precision = 5, scale = 2)
    private BigDecimal winRate;

    /**
     * 总盈利 (lamports)
     */
    @Column(name = "total_profit")
    private Long totalProfit;

    /**
     * 收益率百分比
     */
    @Column(name = "profit_percent", precision = 8, scale = 2)
    private BigDecimal profitPercent;

    /**
     * 平均持仓时间（小时）
     */
    @Column(name = "avg_hold_time")
    private Integer avgHoldTime;

    /**
     * 近期交易次数
     */
    @Column(name = "recent_trades")
    private Integer recentTrades;

    /**
     * 跟单人数
     */
    @Column(name = "followers")
    private Integer followers;

    /**
     * 标签（JSON格式存储）
     */
    @Column(name = "tags", columnDefinition = "TEXT")
    private String tags;

    /**
     * 状态: active, offline
     */
    @Column(name = "status", length = 20)
    private String status;

    /**
     * 最后活跃时间
     */
    @Column(name = "last_active")
    private LocalDateTime lastActive;

    /**
     * 总交易次数
     */
    @Column(name = "total_trades")
    private Integer totalTrades;

    /**
     * 盈利交易次数
     */
    @Column(name = "profitable_trades")
    private Integer profitableTrades;

    /**
     * 总交易量 (lamports)
     */
    @Column(name = "total_volume")
    private Long totalVolume;

    /**
     * 平均盈利 (lamports)
     */
    @Column(name = "avg_profit")
    private Long avgProfit;

    /**
     * 最大单笔盈利
     */
    @Column(name = "max_profit")
    private Long maxProfit;

    /**
     * 最大单笔亏损
     */
    @Column(name = "max_loss")
    private Long maxLoss;

    /**
     * 最佳连胜次数
     */
    @Column(name = "best_win_streak")
    private Integer bestWinStreak;

    /**
     * 当前连胜/连败次数
     */
    @Column(name = "current_streak")
    private Integer currentStreak;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
}
