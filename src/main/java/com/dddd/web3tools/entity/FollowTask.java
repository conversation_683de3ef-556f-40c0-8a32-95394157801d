package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 跟单任务实体
 */
@Entity
@Table(name = "follow_tasks")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FollowTask {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 任务ID（对外展示的ID）
     */
    @Column(name = "task_id", nullable = false, unique = true, length = 50)
    private String taskId;

    /**
     * 用户钱包地址
     */
    @Column(name = "user_wallet", nullable = false, length = 64)
    private String userWallet;

    /**
     * 聪明钱包ID
     */
    @Column(name = "smart_wallet_id", nullable = false)
    private Long smartWalletId;

    /**
     * 加密的钱包私钥
     */
    @Column(name = "wallet_private_key", columnDefinition = "TEXT")
    private String walletPrivateKey;

    /**
     * 跟单配置（JSON格式）
     */
    @Column(name = "config", columnDefinition = "TEXT")
    private String config;

    /**
     * 任务状态: active, paused, stopped
     */
    @Column(name = "status", length = 20)
    private String status;

    /**
     * 总盈利 (lamports)
     */
    @Column(name = "total_profit")
    private Long totalProfit;

    /**
     * 总亏损 (lamports)
     */
    @Column(name = "total_loss")
    private Long totalLoss;

    /**
     * 净盈亏 (lamports)
     */
    @Column(name = "net_profit")
    private Long netProfit;

    /**
     * 盈亏百分比
     */
    @Column(name = "profit_percent", precision = 8, scale = 2)
    private BigDecimal profitPercent;

    /**
     * 跟单次数
     */
    @Column(name = "follow_count")
    private Integer followCount;

    /**
     * 成功率
     */
    @Column(name = "success_rate", precision = 5, scale = 2)
    private BigDecimal successRate;

    /**
     * 总投入 (lamports)
     */
    @Column(name = "total_invested")
    private Long totalInvested;

    /**
     * 创建时间
     */
    @CreationTimestamp
    @Column(name = "created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 关联的聪明钱包
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "smart_wallet_id", insertable = false, updatable = false)
    private SmartWallet smartWallet;
}
