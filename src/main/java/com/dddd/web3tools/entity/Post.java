package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * 博客文章实体类
 */
@Entity
@Table(name = "posts")
public class Post {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 500)
    private String title;
    
    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;
    
    @Column(nullable = false, length = 100)
    private String category;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // 构造函数
    public Post() {}

    public Post(String title, String content, String category) {
        this.title = title;
        this.content = content;
        this.category = category;
    }

    // Getter和Setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 获取文章摘要（前200个字符）
     */
    public String getExcerpt() {
        if (content == null) {
            return "";
        }
        // 移除Markdown标记，获取纯文本
        String plainText = content.replaceAll("#+\\s*", "")  // 移除标题标记
                                 .replaceAll("\\*\\*([^*]+)\\*\\*", "$1")  // 移除粗体标记
                                 .replaceAll("\\*([^*]+)\\*", "$1")  // 移除斜体标记
                                 .replaceAll("```[\\s\\S]*?```", "")  // 移除代码块
                                 .replaceAll("`([^`]+)`", "$1")  // 移除行内代码
                                 .replaceAll("\\[([^\\]]+)\\]\\([^)]+\\)", "$1")  // 移除链接，保留文本
                                 .replaceAll("\\n+", " ")  // 替换换行为空格
                                 .trim();

        if (plainText.length() <= 200) {
            return plainText;
        }
        return plainText.substring(0, 200) + "...";
    }
}
