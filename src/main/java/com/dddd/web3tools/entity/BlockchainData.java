package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "blockchain_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BlockchainData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "chain_type", nullable = false)
    private String chainType; // ETH, BTC

    @Column(name = "block_number")
    private Long blockNumber;

    @Column(name = "block_hash")
    private String blockHash;

    @Column(name = "gas_price") // 以太坊gas价格，比特币可用作手续费
    private String gasPrice;

    @Column(name = "gas_limit") // 以太坊gas限制
    private Long gasLimit;

    @Column(name = "gas_used") // 实际使用的gas
    private Long gasUsed;

    @Column(name = "transaction_count")
    private Integer transactionCount;

    @Column(name = "block_size")
    private Long blockSize;

    @Column(name = "difficulty")
    private String difficulty;

    @Column(name = "timestamp")
    private Long timestamp;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @PrePersist
    public void prePersist() {
        this.createdAt = LocalDateTime.now();
    }
}