package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 钱包实体类
 */
@Entity
@Table(name = "wallets", 
    uniqueConstraints = {
        @UniqueConstraint(name = "unique_project_wallet", columnNames = {"project_id", "address"})
    },
    indexes = {
        @Index(name = "idx_address", columnList = "address"),
        @Index(name = "idx_added_by", columnList = "addedBy"),
        @Index(name = "idx_project_id", columnList = "project_id"),
        @Index(name = "idx_balance", columnList = "balance"),
        @Index(name = "idx_last_activity", columnList = "lastActivity")
    }
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Wallet {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "project_id", nullable = false, foreignKey = @ForeignKey(name = "fk_wallet_project"))
    private Project project;

    @Column(name = "address", nullable = false, length = 42)
    private String address;

    @Column(name = "name", length = 100)
    private String name;

    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;

    @Column(name = "balance", precision = 20, scale = 8)
    @Builder.Default
    private BigDecimal balance = BigDecimal.ZERO;

    @Column(name = "last_activity")
    private LocalDate lastActivity;

    @Column(name = "added_by", nullable = false, length = 42)
    private String addedBy;

    @Column(name = "created_at", nullable = false, updatable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at")
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 获取项目ID
     */
    public Long getProjectId() {
        return project != null ? project.getId() : null;
    }

    /**
     * 设置项目ID
     */
    public void setProjectId(Long projectId) {
        if (projectId != null) {
            this.project = Project.builder().id(projectId).build();
        }
    }
}
