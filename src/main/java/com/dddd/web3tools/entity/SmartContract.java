package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 智能合约实体类
 */
@Entity
@Table(name = "smart_contracts", indexes = {
    @Index(name = "idx_contract_address", columnList = "contractAddress"),
    @Index(name = "idx_created_at", columnList = "createdAt"),
    @Index(name = "idx_score", columnList = "score"),
    @Index(name = "idx_is_filtered", columnList = "isFiltered"),
    @Index(name = "idx_deploy_block", columnList = "deployBlockNumber")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SmartContract {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "contract_address", nullable = false, unique = true, length = 42)
    private String contractAddress;
    
    @Column(name = "deploy_transaction_hash", nullable = false, length = 66)
    private String deployTransactionHash;
    
    @Column(name = "deploy_block_number", nullable = false)
    private Long deployBlockNumber;
    
    @Column(name = "deploy_timestamp", nullable = false)
    private Long deployTimestamp;
    
    @Column(name = "deployer_address", nullable = false, length = 42)
    private String deployerAddress;
    
    @Column(name = "contract_name", length = 100)
    private String contractName;
    
    @Column(name = "contract_symbol", length = 20)
    private String contractSymbol;
    
    @Column(name = "is_token", nullable = false)
    private Boolean isToken = false;
    
    @Column(name = "is_filtered", nullable = false)
    private Boolean isFiltered = false; // 是否被过滤（工厂合约等）
    
    @Column(name = "filter_reason", length = 200)
    private String filterReason; // 过滤原因
    
    // 24小时统计数据
    @Column(name = "interaction_count_24h", nullable = false)
    private Integer interactionCount24h = 0;
    
    @Column(name = "unique_users_24h", nullable = false)
    private Integer uniqueUsers24h = 0;
    
    @Column(name = "total_value_24h", precision = 36, scale = 18)
    private BigDecimal totalValue24h = BigDecimal.ZERO;
    
    @Column(name = "avg_gas_used_24h", precision = 15, scale = 2)
    private BigDecimal avgGasUsed24h = BigDecimal.ZERO;
    
    @Column(name = "total_gas_used_24h")
    private Long totalGasUsed24h = 0L;
    
    @Column(name = "avg_transaction_value_24h", precision = 36, scale = 18)
    private BigDecimal avgTransactionValue24h = BigDecimal.ZERO;
    
    @Column(name = "max_transaction_value_24h", precision = 36, scale = 18)
    private BigDecimal maxTransactionValue24h = BigDecimal.ZERO;
    
    // 评分相关
    @Column(name = "score", precision = 8, scale = 2)
    private BigDecimal score = BigDecimal.ZERO;
    
    @Column(name = "activity_score", precision = 8, scale = 2)
    private BigDecimal activityScore = BigDecimal.ZERO;
    
    @Column(name = "value_score", precision = 8, scale = 2)
    private BigDecimal valueScore = BigDecimal.ZERO;
    
    @Column(name = "user_diversity_score", precision = 8, scale = 2)
    private BigDecimal userDiversityScore = BigDecimal.ZERO;
    
    @Column(name = "gas_efficiency_score", precision = 8, scale = 2)
    private BigDecimal gasEfficiencyScore = BigDecimal.ZERO;
    
    // 时间相关
    @Column(name = "last_interaction_time")
    private LocalDateTime lastInteractionTime;
    
    @Column(name = "stats_updated_at")
    private LocalDateTime statsUpdatedAt;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @PrePersist
    public void prePersist() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 计算综合评分
     */
    public void calculateScore() {
        // 综合评分 = 活跃度评分 * 0.3 + 价值评分 * 0.3 + 用户多样性评分 * 0.25 + Gas效率评分 * 0.15
        this.score = this.activityScore.multiply(BigDecimal.valueOf(0.3))
            .add(this.valueScore.multiply(BigDecimal.valueOf(0.3)))
            .add(this.userDiversityScore.multiply(BigDecimal.valueOf(0.25)))
            .add(this.gasEfficiencyScore.multiply(BigDecimal.valueOf(0.15)));
    }
}
