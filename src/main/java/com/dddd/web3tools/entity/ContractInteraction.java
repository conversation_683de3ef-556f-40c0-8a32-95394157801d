package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合约交互记录实体类（用于24小时统计）
 */
@Entity
@Table(name = "contract_interactions", indexes = {
    @Index(name = "idx_contract_address", columnList = "contractAddress"),
    @Index(name = "idx_interaction_time", columnList = "interactionTime"),
    @Index(name = "idx_user_address", columnList = "userAddress"),
    @Index(name = "idx_contract_time", columnList = "contractAddress,interactionTime")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractInteraction {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "contract_address", nullable = false, length = 42)
    private String contractAddress;
    
    @Column(name = "transaction_hash", nullable = false, length = 66)
    private String transactionHash;
    
    @Column(name = "user_address", nullable = false, length = 42)
    private String userAddress;
    
    @Column(name = "block_number", nullable = false)
    private Long blockNumber;
    
    @Column(name = "transaction_value", precision = 36, scale = 18)
    private BigDecimal transactionValue = BigDecimal.ZERO;
    
    @Column(name = "gas_used")
    private Long gasUsed;
    
    @Column(name = "gas_price", precision = 65, scale = 0)
    private String gasPrice;
    
    @Column(name = "method_signature", length = 10)
    private String methodSignature; // 方法签名前4字节
    
    @Column(name = "interaction_time", nullable = false)
    private LocalDateTime interactionTime;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @PrePersist
    public void prePersist() {
        this.createdAt = LocalDateTime.now();
    }
}
