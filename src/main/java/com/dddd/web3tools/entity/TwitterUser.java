package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "twitter_user")
@Data
public class TwitterUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true)
    private String userId;

    @Column(columnDefinition = "varchar(32)")
    private String twitterName;

    @Column(columnDefinition = "varchar(32)")
    private String screenName;

    @Column(columnDefinition = "varchar(32)")
    private String remark;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(length = 11)
    private Integer followers;

    @Column(columnDefinition = "varchar(32)")
    private String createdAt;

    @Column(columnDefinition = "varchar(256)")
    private String website;

    /**
     * 1: 项目追踪用的
     */
    @Column(length = 2)
    private Integer type;
}
