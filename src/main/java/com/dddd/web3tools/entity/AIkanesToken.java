package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "aikanes_token")
public class AIkanesToken {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "token_id", length = 100)
    @Comment("代币唯一标识符，格式为 链ID:代币ID")
    private String tokenId;

    @Column(name = "mint_active")
    @Comment("正在mint是1")
    private Integer mintActive;

    @Column(name = "actual_mint_active")
    @Comment("实际是否在mint 1 有的项目可能会在mintActive=0但是实际在mint")
    private Integer actualMintActive;

    @Column(name = "name", length = 100)
    @Comment("代币名称")
    private String name;

    @Column(name = "symbol", length = 50)
    @Comment("代币符号/简称")
    private String symbol;

    @Column(name = "image", length = 512)
    @Comment("代币图片URL")
    private String image;

    @Column(name = "cap", length = 50)
    @Comment("代币总量上限")
    private String cap;

    @Column(name = "premine", length = 50)
    @Comment("预挖数量")
    private String premine;

    @Column(name = "minted", length = 50)
    @Comment("已挖数量")
    private String minted;

    @Column(name = "mint_amount", length = 50)
    @Comment("每次挖矿数量")
    private String mintAmount;

    @Column(name = "total_supply")
    @Comment("代币总供应量")
    private BigDecimal totalSupply;

    @Column(name = "progress")
    @Comment("挖矿进度百分比(0-100)")
    private Double progress;

    @Column(name = "holders")
    @Comment("当前持有者数量")
    private Integer holders;

    @Column(name = "floor_price")
    @Comment("当前地板价")
    private BigDecimal floorPrice;

    @Column(name = "market_cap", length = 50)
    @Comment("当前市值")
    private String marketCap;

    @Column(name = "price_change24h", length = 20)
    @Comment("24小时价格变化百分比")
    private String priceChange24h;

    @Column(name = "price_change7d", length = 20)
    @Comment("7天价格变化百分比")
    private String priceChange7d;

    @Column(name = "price_change30d", length = 20)
    @Comment("30天价格变化百分比")
    private String priceChange30d;

    @Column(name = "trading_volume24h", length = 50)
    @Comment("24小时交易量")
    private String tradingVolume24h;

    @Column(name = "trading_volume7d", length = 50)
    @Comment("7天交易量")
    private String tradingVolume7d;

    @Column(name = "trading_volume30d", length = 50)
    @Comment("30天交易量")
    private String tradingVolume30d;

    @Column(name = "total_trading_volume", length = 50)
    @Comment("总交易量")
    private String totalTradingVolume;

    @Column(name = "trading_count24h")
    @Comment("24小时交易次数")
    private Integer tradingCount24h;

    @Column(name = "trading_count7d")
    @Comment("7天交易次数")
    private Integer tradingCount7d;

    @Column(name = "trading_count30d")
    @Comment("30天交易次数")
    private Integer tradingCount30d;

    @Column(name = "total_trading_count")
    @Comment("总交易次数")
    private Integer totalTradingCount;

    @Column(name = "data", length = 512)
    @Comment("代币数据URL")
    private String data;

    @Column(name = "reserve_number")
    @Comment("保留编号")
    private Integer reserveNumber;

    @Column(name = "is_nft_collection")
    @Comment("是否为NFT集合")
    private Boolean isNftCollection;

    // mempool相关字段
    @Column(name = "mempool_count")
    @Comment("内存池中待处理的交易数量")
    private Integer mempoolCount;

    @Column(name = "mempool_address_count")
    @Comment("内存池中涉及的地址数量")
    private Integer mempoolAddressCount;

    @Column(name = "mempool_next_block_count")
    @Comment("预计下个区块包含的交易数量")
    private Integer mempoolNextBlockCount;

    @Column(name = "mempool_median_fee_rate", length = 20)
    @Comment("内存池交易的中位费率")
    private String mempoolMedianFeeRate;

    @Column(name = "mempool_fee_rate_range", length = 50)
    @Comment("内存池交易的gas区间")
    private String mempoolFeeRateRange;

    @CreationTimestamp
    @Column(name = "created_time", updatable = false)
    @Comment("记录创建时间")
    private Date createdTime;

    @UpdateTimestamp
    @Column(name = "updated_time")
    @Comment("记录最后更新时间")
    private Date updatedTime;
}