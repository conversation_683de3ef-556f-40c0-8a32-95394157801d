package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 空投项目实体类
 */
@Entity
@Table(name = "projects", indexes = {
    @Index(name = "idx_created_by", columnList = "createdBy"),
    @Index(name = "idx_chain", columnList = "chain"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_start_date", columnList = "startDate"),
    @Index(name = "idx_end_date", columnList = "endDate")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Project {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "chain", nullable = false, length = 50)
    private String chain;

    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    @Column(name = "end_date", nullable = false)
    private LocalDate endDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    @Builder.Default
    private ProjectStatus status = ProjectStatus.active;

    @Column(name = "wallet_count")
    @Builder.Default
    private Integer walletCount = 0;

    @Column(name = "total_value", precision = 20, scale = 8)
    @Builder.Default
    private BigDecimal totalValue = BigDecimal.ZERO;

    @Column(name = "created_by", nullable = false, length = 42)
    private String createdBy;

    @Column(name = "created_at", nullable = false, updatable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at")
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Wallet> wallets;

    @OneToMany(mappedBy = "project", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<TableColumn> tableColumns;

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 项目状态枚举
     */
    public enum ProjectStatus {
        active("活跃"),
        inactive("非活跃"),
        paused("暂停");

        private final String description;

        ProjectStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
