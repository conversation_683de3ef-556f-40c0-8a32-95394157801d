package com.dddd.web3tools.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 钱包代币实体类
 */
@Entity
@Table(name = "wallet_tokens", 
    indexes = {
        @Index(name = "idx_wallet_id", columnList = "wallet_id"),
        @Index(name = "idx_symbol", columnList = "symbol"),
        @Index(name = "idx_balance", columnList = "balance")
    }
)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletToken {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "wallet_id", nullable = false, foreignKey = @ForeignKey(name = "fk_wallet_token_wallet"))
    private UserWallet wallet;

    @Column(name = "symbol", nullable = false, length = 20)
    private String symbol; // ETH, BTC, SOL, etc.

    @Column(name = "name", nullable = false, length = 100)
    private String name; // Ethereum, Bitcoin, Solana, etc.

    @Column(name = "balance", precision = 20, scale = 8)
    @Builder.Default
    private BigDecimal balance = BigDecimal.ZERO;

    @Column(name = "usd_value", precision = 20, scale = 2)
    @Builder.Default
    private BigDecimal usdValue = BigDecimal.ZERO;

    @Column(name = "price", precision = 20, scale = 2)
    @Builder.Default
    private BigDecimal price = BigDecimal.ZERO;

    @Column(name = "change_24h", precision = 10, scale = 2)
    @Builder.Default
    private BigDecimal change24h = BigDecimal.ZERO;

    @Column(name = "contract_address", length = 64)
    private String contractAddress; // 代币合约地址（主币为null）

    @Column(name = "decimals")
    private Integer decimals; // 代币精度

    @Column(name = "created_at", nullable = false, updatable = false)
    @Builder.Default
    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "updated_at")
    @Builder.Default
    private LocalDateTime updatedAt = LocalDateTime.now();

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 获取钱包ID
     */
    public Long getWalletId() {
        return wallet != null ? wallet.getId() : null;
    }

    /**
     * 设置钱包ID
     */
    public void setWalletId(Long walletId) {
        if (walletId != null) {
            this.wallet = UserWallet.builder().id(walletId).build();
        }
    }
}
