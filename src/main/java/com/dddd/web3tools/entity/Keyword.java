package com.dddd.web3tools.entity;

import jakarta.persistence.*;

@Entity
@Table(name = "keywords")
public class Keyword {
    // 新增类型字段
    public static final String TYPE_REQUIRED = "REQUIRED";
    public static final String TYPE_DELETE = "DELETE";
    public static final String TYPE_BASE = "BASE";
    public static final String TYPE_GAS = "GAS";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String word;
    private String type;  // 新增类型字段

    // getters and setters
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }
}