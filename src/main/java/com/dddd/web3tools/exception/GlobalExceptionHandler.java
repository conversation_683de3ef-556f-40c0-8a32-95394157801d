package com.dddd.web3tools.exception;

import com.dddd.web3tools.vo.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.util.stream.Collectors;

/**
 * 全局异常处理器
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        log.warn("参数验证失败: {}", message);
        return ApiResponse.error("VALIDATION_ERROR", "参数验证失败", message);
    }
    
    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleBindException(BindException e) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        log.warn("参数绑定失败: {}", message);
        return ApiResponse.error("VALIDATION_ERROR", "参数绑定失败", message);
    }
    
    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        log.warn("文件上传大小超限: {}", e.getMessage());
        return ApiResponse.error("FILE_TOO_LARGE", "文件大小超过限制（最大5MB）");
    }
    
    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleIllegalArgumentException(IllegalArgumentException e) {
        log.warn("非法参数异常: {}", e.getMessage());

        // 根据异常消息返回相应的错误代码
        String errorCode = "INVALID_ARGUMENT";
        if (e.getMessage().contains("钱包地址")) {
            errorCode = "INVALID_ADDRESS";
        } else if (e.getMessage().contains("区块链")) {
            errorCode = "CHAIN_NOT_SUPPORTED";
        } else if (e.getMessage().contains("已存在")) {
            errorCode = "DUPLICATE_WALLET";
        }

        return ApiResponse.error(errorCode, e.getMessage());
    }

    /**
     * 处理运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleRuntimeException(RuntimeException e) {
        log.error("运行时异常: {}", e.getMessage());

        // 根据异常消息返回相应的错误代码
        String errorCode = "RUNTIME_ERROR";
        if (e.getMessage().contains("用户不存在")) {
            errorCode = "USER_NOT_FOUND";
        } else if (e.getMessage().contains("钱包不存在")) {
            errorCode = "WALLET_NOT_FOUND";
        } else if (e.getMessage().contains("令牌") || e.getMessage().contains("token")) {
            errorCode = "INVALID_TOKEN";
        } else if (e.getMessage().contains("签名")) {
            errorCode = "SIGNATURE_VERIFICATION_FAILED";
        } else if (e.getMessage().contains("权限")) {
            errorCode = "INSUFFICIENT_PERMISSIONS";
        }

        return ApiResponse.error(errorCode, e.getMessage());
    }
    
    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleException(Exception e) {
        log.error("系统异常: ", e);
        return ApiResponse.error("SERVER_ERROR", "系统内部错误", e.getMessage());
    }
}
