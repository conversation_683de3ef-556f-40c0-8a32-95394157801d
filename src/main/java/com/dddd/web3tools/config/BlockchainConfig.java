package com.dddd.web3tools.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.http.HttpService;

import java.util.concurrent.TimeUnit;

@Configuration
@Data
public class BlockchainConfig {

    @Value("${blockchain.ethereum.rpc-url}")
    private String ethereumRpcUrl;

    @Value("${blockchain.bitcoin.api-url}")
    private String bitcoinApiUrl;

    @Bean
    public Web3j web3j() {
        // 创建 HttpService 并设置连接超时和读取超时时间
        HttpService httpService = new HttpService(ethereumRpcUrl);
        return Web3j.build(httpService);
    }

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }
}