package com.dddd.web3tools.config;

import dev.langchain4j.model.googleai.GoogleAiGeminiChatModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Gemini AI配置类
 */
@Configuration
@Slf4j
public class GeminiConfig {

    @Value("${langchain4j.google-ai-gemini.api-key:}")
    private String apiKey;

    @Value("${langchain4j.google-ai-gemini.model-name:gemini-1.5-flash}")
    private String modelName;

    @Value("${langchain4j.google-ai-gemini.temperature:0.7}")
    private Double temperature;

    @Value("${langchain4j.google-ai-gemini.max-tokens:1000}")
    private Integer maxTokens;

    @Bean
    public GoogleAiGeminiChatModel googleAiGeminiChatModel() {
        if (apiKey == null || apiKey.trim().isEmpty() || "YOUR_GEMINI_API_KEY_HERE".equals(apiKey)) {
            log.warn("Gemini API Key未配置或使用默认值，请在application.properties中设置正确的API Key");
            // 返回一个默认配置，但实际使用时会失败
            return GoogleAiGeminiChatModel.builder()
                    .apiKey("dummy-key")
                    .modelName(modelName)
                    .temperature(temperature)
                    .maxOutputTokens(maxTokens)
                    .build();
        }

        log.info("初始化Gemini AI模型: {}", modelName);
        return GoogleAiGeminiChatModel.builder()
                .apiKey(apiKey)
                .modelName(modelName)
                .temperature(temperature)
                .maxOutputTokens(maxTokens)
                .build();
    }
}
