package com.dddd.web3tools.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * 空投项目管理系统配置
 */
@Configuration
@ConfigurationProperties(prefix = "airdrop")
@Data
public class AirdropConfig {

    /**
     * 管理员配置
     */
    private Admin admin = new Admin();

    /**
     * 区块链配置
     */
    private Map<String, BlockchainConfig> blockchains;

    /**
     * 限制配置
     */
    private Limits limits = new Limits();

    /**
     * 同步配置
     */
    private Sync sync = new Sync();

    /**
     * 导出配置
     */
    private Export export = new Export();

    @Data
    public static class Admin {
        private String address = "0x7FC630A70948A8d21cD7C7cFA8f203D7b7e120F2";
        private List<String> addresses;
    }

    @Data
    public static class BlockchainConfig {
        private String name;
        private String rpcUrl;
        private String apiUrl;
        private String apiKey;
        private String nativeToken;
        private String explorerUrl;
        private Integer chainId;
        private boolean enabled = true;
    }

    @Data
    public static class Limits {
        private Integer maxProjectsPerUser = 100;
        private Integer maxWalletsPerProject = 10000;
        private Integer maxBatchImportSize = 1000;
        private Integer maxExportSize = 50000;
        private Integer maxQueryPageSize = 100;
        private Integer defaultPageSize = 20;
    }

    @Data
    public static class Sync {
        private boolean enabled = true;
        private Integer batchSize = 100;
        private Integer delayBetweenRequests = 100; // milliseconds
        private Integer maxRetries = 3;
        private Integer timeoutSeconds = 30;
        private String schedule = "0 0 */6 * * *"; // every 6 hours
    }

    @Data
    public static class Export {
        private boolean enabled = true;
        private List<String> allowedFormats = List.of("json", "csv", "xlsx");
        private String tempDirectory = "/tmp/airdrop-exports";
        private Integer maxFileSize = 100; // MB
        private Integer retentionDays = 7;
    }

    /**
     * 获取区块链配置
     */
    public BlockchainConfig getBlockchainConfig(String chain) {
        if (blockchains == null) {
            return null;
        }
        return blockchains.get(chain.toLowerCase());
    }

    /**
     * 检查区块链是否支持
     */
    public boolean isSupportedChain(String chain) {
        BlockchainConfig config = getBlockchainConfig(chain);
        return config != null && config.isEnabled();
    }

    /**
     * 获取支持的区块链列表
     */
    public List<String> getSupportedChains() {
        if (blockchains == null) {
            return List.of();
        }
        return blockchains.entrySet().stream()
                .filter(entry -> entry.getValue().isEnabled())
                .map(Map.Entry::getKey)
                .toList();
    }

    /**
     * 检查是否是管理员地址
     */
    public boolean isAdminAddress(String address) {
        if (address == null) {
            return false;
        }
        
        // 检查主管理员地址
        if (admin.getAddress() != null && admin.getAddress().equalsIgnoreCase(address)) {
            return true;
        }
        
        // 检查管理员地址列表
        if (admin.getAddresses() != null) {
            return admin.getAddresses().stream()
                    .anyMatch(adminAddr -> adminAddr.equalsIgnoreCase(address));
        }
        
        return false;
    }

    /**
     * 验证批量导入大小
     */
    public boolean isValidBatchSize(int size) {
        return size > 0 && size <= limits.getMaxBatchImportSize();
    }

    /**
     * 验证分页大小
     */
    public boolean isValidPageSize(int size) {
        return size > 0 && size <= limits.getMaxQueryPageSize();
    }

    /**
     * 获取默认分页大小
     */
    public int getDefaultPageSize() {
        return limits.getDefaultPageSize();
    }

    /**
     * 获取最大分页大小
     */
    public int getMaxPageSize() {
        return limits.getMaxQueryPageSize();
    }
}
