package com.dddd.web3tools.config;

import com.dddd.web3tools.entity.TableColumn;
import com.dddd.web3tools.repository.TableColumnRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 空投系统数据初始化器
 */
@Component
@Order(2)
@RequiredArgsConstructor
@Slf4j
public class AirdropDataInitializer implements CommandLineRunner {

    private final TableColumnRepository tableColumnRepository;

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化空投系统数据...");
        
        try {
            initializeDefaultTableColumns();
            log.info("空投系统数据初始化完成");
        } catch (Exception e) {
            log.error("空投系统数据初始化失败", e);
        }
    }

    /**
     * 初始化默认表格列配置
     */
    private void initializeDefaultTableColumns() {
        log.info("初始化默认表格列配置...");

        // 检查是否已存在默认配置
        List<TableColumn> existingColumns = tableColumnRepository.findByProjectIsNullOrderBySortOrder();
        if (!existingColumns.isEmpty()) {
            log.info("默认表格列配置已存在，跳过初始化");
            return;
        }

        // 创建默认列配置
        List<TableColumn> defaultColumns = List.of(
                createDefaultTableColumn("name", "钱包名称", "150px", true, true, 1),
                createDefaultTableColumn("address", "地址", "200px", false, true, 2),
                createDefaultTableColumn("balance", "余额", "100px", false, true, 3),
                createDefaultTableColumn("lastActivity", "最后活动", "120px", false, true, 4),
                createDefaultTableColumn("notes", "备注", "200px", true, true, 5),
                createDefaultTableColumn("addedBy", "添加者", "150px", false, true, 6),
                createDefaultTableColumn("createdAt", "创建时间", "150px", false, true, 7)
        );

        tableColumnRepository.saveAll(defaultColumns);
        log.info("默认表格列配置初始化完成，共创建 {} 个列配置", defaultColumns.size());
    }

    /**
     * 创建默认表格列配置
     */
    private TableColumn createDefaultTableColumn(String key, String label, String width, 
                                               boolean editable, boolean visible, int sortOrder) {
        return TableColumn.builder()
                .project(null) // 默认配置不关联具体项目
                .columnKey(key)
                .label(label)
                .width(width)
                .editable(editable)
                .visible(visible)
                .sortOrder(sortOrder)
                .build();
    }
}
