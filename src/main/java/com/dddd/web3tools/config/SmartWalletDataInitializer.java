package com.dddd.web3tools.config;

import com.dddd.web3tools.entity.SmartWallet;
import com.dddd.web3tools.repository.SmartWalletRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * SmartWallet示例数据初始化
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmartWalletDataInitializer implements CommandLineRunner {

    private final SmartWalletRepository smartWalletRepository;
    private final ObjectMapper objectMapper;

    @Override
    public void run(String... args) throws Exception {
        try {
            initializeSmartWalletData();
            log.info("SmartWallet示例数据初始化完成");
        } catch (Exception e) {
            log.error("SmartWallet示例数据初始化失败", e);
        }
    }

    private void initializeSmartWalletData() {
        // 检查是否已有数据
        if (smartWalletRepository.count() > 0) {
            log.info("SmartWallet数据已存在，跳过初始化");
            return;
        }

        try {
            // 创建示例钱包数据
            SmartWallet wallet1 = SmartWallet.builder()
                .address("DRiP2Pn2K6fuMLKQmt5rZWyHiUZ6WK3GChEySUpHSS4x")
                .nickname("鲸鱼交易员A")
                .winRate(new BigDecimal("85.60"))
                .totalProfit(2580000L)
                .profitPercent(new BigDecimal("340.50"))
                .avgHoldTime(60)
                .recentTrades(45)
                .followers(1250)
                .tags(objectMapper.writeValueAsString(Arrays.asList("DeFi", "Memecoin", "高频")))
                .status("active")
                .lastActive(LocalDateTime.now())
                .totalTrades(156)
                .profitableTrades(134)
                .totalVolume(15680000L)
                .avgProfit(16538L)
                .maxProfit(245000L)
                .maxLoss(-89000L)
                .bestWinStreak(12)
                .currentStreak(3)
                .build();

            SmartWallet wallet2 = SmartWallet.builder()
                .address("9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM")
                .nickname("Alpha猎手")
                .winRate(new BigDecimal("92.10"))
                .totalProfit(4200000L)
                .profitPercent(new BigDecimal("520.30"))
                .avgHoldTime(48)
                .recentTrades(38)
                .followers(890)
                .tags(objectMapper.writeValueAsString(Arrays.asList("Alpha", "早期发现")))
                .status("active")
                .lastActive(LocalDateTime.now())
                .totalTrades(89)
                .profitableTrades(82)
                .totalVolume(12450000L)
                .avgProfit(47191L)
                .maxProfit(380000L)
                .maxLoss(-45000L)
                .bestWinStreak(18)
                .currentStreak(7)
                .build();

            SmartWallet wallet3 = SmartWallet.builder()
                .address("HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH")
                .nickname("稳健投资者")
                .winRate(new BigDecimal("78.30"))
                .totalProfit(1890000L)
                .profitPercent(new BigDecimal("245.80"))
                .avgHoldTime(120)
                .recentTrades(28)
                .followers(567)
                .tags(objectMapper.writeValueAsString(Arrays.asList("稳健", "长期持有")))
                .status("active")
                .lastActive(LocalDateTime.now())
                .totalTrades(234)
                .profitableTrades(183)
                .totalVolume(8900000L)
                .avgProfit(8077L)
                .maxProfit(156000L)
                .maxLoss(-67000L)
                .bestWinStreak(8)
                .currentStreak(-2)
                .build();

            smartWalletRepository.saveAll(Arrays.asList(wallet1, wallet2, wallet3));
            log.info("已创建{}个示例SmartWallet", 3);

        } catch (Exception e) {
            log.error("创建示例SmartWallet数据失败", e);
        }
    }
}
