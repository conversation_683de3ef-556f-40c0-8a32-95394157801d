package com.dddd.web3tools.config;

import com.dddd.web3tools.service.BlogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据初始化器
 */
@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger log = LoggerFactory.getLogger(DataInitializer.class);

    private final BlogService blogService;

    // 构造函数注入
    public DataInitializer(BlogService blogService) {
        this.blogService = blogService;
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("开始初始化博客系统数据...");

        // 初始化默认分类
        blogService.initDefaultCategories();

        log.info("博客系统数据初始化完成");
    }
}
