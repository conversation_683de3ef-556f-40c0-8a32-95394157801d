package com.dddd.web3tools.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Twitter API配置类
 * 支持多个API Key的配置和管理
 */
@Configuration
@ConfigurationProperties(prefix = "twitter.api")
@Data
public class TwitterApiConfig {
    
    /**
     * Twitter API基础URL
     */
    private String url;
    
    /**
     * Twitter搜索API URL
     */
    private String searchUrl;
    
    /**
     * Twitter列表ID
     */
    private String listId;
    
    /**
     * Twitter API主机
     */
    private String host;
    
    /**
     * API Key池配置
     */
    private Pool pool = new Pool();
    
    @Data
    public static class Pool {
        /**
         * API Keys列表
         */
        private List<String> keys;
        
        /**
         * 每个key的最大请求次数，默认1000次
         */
        private int maxRequestsPerKey = 1000;
        
        /**
         * 是否启用key池，默认启用
         */
        private boolean enabled = true;
        
        /**
         * key使用统计重置间隔（小时），默认24小时
         */
        private int resetIntervalHours = 24;
    }
}
