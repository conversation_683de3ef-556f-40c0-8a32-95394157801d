package com.dddd.web3tools.config;

import com.dddd.web3tools.service.TelegramService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.telegram.telegrambots.meta.TelegramBotsApi;
import org.telegram.telegrambots.meta.exceptions.TelegramApiException;
import org.telegram.telegrambots.updatesreceivers.DefaultBotSession;

import jakarta.annotation.PostConstruct;

/**
 * Telegram Bot配置类
 * 用于注册和初始化Telegram Bot
 */
@Slf4j
@Configuration
public class TelegramBotConfig {

    @Autowired
    private TelegramService telegramService;

    @Autowired
    private TelegramConfig telegramConfig;

    @PostConstruct
    public void init() {
        try {
            if (telegramConfig.isEnabled() && 
                telegramConfig.getToken() != null && 
                !telegramConfig.getToken().equals("YOUR_BOT_TOKEN_HERE")) {
                
                TelegramBotsApi botsApi = new TelegramBotsApi(DefaultBotSession.class);
                botsApi.registerBot(telegramService);
                log.info("Telegram Bot注册成功");
            } else {
                log.info("Telegram Bot未启用或配置不完整，跳过注册");
            }
        } catch (TelegramApiException e) {
            log.error("注册Telegram Bot失败", e);
        }
    }
}
