package com.dddd.web3tools.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 钱包缓存配置
 */
@Configuration
@ConfigurationProperties(prefix = "wallet.cache")
@Data
public class WalletCacheConfig {

    /**
     * 余额缓存过期时间（分钟）
     */
    private Integer balanceExpireMinutes = 5;

    /**
     * 价格缓存过期时间（分钟）
     */
    private Integer priceExpireMinutes = 5;

    /**
     * 是否启用缓存
     */
    private Boolean enabled = true;

    /**
     * 缓存key前缀
     */
    private String keyPrefix = "wallet:";

    /**
     * 定时刷新间隔（分钟）
     */
    private Integer refreshIntervalMinutes = 5;

    /**
     * 批量处理大小
     */
    private Integer batchSize = 50;

    /**
     * 请求间隔（毫秒）
     */
    private Integer requestIntervalMs = 100;
}
