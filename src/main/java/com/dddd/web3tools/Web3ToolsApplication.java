package com.dddd.web3tools;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableScheduling  // 启用定时任务
@EntityScan("com.dddd.web3tools.entity") // 指定实体类所在的包
@EnableJpaRepositories("com.dddd.web3tools.repository") // 指定仓库接口所在的包
@EnableJpaAuditing
public class Web3ToolsApplication {
	public static void main(String[] args) {
		SpringApplication.run(Web3ToolsApplication.class, args);
	}
}
