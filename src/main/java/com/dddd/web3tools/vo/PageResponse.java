package com.dddd.web3tools.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 分页响应对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PageResponse<T> {

    private List<T> content;
    private long totalElements;
    private int page;
    private int size;
    private int totalPages;

    // 静态工厂方法
    public static <T> PageResponse<T> fromPage(Page<T> page) {
        return PageResponse.<T>builder()
                .content(page.getContent())
                .totalElements(page.getTotalElements())
                .page(page.getNumber() + 1) // Spring Data JPA页码从0开始，转换为从1开始
                .size(page.getSize())
                .totalPages(page.getTotalPages())
                .build();
    }

    // 兼容旧版本的getter方法
    public List<T> getPosts() {
        return content;
    }

    public long getTotal() {
        return totalElements;
    }
}
