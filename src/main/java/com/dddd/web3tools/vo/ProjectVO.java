package com.dddd.web3tools.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目视图对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProjectVO {

    private Long id;
    private String name;
    private String description;
    private String chain;
    private LocalDate startDate;
    private LocalDate endDate;
    private String status;
    private String statusDescription;
    private Integer walletCount;
    private BigDecimal totalValue;
    private String totalValueFormatted;
    private String createdBy;
    private String createdByShort;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String timeAgo;
    private List<WalletVO> wallets;
    private StatsVO stats;

    /**
     * 项目概览VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OverviewVO {
        private Long id;
        private String name;
        private String chain;
        private String status;
        private Integer walletCount;
        private String totalValue;
        private LocalDateTime createdAt;
        private String timeAgo;
    }

    /**
     * 项目卡片VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CardVO {
        private Long id;
        private String name;
        private String description;
        private String chain;
        private String chainIcon;
        private String status;
        private String statusColor;
        private Integer walletCount;
        private String totalValue;
        private String progress;
        private LocalDate endDate;
        private String daysLeft;
        private LocalDateTime updatedAt;
        private String lastUpdated;
    }

    /**
     * 项目统计VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class StatsVO {
        private Integer totalProjects;
        private Integer activeProjects;
        private Integer pausedProjects;
        private Integer inactiveProjects;
        private Integer totalWallets;
        private String totalValue;
        private String averageValue;
        private List<ChainStatsVO> chainStats;
        private List<StatusStatsVO> statusStats;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class ChainStatsVO {
            private String chain;
            private String chainName;
            private String chainIcon;
            private Integer projectCount;
            private Integer walletCount;
            private String totalValue;
            private String percentage;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class StatusStatsVO {
            private String status;
            private String statusName;
            private String statusColor;
            private Integer count;
            private String percentage;
        }
    }
}
