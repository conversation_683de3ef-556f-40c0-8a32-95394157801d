package com.dddd.web3tools.vo;

import java.util.List;

/**
 * 统计信息视图对象
 */
public class StatsVO {

    private long totalPosts;
    private long totalCategories;
    private List<RecentPostVO> recentPosts;
    private List<CategoryStatsVO> categoryStats;

    // Getter和Setter方法
    public long getTotalPosts() {
        return totalPosts;
    }

    public void setTotalPosts(long totalPosts) {
        this.totalPosts = totalPosts;
    }

    public long getTotalCategories() {
        return totalCategories;
    }

    public void setTotalCategories(long totalCategories) {
        this.totalCategories = totalCategories;
    }

    public List<RecentPostVO> getRecentPosts() {
        return recentPosts;
    }

    public void setRecentPosts(List<RecentPostVO> recentPosts) {
        this.recentPosts = recentPosts;
    }

    public List<CategoryStatsVO> getCategoryStats() {
        return categoryStats;
    }

    public void setCategoryStats(List<CategoryStatsVO> categoryStats) {
        this.categoryStats = categoryStats;
    }

    public static class RecentPostVO {
        private Long id;
        private String title;
        private String createdAt;

        // Getter和Setter方法
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }
    }

    public static class CategoryStatsVO {
        private String categoryId;
        private String categoryName;
        private long postCount;

        // Getter和Setter方法
        public String getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(String categoryId) {
            this.categoryId = categoryId;
        }

        public String getCategoryName() {
            return categoryName;
        }

        public void setCategoryName(String categoryName) {
            this.categoryName = categoryName;
        }

        public long getPostCount() {
            return postCount;
        }

        public void setPostCount(long postCount) {
            this.postCount = postCount;
        }
    }
}
