package com.dddd.web3tools.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 钱包视图对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletVO {

    private Long id;
    private Long projectId;
    private String address;
    private String addressShort;
    private String addressChecksum;
    private String name;
    private String notes;
    private BigDecimal balance;
    private String balanceFormatted;
    private String balanceUsd;
    private LocalDate lastActivity;
    private String lastActivityFormatted;
    private String activityStatus;
    private String addedBy;
    private String addedByShort;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String timeAgo;
    private List<TokenVO> tokens;
    private TransactionStatsVO transactionStats;

    /**
     * 钱包概览VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OverviewVO {
        private String address;
        private String addressShort;
        private String name;
        private String balance;
        private String balanceUsd;
        private String lastActivity;
        private String activityStatus;
    }

    /**
     * 钱包卡片VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CardVO {
        private Long id;
        private String address;
        private String addressShort;
        private String name;
        private String balance;
        private String balanceUsd;
        private String balanceChange24h;
        private String balanceChangePercent;
        private String lastActivity;
        private String activityStatus;
        private Integer transactionCount;
        private String riskLevel;
        private List<String> tags;
    }

    /**
     * 代币VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TokenVO {
        private String address;
        private String symbol;
        private String name;
        private String logo;
        private BigDecimal balance;
        private String balanceFormatted;
        private Integer decimals;
        private String price;
        private String priceChange24h;
        private String usdValue;
        private String percentage;
    }

    /**
     * 交易统计VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TransactionStatsVO {
        private Integer totalTransactions;
        private Integer transactionsLast7Days;
        private Integer transactionsLast30Days;
        private String totalVolume;
        private String volumeLast7Days;
        private String volumeLast30Days;
        private String averageGasPrice;
        private String totalGasFees;
        private LocalDateTime firstTransaction;
        private LocalDateTime lastTransaction;
    }

    /**
     * 钱包活动VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ActivityVO {
        private String type;
        private String description;
        private String hash;
        private String from;
        private String to;
        private String value;
        private String token;
        private LocalDateTime timestamp;
        private String timeAgo;
        private String status;
        private String gasUsed;
        private String gasFee;
    }

    /**
     * 钱包风险评估VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class RiskAssessmentVO {
        private String riskLevel; // LOW, MEDIUM, HIGH
        private String riskScore; // 0-100
        private List<RiskFactorVO> riskFactors;
        private List<String> recommendations;
        private LocalDateTime lastAssessment;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        @Builder
        public static class RiskFactorVO {
            private String factor;
            private String description;
            private String severity;
            private String impact;
        }
    }

    /**
     * 钱包标签VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TagVO {
        private String name;
        private String color;
        private String description;
        private String category;
        private LocalDateTime createdAt;
    }

    /**
     * 钱包分组VO
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class GroupVO {
        private Long id;
        private String name;
        private String description;
        private String color;
        private Integer walletCount;
        private String totalValue;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }
}
