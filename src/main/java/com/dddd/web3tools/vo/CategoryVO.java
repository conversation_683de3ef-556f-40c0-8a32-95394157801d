package com.dddd.web3tools.vo;

import com.dddd.web3tools.entity.Category;

import java.time.LocalDateTime;

/**
 * 分类视图对象
 */
public class CategoryVO {

    private String id;
    private String name;
    private String icon;
    private String color;
    private LocalDateTime createdAt;
    private Long postCount;

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public Long getPostCount() {
        return postCount;
    }

    public void setPostCount(Long postCount) {
        this.postCount = postCount;
    }

    // 静态工厂方法
    public static CategoryVO fromEntity(Category category, Long postCount) {
        CategoryVO vo = new CategoryVO();
        vo.setId(category.getId());
        vo.setName(category.getName());
        vo.setIcon(category.getIcon());
        vo.setColor(category.getColor());
        vo.setCreatedAt(category.getCreatedAt());
        vo.setPostCount(postCount);
        return vo;
    }
}
