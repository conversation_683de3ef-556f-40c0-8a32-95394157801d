package com.dddd.web3tools.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.encrypt.Encryptors;
import org.springframework.security.crypto.encrypt.TextEncryptor;
import org.springframework.stereotype.Component;

/**
 * 加密工具类
 * 用于加密存储私钥等敏感信息
 */
@Component
@Slf4j
public class CryptoUtil {

    @Value("${app.crypto.password:SmartWalletTracker2024}")
    private String password;

    @Value("${app.crypto.salt:deadbeef}")
    private String salt;

    private TextEncryptor textEncryptor;

    private TextEncryptor getEncryptor() {
        if (textEncryptor == null) {
            textEncryptor = Encryptors.text(password, salt);
        }
        return textEncryptor;
    }

    /**
     * 加密文本
     */
    public String encrypt(String plainText) {
        try {
            if (plainText == null || plainText.isEmpty()) {
                return plainText;
            }
            return getEncryptor().encrypt(plainText);
        } catch (Exception e) {
            log.error("加密失败", e);
            throw new RuntimeException("加密失败: " + e.getMessage());
        }
    }

    /**
     * 解密文本
     */
    public String decrypt(String encryptedText) {
        try {
            if (encryptedText == null || encryptedText.isEmpty()) {
                return encryptedText;
            }
            return getEncryptor().decrypt(encryptedText);
        } catch (Exception e) {
            log.error("解密失败", e);
            throw new RuntimeException("解密失败: " + e.getMessage());
        }
    }

    /**
     * 加密私钥
     */
    public String encryptPrivateKey(String privateKey) {
        return encrypt(privateKey);
    }

    /**
     * 解密私钥
     */
    public String decryptPrivateKey(String encryptedPrivateKey) {
        return decrypt(encryptedPrivateKey);
    }
}
