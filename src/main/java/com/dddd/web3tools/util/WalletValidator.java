package com.dddd.web3tools.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.web3j.crypto.Keys;
import org.web3j.utils.Numeric;

import java.util.regex.Pattern;

/**
 * 钱包地址验证工具类
 */
@Component
@Slf4j
public class WalletValidator {

    // 以太坊地址正则表达式
    private static final Pattern ETH_ADDRESS_PATTERN = Pattern.compile("^0x[a-fA-F0-9]{40}$");
    
    // 比特币地址正则表达式（简化版）
    private static final Pattern BTC_ADDRESS_PATTERN = Pattern.compile("^[13][a-km-zA-HJ-NP-Z1-9]{25,34}$|^bc1[a-z0-9]{39,59}$");

    /**
     * 验证钱包地址是否有效
     * 
     * @param address 钱包地址
     * @return 是否有效
     */
    public boolean isValidAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }

        address = address.trim();

        // 验证以太坊地址格式
        if (isValidEthereumAddress(address)) {
            return true;
        }

        // 验证比特币地址格式
        if (isValidBitcoinAddress(address)) {
            return true;
        }

        return false;
    }

    /**
     * 验证以太坊地址是否有效
     * 
     * @param address 以太坊地址
     * @return 是否有效
     */
    public boolean isValidEthereumAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }

        address = address.trim();

        // 检查基本格式
        if (!ETH_ADDRESS_PATTERN.matcher(address).matches()) {
            return false;
        }

        try {
            // 使用Web3j验证地址格式
            String cleanAddress = Numeric.cleanHexPrefix(address);
            if (cleanAddress.length() != 40) {
                return false;
            }

            // 验证checksum（如果地址包含大小写混合）
            if (hasUpperAndLowerCase(address)) {
                return isValidChecksum(address);
            }

            return true;
        } catch (Exception e) {
            log.debug("验证以太坊地址失败: {}", address, e);
            return false;
        }
    }

    /**
     * 验证比特币地址是否有效
     * 
     * @param address 比特币地址
     * @return 是否有效
     */
    public boolean isValidBitcoinAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }

        address = address.trim();

        // 检查基本格式
        return BTC_ADDRESS_PATTERN.matcher(address).matches();
    }

    /**
     * 将地址转换为checksum格式
     * 
     * @param address 原始地址
     * @return checksum地址
     */
    public String toChecksumAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return address;
        }

        address = address.trim();

        // 只处理以太坊地址
        if (!ETH_ADDRESS_PATTERN.matcher(address).matches()) {
            return address;
        }

        try {
            return Keys.toChecksumAddress(address);
        } catch (Exception e) {
            log.debug("转换checksum地址失败: {}", address, e);
            return address;
        }
    }

    /**
     * 验证地址的checksum是否正确
     * 
     * @param address 地址
     * @return 是否正确
     */
    public boolean isValidChecksum(String address) {
        if (address == null || address.trim().isEmpty()) {
            return false;
        }

        try {
            String checksumAddress = Keys.toChecksumAddress(address);
            return address.equals(checksumAddress);
        } catch (Exception e) {
            log.debug("验证checksum失败: {}", address, e);
            return false;
        }
    }

    /**
     * 检查地址是否包含大小写混合
     * 
     * @param address 地址
     * @return 是否包含大小写混合
     */
    private boolean hasUpperAndLowerCase(String address) {
        if (address == null || address.length() < 3) {
            return false;
        }

        // 跳过0x前缀
        String hexPart = address.substring(2);
        boolean hasUpper = false;
        boolean hasLower = false;

        for (char c : hexPart.toCharArray()) {
            if (Character.isUpperCase(c)) {
                hasUpper = true;
            } else if (Character.isLowerCase(c)) {
                hasLower = true;
            }

            if (hasUpper && hasLower) {
                return true;
            }
        }

        return false;
    }

    /**
     * 标准化地址格式
     * 
     * @param address 原始地址
     * @return 标准化地址
     */
    public String normalizeAddress(String address) {
        if (address == null || address.trim().isEmpty()) {
            return address;
        }

        address = address.trim();

        // 以太坊地址转换为checksum格式
        if (isValidEthereumAddress(address)) {
            return toChecksumAddress(address);
        }

        // 其他地址保持原样
        return address;
    }

    /**
     * 获取地址类型
     * 
     * @param address 地址
     * @return 地址类型
     */
    public String getAddressType(String address) {
        if (address == null || address.trim().isEmpty()) {
            return "unknown";
        }

        address = address.trim();

        if (isValidEthereumAddress(address)) {
            return "ethereum";
        }

        if (isValidBitcoinAddress(address)) {
            return "bitcoin";
        }

        return "unknown";
    }

    /**
     * 验证地址是否属于指定的区块链
     * 
     * @param address 地址
     * @param chain 区块链类型
     * @return 是否匹配
     */
    public boolean isValidForChain(String address, String chain) {
        if (address == null || chain == null) {
            return false;
        }

        String addressType = getAddressType(address);
        
        switch (chain.toLowerCase()) {
            case "ethereum":
            case "arbitrum":
            case "polygon":
            case "bsc":
            case "avalanche":
            case "fantom":
            case "optimism":
            case "base":
                return "ethereum".equals(addressType);
            case "bitcoin":
                return "bitcoin".equals(addressType);
            default:
                return false;
        }
    }

    /**
     * 缩短地址显示
     * 
     * @param address 完整地址
     * @param prefixLength 前缀长度
     * @param suffixLength 后缀长度
     * @return 缩短的地址
     */
    public String shortenAddress(String address, int prefixLength, int suffixLength) {
        if (address == null || address.length() <= prefixLength + suffixLength) {
            return address;
        }

        return address.substring(0, prefixLength) + "..." + 
               address.substring(address.length() - suffixLength);
    }

    /**
     * 缩短地址显示（默认格式）
     * 
     * @param address 完整地址
     * @return 缩短的地址（0x1234...5678格式）
     */
    public String shortenAddress(String address) {
        return shortenAddress(address, 6, 4);
    }
}
