package com.dddd.web3tools.util;

import com.dddd.web3tools.config.TwitterApiConfig;
import com.dddd.web3tools.service.TwitterApiKeyPoolService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

@Slf4j
@Component
public class TwitterAPIUtil {

    public static final HttpClient HTTP_CLIENT = HttpClient.newHttpClient();

    @Autowired
    private TwitterApiKeyPoolService keyPoolService;

    @Autowired
    private TwitterApiConfig twitterApiConfig;

    // 保持向后兼容的静态变量（已废弃）
    @Deprecated
    public static final String apiKey = "7aa357b972msh4eee09d21178af4p1b40a4jsnff9e6245cb7c";
    @Deprecated
    public static final String apiHost = "twitter-api45.p.rapidapi.com";

    /**
     * 构建URL（静态方法，保持向后兼容）
     */
    public static String buildUrl(String baseUrl, String... params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl).append("?");
        for (int i = 0; i < params.length; i += 2) {
            if (i > 0) urlBuilder.append("&");
            urlBuilder.append(params[i]).append("=").append(params[i + 1]);
        }
        return urlBuilder.toString();
    }

    /**
     * 发送HTTP请求（使用key池，带重试机制）
     */
    public String sendHttpRequestWithPool(String url) throws Exception {
        return sendHttpRequestWithPoolAndRetry(url, 3); // 最多重试3次
    }

    /**
     * 发送HTTP请求（使用key池，带重试机制）
     * @param url 请求URL
     * @param maxRetries 最大重试次数
     * @return 响应内容
     * @throws Exception 当所有重试都失败时抛出异常
     */
    public String sendHttpRequestWithPoolAndRetry(String url, int maxRetries) throws Exception {
        String currentApiKey = null;
        Exception lastException = null;

        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                // 获取当前可用的API Key
                if (attempt == 0) {
                    currentApiKey = keyPoolService.getCurrentApiKey();
                } else {
                    // 重试时获取下一个可用的key
                    currentApiKey = keyPoolService.getNextAvailableKey(currentApiKey);
                }

                if (currentApiKey == null) {
                    log.warn("第{}次尝试：无可用的API Key，使用默认key", attempt + 1);
                    currentApiKey = apiKey;
                }

                String host = twitterApiConfig.getHost();
                if (host == null) {
                    host = apiHost;
                }

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(url))
                        .header("x-rapidapi-key", currentApiKey)
                        .header("x-rapidapi-host", host)
                        .GET()
                        .build();

                HttpResponse<String> httpResponse = HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString());
                String response = httpResponse.body();

                // 检查响应状态码
                if (httpResponse.statusCode() == 200) {
                    // 成功，记录API Key使用次数
                    keyPoolService.recordKeyUsage(currentApiKey);
                    log.debug("API请求成功，使用key: {}", keyPoolService.maskApiKey(currentApiKey));
                    return response;
                } else if (httpResponse.statusCode() == 429) {
                    // 速率限制，标记当前key为失败并重试
                    String errorMsg = "API速率限制 (HTTP " + httpResponse.statusCode() + ")";
                    log.warn("第{}次尝试失败: {} - Key: {}", attempt + 1, errorMsg, keyPoolService.maskApiKey(currentApiKey));
                    keyPoolService.markKeyAsFailed(currentApiKey, errorMsg);
                    lastException = new Exception(errorMsg + ": " + response);
                } else if (httpResponse.statusCode() == 401 || httpResponse.statusCode() == 403) {
                    // 认证失败，标记当前key为失败并重试
                    String errorMsg = "API认证失败 (HTTP " + httpResponse.statusCode() + ")";
                    log.warn("第{}次尝试失败: {} - Key: {}", attempt + 1, errorMsg, keyPoolService.maskApiKey(currentApiKey));
                    keyPoolService.markKeyAsFailed(currentApiKey, errorMsg);
                    lastException = new Exception(errorMsg + ": " + response);
                } else {
                    // 其他错误，记录使用次数但标记为失败
                    String errorMsg = "API请求失败 (HTTP " + httpResponse.statusCode() + ")";
                    log.warn("第{}次尝试失败: {} - Key: {}", attempt + 1, errorMsg, keyPoolService.maskApiKey(currentApiKey));
                    keyPoolService.recordKeyUsage(currentApiKey);
                    keyPoolService.markKeyAsFailed(currentApiKey, errorMsg);
                    lastException = new Exception(errorMsg + ": " + response);
                }

            } catch (Exception e) {
                String errorMsg = "网络请求异常: " + e.getMessage();
                log.warn("第{}次尝试失败: {} - Key: {}", attempt + 1, errorMsg, keyPoolService.maskApiKey(currentApiKey));

                if (currentApiKey != null) {
                    keyPoolService.markKeyAsFailed(currentApiKey, errorMsg);
                }
                lastException = e;
            }

            // 如果不是最后一次尝试，等待一段时间再重试
            if (attempt < maxRetries) {
                try {
                    Thread.sleep(1000 * (attempt + 1)); // 递增等待时间：1s, 2s, 3s...
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new Exception("请求被中断", ie);
                }
            }
        }

        // 所有重试都失败了
        String finalErrorMsg = String.format("API请求失败，已重试%d次，最后错误: %s",
                maxRetries, lastException != null ? lastException.getMessage() : "未知错误");
        log.error(finalErrorMsg);
        throw new Exception(finalErrorMsg, lastException);
    }

    /**
     * 发送HTTP请求（静态方法，保持向后兼容，已废弃）
     */
    @Deprecated
    public static String sendHttpRequest(String url) throws Exception {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("x-rapidapi-key", apiKey)
                .header("x-rapidapi-host", apiHost)
                .GET()
                .build();

        return HTTP_CLIENT.send(request, HttpResponse.BodyHandlers.ofString()).body();
    }

    /**
     * 获取Twitter列表用户（使用key池）
     */
    public JsonNode getTwitterListUsers(String listId) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/list_members.php", "list_id", listId);
        String response = sendHttpRequestWithPool(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * 获取用户推文（使用key池）
     */
    public JsonNode getUserTweets(String username) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/timeline.php",
                            "screenname", username);
        String response = sendHttpRequestWithPool(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * 根据用户名获取用户信息（使用key池）
     */
    public JsonNode getUserByUsername(String username) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/screenname.php",
                "screenname", username);
        String response = sendHttpRequestWithPool(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * 根据列表ID获取Twitter列表（使用key池）
     */
    public JsonNode getTwitterListByListId(String listId) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/listtimeline.php",
                "list_id", listId);
        String response = sendHttpRequestWithPool(url);
        return new ObjectMapper().readTree(response);
    }

    // ========== 静态方法（保持向后兼容，已废弃） ==========

    /**
     * @deprecated 请使用实例方法 getTwitterListUsers(String listId)
     */
    @Deprecated
    public static JsonNode getTwitterListUsersStatic(String listId) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/list_members.php", "list_id", listId);
        String response = sendHttpRequest(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * @deprecated 请使用实例方法 getUserTweets(String username)
     */
    @Deprecated
    public static JsonNode getUserTweetsStatic(String username) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/timeline.php",
                            "screenname", username);
        String response = sendHttpRequest(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * @deprecated 请使用实例方法 getUserByUsername(String username)
     */
    @Deprecated
    public static JsonNode getUserByUsernameStatic(String username) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/screenname.php",
                "screenname", username);
        String response = sendHttpRequest(url);
        return new ObjectMapper().readTree(response);
    }

    /**
     * @deprecated 请使用实例方法 getTwitterListByListId(String listId)
     */
    @Deprecated
    public static JsonNode getTwitterListByListIdStatic(String listId) throws Exception {
        String url = buildUrl("https://twitter-api45.p.rapidapi.com/listtimeline.php",
                "list_id", listId);
        String response = sendHttpRequest(url);
        return new ObjectMapper().readTree(response);
    }

}
