package com.dddd.web3tools.service;

import com.dddd.web3tools.dto.ProjectDTO;
import com.dddd.web3tools.dto.WalletDTO;
import com.dddd.web3tools.entity.Project;
import com.dddd.web3tools.repository.ProjectRepository;
import com.dddd.web3tools.repository.WalletRepository;
import com.dddd.web3tools.service.impl.AirdropServiceImpl;
import com.dddd.web3tools.util.WalletValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 空投服务测试类
 */
@ExtendWith(MockitoExtension.class)
class AirdropServiceTest {

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private WalletRepository walletRepository;

    @Mock
    private WalletValidator walletValidator;

    @InjectMocks
    private AirdropServiceImpl airdropService;

    private ProjectDTO.CreateRequest createProjectRequest;
    private Project mockProject;

    @BeforeEach
    void setUp() {
        createProjectRequest = ProjectDTO.CreateRequest.builder()
                .name("测试项目")
                .description("这是一个测试项目")
                .chain("ethereum")
                .startDate(LocalDate.now())
                .endDate(LocalDate.now().plusDays(30))
                .status("active")
                .createdBy("******************************************")
                .build();

        mockProject = Project.builder()
                .id(1L)
                .name("测试项目")
                .description("这是一个测试项目")
                .chain("ethereum")
                .startDate(LocalDate.now())
                .endDate(LocalDate.now().plusDays(30))
                .status(Project.ProjectStatus.active)
                .createdBy("******************************************")
                .build();
    }

    @Test
    void testCreateProject_Success() {
        // Given
        when(projectRepository.existsByCreatedByAndNameIgnoreCase(anyString(), anyString())).thenReturn(false);
        when(projectRepository.save(any(Project.class))).thenReturn(mockProject);

        // When
        ProjectDTO result = airdropService.createProject(createProjectRequest);

        // Then
        assertNotNull(result);
        assertEquals("测试项目", result.getName());
        assertEquals("ethereum", result.getChain());
        assertEquals("active", result.getStatus());
        verify(projectRepository).save(any(Project.class));
    }

    @Test
    void testCreateProject_DuplicateName() {
        // Given
        when(projectRepository.existsByCreatedByAndNameIgnoreCase(anyString(), anyString())).thenReturn(true);

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> airdropService.createProject(createProjectRequest)
        );
        assertEquals("项目名称已存在", exception.getMessage());
        verify(projectRepository, never()).save(any(Project.class));
    }

    @Test
    void testCreateProject_InvalidDateRange() {
        // Given
        createProjectRequest.setEndDate(LocalDate.now().minusDays(1)); // 结束日期早于开始日期
        when(projectRepository.existsByCreatedByAndNameIgnoreCase(anyString(), anyString())).thenReturn(false);

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> airdropService.createProject(createProjectRequest)
        );
        assertEquals("结束日期不能早于开始日期", exception.getMessage());
        verify(projectRepository, never()).save(any(Project.class));
    }

    @Test
    void testGetProjectById_Success() {
        // Given
        when(projectRepository.findById(1L)).thenReturn(Optional.of(mockProject));

        // When
        ProjectDTO result = airdropService.getProjectById(1L);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("测试项目", result.getName());
    }

    @Test
    void testGetProjectById_NotFound() {
        // Given
        when(projectRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> airdropService.getProjectById(1L)
        );
        assertEquals("项目不存在", exception.getMessage());
    }

    @Test
    void testValidateWalletAddress_Valid() {
        // Given
        WalletDTO.ValidationRequest request = WalletDTO.ValidationRequest.builder()
                .address("******************************************")
                .chain("ethereum")
                .build();

        when(walletValidator.isValidAddress(anyString())).thenReturn(true);
        when(walletValidator.toChecksumAddress(anyString())).thenReturn("******************************************");

        // When
        WalletDTO.ValidationResponse result = airdropService.validateWalletAddress(request);

        // Then
        assertNotNull(result);
        assertTrue(result.getIsValid());
        assertEquals("ethereum", result.getFormat());
        assertEquals("******************************************", result.getChecksumAddress());
    }

    @Test
    void testValidateWalletAddress_Invalid() {
        // Given
        WalletDTO.ValidationRequest request = WalletDTO.ValidationRequest.builder()
                .address("invalid_address")
                .chain("ethereum")
                .build();

        when(walletValidator.isValidAddress(anyString())).thenReturn(false);

        // When
        WalletDTO.ValidationResponse result = airdropService.validateWalletAddress(request);

        // Then
        assertNotNull(result);
        assertFalse(result.getIsValid());
        assertEquals("ethereum", result.getFormat());
        assertEquals("invalid_address", result.getChecksumAddress());
    }

    @Test
    void testIsAdmin_True() {
        // When
        boolean result = airdropService.isAdmin("******************************************");

        // Then
        assertTrue(result);
    }

    @Test
    void testIsAdmin_False() {
        // When
        boolean result = airdropService.isAdmin("******************************************");

        // Then
        assertFalse(result);
    }

    @Test
    void testHasProjectAccess_Admin() {
        // When
        boolean result = airdropService.hasProjectAccess(1L, "******************************************");

        // Then
        assertTrue(result);
    }

    @Test
    void testHasProjectAccess_Owner() {
        // Given
        when(projectRepository.findByIdAndCreatedBy(1L, "******************************************"))
                .thenReturn(Optional.of(mockProject));

        // When
        boolean result = airdropService.hasProjectAccess(1L, "******************************************");

        // Then
        assertTrue(result);
    }

    @Test
    void testHasProjectAccess_NoAccess() {
        // Given
        when(projectRepository.findByIdAndCreatedBy(1L, "******************************************"))
                .thenReturn(Optional.empty());

        // When
        boolean result = airdropService.hasProjectAccess(1L, "******************************************");

        // Then
        assertFalse(result);
    }
}
