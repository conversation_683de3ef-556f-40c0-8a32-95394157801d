package com.dddd.web3tools.service;

import com.dddd.web3tools.config.TwitterApiConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class TwitterApiKeyPoolServiceTest {

    @Mock
    private TwitterApiConfig twitterApiConfig;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @InjectMocks
    private TwitterApiKeyPoolService keyPoolService;

    private TwitterApiConfig.Pool poolConfig;

    @BeforeEach
    void setUp() {
        poolConfig = new TwitterApiConfig.Pool();
        poolConfig.setEnabled(true);
        poolConfig.setMaxRequestsPerKey(1000);
        poolConfig.setResetIntervalHours(24);

        List<String> keys = Arrays.asList(
            "key1_test",
            "key2_test",
            "key3_test"
        );
        poolConfig.setKeys(keys);

        lenient().when(twitterApiConfig.getPool()).thenReturn(poolConfig);
        lenient().when(redisTemplate.opsForValue()).thenReturn(valueOperations);
    }

    @Test
    void testGetCurrentApiKey_ReturnsFirstKey() {
        // Given
        lenient().when(valueOperations.get("twitter:api:current_index")).thenReturn(null);
        lenient().when(valueOperations.get("twitter:api:key:key1_test")).thenReturn(null);

        // When
        String currentKey = keyPoolService.getCurrentApiKey();

        // Then
        assertEquals("key1_test", currentKey);
    }

    @Test
    void testRecordKeyUsage_IncrementsCounter() {
        // Given
        String apiKey = "key1_test";
        when(valueOperations.increment("twitter:api:key:" + apiKey)).thenReturn(1L);

        // When
        keyPoolService.recordKeyUsage(apiKey);

        // Then
        verify(valueOperations).increment("twitter:api:key:" + apiKey);
        verify(redisTemplate).expire(eq("twitter:api:key:" + apiKey), any());
    }

    @Test
    void testGetKeyUsageCount_ReturnsCorrectCount() {
        // Given
        String apiKey = "key1_test";
        when(valueOperations.get("twitter:api:key:" + apiKey)).thenReturn(500);

        // When
        int count = keyPoolService.getKeyUsageCount(apiKey);

        // Then
        assertEquals(500, count);
    }

    @Test
    void testGetKeyUsageCount_ReturnsZeroWhenNull() {
        // Given
        String apiKey = "key1_test";
        when(valueOperations.get("twitter:api:key:" + apiKey)).thenReturn(null);

        // When
        int count = keyPoolService.getKeyUsageCount(apiKey);

        // Then
        assertEquals(0, count);
    }

    @Test
    void testGetPoolStatus_ReturnsFormattedStatus() {
        // Given
        lenient().when(valueOperations.get("twitter:api:current_index")).thenReturn(0);
        lenient().when(valueOperations.get("twitter:api:key:key1_test")).thenReturn(245);
        lenient().when(valueOperations.get("twitter:api:key:key2_test")).thenReturn(0);
        lenient().when(valueOperations.get("twitter:api:key:key3_test")).thenReturn(0);

        // When
        String status = keyPoolService.getPoolStatus();

        // Then
        assertTrue(status.contains("Twitter API Key池状态"));
        assertTrue(status.contains("key1****test"));
        assertTrue(status.contains("245/1000"));
        assertTrue(status.contains("[当前]"));
    }

    @Test
    void testGetCurrentApiKey_WhenPoolDisabled_ReturnsNull() {
        // Given
        poolConfig.setEnabled(false);

        // When
        String currentKey = keyPoolService.getCurrentApiKey();

        // Then
        assertNull(currentKey);
    }

    @Test
    void testGetCurrentApiKey_WhenNoKeys_ReturnsNull() {
        // Given
        poolConfig.setKeys(null);

        // When
        String currentKey = keyPoolService.getCurrentApiKey();

        // Then
        assertNull(currentKey);
    }

    @Test
    void testMarkKeyAsFailed_ShouldMarkKeyAsUnavailable() {
        // Given
        String testKey = "key1_test";
        String reason = "API rate limit";

        // When
        keyPoolService.markKeyAsFailed(testKey, reason);

        // Then
        verify(valueOperations).set(eq("twitter:api:failed:" + testKey), eq(reason), any());
    }

    @Test
    void testGetNextAvailableKey_WhenCurrentKeyFails_ShouldReturnNextKey() {
        // Given
        String failedKey = "key1_test";
        lenient().when(valueOperations.get(anyString())).thenReturn(null); // 没有使用记录
        lenient().when(redisTemplate.hasKey(anyString())).thenReturn(false); // 没有失败记录

        // When
        String result = keyPoolService.getNextAvailableKey(failedKey);

        // Then
        assertNotNull(result);
        assertNotEquals(failedKey, result);
        // 验证失败的key被标记
        verify(valueOperations).set(eq("twitter:api:failed:" + failedKey), eq("API调用失败"), any());
    }

    @Test
    void testClearKeyFailureStatus_ShouldRemoveFailureRecord() {
        // Given
        String testKey = "key1_test";

        // When
        keyPoolService.clearKeyFailureStatus(testKey);

        // Then
        verify(redisTemplate).delete("twitter:api:failed:" + testKey);
    }

    @Test
    void testMaskApiKey_ShouldMaskKeyProperly() {
        // Given
        String testKey = "abcdefghijklmnop";

        // When
        String result = keyPoolService.maskApiKey(testKey);

        // Then
        assertEquals("abcd****mnop", result);
    }

    @Test
    void testMaskApiKey_WhenKeyTooShort_ShouldReturnMask() {
        // Given
        String shortKey = "abc";

        // When
        String result = keyPoolService.maskApiKey(shortKey);

        // Then
        assertEquals("****", result);
    }

    @Test
    void testResetAllKeyUsage_ShouldClearAllRecords() {
        // When
        keyPoolService.resetAllKeyUsage();

        // Then
        List<String> keys = poolConfig.getKeys();
        for (String key : keys) {
            verify(redisTemplate).delete("twitter:api:key:" + key);
            verify(redisTemplate).delete("twitter:api:failed:" + key);
        }
    }
}
